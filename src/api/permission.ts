import { http } from "@/utils/http";
import type { ApiListResponse, ApiResponse, ListParams } from "./types";

// Permission相关类型定义
export type PermissionInfo = {
  id: string;
  name: string;
  group: string;
  method: string;
  path: string;
  backup?: string;
};

export type PermissionCreateRequest = {
  name: string;
  group: string;
  method: string;
  path: string;
  backup?: string;
};

export type PermissionUpdateRequest = {
  id: string;
  name?: string;
  group?: string;
  method?: string;
  path?: string;
  backup?: string;
};

// 使用通用的ListParams类型
export type PermissionListParams = ListParams;

// 根据角色获取权限的参数类型
export type PermissionRoleParams = {
  role: string;
};

// Permission管理CRUD方法
/** 获取权限列表 */
export const getPermissionList = (params: PermissionListParams) => {
  return http.post<ApiListResponse<PermissionInfo>, PermissionListParams>(
    "/api/permission/list",
    params
  );
};

/** 根据角色获取权限 */
export const getPermissionByRole = (params: PermissionRoleParams) => {
  return http.post<ApiResponse<PermissionInfo[]>, PermissionRoleParams>(
    "/api/permission/role",
    params
  );
};

/** 创建权限 */
export const createPermission = (data: PermissionCreateRequest) => {
  return http.post<ApiResponse<string>, PermissionCreateRequest>("/api/permission", data);
};

/** 更新权限 */
export const updatePermission = (data: PermissionUpdateRequest) => {
  return http.request<ApiResponse<string>>("put", "/api/permission", {
    data
  });
};

/** 删除权限 */
export const deletePermission = (id: string) => {
  return http.request<ApiResponse<string>>("delete", `/api/permission/${id}`);
};

/** 获取单个权限信息 */
export const getPermission = (id: string) => {
  return http.get<ApiResponse<PermissionInfo>, any>(`/api/permission/${id}`);
};
