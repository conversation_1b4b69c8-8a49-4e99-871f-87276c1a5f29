// 测试后端菜单数据转换
import { getAsyncRoutes } from "./routes";

// 模拟后端返回的数据
const mockBackendData = {
  "code": 200,
  "data": [
    {
      "id": "menu:19jte1tgkwaxzvvzzatl",
      "name": "warehouseDetail",
      "order": 1,
      "path": "/warehouse/detail",
      "component": "pages/warehouse/detail",
      "redirect": "",
      "active": "yes",
      "title": "WarehouseDetail",
      "icon": "group",
      "keep_alive": "yes",
      "hidden": "yes",
      "is_link": "no",
      "parent": "WarehouseList",
      "remark": "仓库详情",
      "children": null
    },
    {
      "id": "menu:ty827cgunxhnq25qnnx8",
      "name": "contractList",
      "order": 1,
      "path": "/contract/list",
      "component": "pages/contract/index",
      "redirect": "",
      "active": "yes",
      "title": "ContractList",
      "icon": "group",
      "keep_alive": "no",
      "hidden": "no",
      "is_link": "no",
      "parent": "''",
      "remark": "服务订单列表",
      "children": null
    },
    {
      "id": "menu:91nu9ijvxsq9zlbpyotr",
      "name": "warehouseList",
      "order": 6,
      "path": "/warehouse/list",
      "component": "pages/warehouse/index",
      "redirect": "",
      "active": "yes",
      "title": "WarehouseList",
      "icon": "group",
      "keep_alive": "no",
      "hidden": "no",
      "is_link": "no",
      "parent": "''",
      "remark": "仓库列表",
      "children": null
    }
  ],
  "msg": "success"
};

// 测试函数
export function testDataTransformation() {
  console.log("原始后端数据:", mockBackendData);
  
  // 这里应该会调用转换函数
  // 实际使用时会通过 getAsyncRoutes() 调用
  console.log("转换后的数据将通过 getAsyncRoutes() 获取");
  
  // 预期的转换结果应该是：
  // 1. contractList 和 warehouseList 作为根节点（parent为空或''）
  // 2. warehouseDetail 作为 warehouseList 的子节点
  // 3. 按 order 排序
  // 4. hidden="yes" 的项目 showLink=false
  // 5. keep_alive="yes" 的项目 keepAlive=true
}

// 预期的转换结果示例
const expectedResult = [
  {
    "path": "/contract/list",
    "name": "contractList",
    "component": "pages/contract/index",
    "meta": {
      "title": "ContractList",
      "icon": "group",
      "rank": 1,
      "showLink": true,
      "keepAlive": false,
      "roles": ["admin", "common"]
    }
  },
  {
    "path": "/warehouse/list",
    "name": "warehouseList",
    "component": "pages/warehouse/index",
    "meta": {
      "title": "WarehouseList",
      "icon": "group",
      "rank": 6,
      "showLink": true,
      "keepAlive": false,
      "roles": ["admin", "common"]
    },
    "children": [
      {
        "path": "/warehouse/detail",
        "name": "warehouseDetail",
        "component": "pages/warehouse/detail",
        "meta": {
          "title": "WarehouseDetail",
          "icon": "group",
          "rank": 1,
          "showLink": false, // hidden="yes"
          "keepAlive": true,  // keep_alive="yes"
          "roles": ["admin", "common"]
        }
      }
    ]
  }
];

console.log("预期转换结果:", expectedResult);
