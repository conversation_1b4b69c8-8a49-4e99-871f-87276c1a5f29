import { http } from "@/utils/http";
import type { ApiListResponse, ApiResponse, ListParams } from "./types";

// Menu相关类型定义
export type MenuInfo = {
  id: string;
  name: string;
  order: number;
  path?: string;
  component?: string;
  redirect?: string;
  active?: string;
  title?: string;
  icon?: string;
  keep_alive?: string;
  hidden?: string;
  is_link?: string;
  parent?: string;
  remark?: string;
  children?: MenuInfo[];
};

export type MenuCreateRequest = {
  name: string;
  order: number;
  path?: string;
  component?: string;
  redirect?: string;
  active?: string;
  title?: string;
  icon?: string;
  keep_alive?: string;
  hidden?: string;
  is_link?: string;
  parent?: string;
  remark?: string;
};

export type MenuUpdateRequest = {
  id: string;
  name?: string;
  order?: number;
  path?: string;
  component?: string;
  redirect?: string;
  active?: string;
  title?: string;
  icon?: string;
  keep_alive?: string;
  hidden?: string;
  is_link?: string;
  parent?: string;
  remark?: string;
};

// 使用通用的ListParams类型
export type MenuListParams = ListParams;

// Menu管理CRUD方法
/** 获取菜单列表 */
export const getMenuList = (params: MenuListParams) => {
  return http.post<ApiListResponse<MenuInfo>, MenuListParams>(
    "/api/menu/list",
    params
  );
};

/** 创建菜单 */
export const createMenu = (data: MenuCreateRequest) => {
  return http.post<ApiResponse<string>, MenuCreateRequest>("/api/menu", data);
};

/** 更新菜单 */
export const updateMenu = (data: MenuUpdateRequest) => {
  return http.request<ApiResponse<string>>("put", "/api/menu", {
    data
  });
};

/** 删除菜单 */
export const deleteMenu = (id: string) => {
  return http.request<ApiResponse<string>>("delete", `/api/menu/${id}`);
};

/** 获取单个菜单信息 */
export const getMenu = (id: string) => {
  return http.get<ApiResponse<MenuInfo>, any>(`/api/menu/${id}`);
};
