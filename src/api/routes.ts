import { http } from "@/utils/http";

type Result = {
  code: number;
  data: Array<any>;
  msg: string;
};

// 后端菜单数据类型
interface BackendMenuItem {
  id: string;
  name: string;
  order: number;
  path: string;
  component: string;
  redirect: string;
  active: string;
  title: string;
  icon: string;
  keep_alive: string;
  hidden: string;
  is_link: string;
  parent: string;
  remark: string;
  children: BackendMenuItem[] | null;
}

// 统一的路由数据类型（符合项目标准）
interface RouteConfigsTable {
  path: string;
  name?: string;
  component?: string;
  redirect?: string;
  meta: {
    title: string;
    icon?: string;
    rank?: number;
    showLink?: boolean;
    showParent?: boolean;
    keepAlive?: boolean;
    roles?: string[];
  };
  children?: RouteConfigsTable[];
}

/**
 * 将后端菜单数据转换为前端路由格式
 */
function transformBackendMenuToRoutes(
  menuData: BackendMenuItem[]
): RouteConfigsTable[] {
  if (!menuData || menuData.length === 0) return [];

  // 首先构建父子关系映射
  const menuMap = new Map<string, BackendMenuItem>();
  const rootMenus: BackendMenuItem[] = [];

  // 第一遍遍历：建立映射和找出根节点
  menuData.forEach(item => {
    menuMap.set(item.name, item);
    // 处理各种空值情况：空字符串、"''"、null、undefined
    const parentValue = item.parent?.trim();
    if (
      !parentValue ||
      parentValue === "''" ||
      parentValue === '""' ||
      parentValue === "null"
    ) {
      rootMenus.push(item);
    }
  });

  // 第二遍遍历：构建树形结构
  menuData.forEach(item => {
    const parentValue = item.parent?.trim();
    if (
      parentValue &&
      parentValue !== "''" &&
      parentValue !== '""' &&
      parentValue !== "null"
    ) {
      const parent = menuMap.get(parentValue);
      if (parent) {
        if (!parent.children) {
          parent.children = [];
        }
        parent.children.push(item);
      }
    }
  });

  // 转换为前端路由格式
  function convertToRoute(
    item: BackendMenuItem,
    parentItem?: BackendMenuItem
  ): RouteConfigsTable {
    const route: RouteConfigsTable = {
      path: item.path,
      name: item.name,
      meta: {
        title: item.title,
        icon: item.icon || undefined,
        rank: item.order,
        showLink: item.hidden !== "yes", // hidden为"yes"时不显示链接
        keepAlive: item.keep_alive === "yes",
        roles: ["admin", "common"] // 默认角色，可根据实际需求调整
      }
    };

    // 处理 showParent 逻辑
    // 只有在父菜单有自己的组件且只有一个子菜单时才设置 showParent
    if (
      parentItem &&
      parentItem.children &&
      parentItem.children.length === 1 &&
      parentItem.component
    ) {
      route.meta.showParent = true;
    }

    // 设置组件路径
    // 如果有component字段，就设置组件路径（无论是否有子路由）
    if (item.component) {
      // 将后端的component路径转换为符合项目结构的路径
      // 例如：pages/user/index -> /src/views/user/index.vue
      route.component = item.component;
    }

    // 设置重定向逻辑
    // 只有后端明确设置了redirect字段时才添加重定向
    // if (item.redirect && item.redirect.trim() !== "") {
    //   route.redirect = item.redirect;
    // }
    // 不再自动添加重定向逻辑

    // 处理子路由
    if (item.children && item.children.length > 0) {
      // 按order排序
      item.children.sort((a, b) => a.order - b.order);
      route.children = item.children.map(child => convertToRoute(child, item));
    }

    return route;
  }

  // 转换根节点并按order排序
  const routes = rootMenus
    .sort((a, b) => a.order - b.order)
    .map(item => convertToRoute(item));

  return routes;
}

export const getAsyncRoutes = async () => {
  try {
    const response = await http.request<Result>("post", "/api/menu/current");
    if (response.code === 200) {
      const transformedData = transformBackendMenuToRoutes(response.data);
      return {
        ...response,
        data: transformedData
      };
    }
    return response;
  } catch (error) {
    console.error("获取菜单数据失败:", error);
    return {
      code: 500,
      data: [],
      msg: "获取菜单数据失败"
    };
  }
};
