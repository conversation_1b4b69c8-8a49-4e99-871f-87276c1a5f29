// 通用API响应类型定义

/**
 * 通用API响应类型
 * @template T 响应数据的类型
 */
export type ApiResponse<T> = {
  code: number;
  data: T;
  msg: string;
};

/**
 * 通用列表响应类型（匹配后端的ListResponse<T>）
 * @template T 列表项的类型
 */
export type ListResponse<T> = {
  data: T[];
  total: number;
  page: number;
  size: number;
};

/**
 * 通用列表API响应类型
 * @template T 列表项的类型
 */
export type ApiListResponse<T> = ApiResponse<ListResponse<T>>;

/**
 * 通用列表查询参数类型
 */
export type ListParams = {
  options: {
    order_by: string;
    desc: boolean;
  };
  page: {
    page: number;
    limit: number;
  };
  params?: Array<{
    var: string;
    val: string;
  }>;
};

/**
 * 列表选项类型
 */
export type ListOptions = {
  order_by: string;
  desc: boolean;
};

/**
 * Where条件类型
 */
export type WhereOptions = {
  var: string;
  val: string;
  operator?: string; // 操作符，如 "=", "!=", ">", "<", "like" 等
};

/**
 * 分页类型
 */
export type Page = {
  page: number;
  limit: number;
};

/**
 * 灵活参数类型（对应后端FlexibleParams）
 */
export type FlexibleParams = {
  options?: ListOptions;
  id?: IdParams;
  params?: WhereOptions[];
  page?: Page;
};

/**
 * 更新选项类型
 */
export type UpdateOptions = {
  field: string;
  value: string;
};

/**
 * 更新字段类型
 */
export type UpdateFields = {
  id: string;
  update_fields: UpdateOptions[];
};

/**
 * 通用ID参数类型
 */
export type IdParams = {
  id: string;
};

/**
 * 通用创建参数类型
 * @template T 创建数据的类型
 */
export type CreateParams<T> = {
  data: T;
};

/**
 * 通用更新参数类型
 * @template T 更新数据的类型
 */
export type UpdateParams<T> = {
  data: T;
};

// 导出常用的响应类型别名
export type StringResponse = ApiResponse<string>;
export type BooleanResponse = ApiResponse<boolean>;
export type NumberResponse = ApiResponse<number>;
