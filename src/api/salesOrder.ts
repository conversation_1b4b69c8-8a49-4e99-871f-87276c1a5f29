import { http, postFormDataAction } from "@/utils/http";
import type { ApiListResponse, ApiResponse, ListParams } from "./types";

// SalesOrder相关类型定义
export type SalesOrderInfo = {
  id: string;
  status?: string;
  creator_id?: string;
  updater_id?: string;
  serial: string;
  contract_id?: string;
  repayment_id?: string;
  // 导入批次
  import_record?: string;
  purchase_time?: string;
  pay_time?: string;
  pay_type?: string;
  pay_info?: string;
  customer?: string;
  receive_phone?: string;
  customer_phone?: string;
  address?: string;
  express_type?: string;
  express_company?: string;
  express_order?: string;
  platform_name?: string;
  platform_serial?: string;
  platform_order_serial?: string;
  platform_fee_total: number;
  amount: number;
  express_fee: number;
  total_payment: number;
  // 发货时间
  delivery_time?: string;
  // 签收时间
  sign_time?: string;
  // 订单完成时间
  complete_time?: string;
  created_at: number;
  updated_at: number;
};

export type SalesOrderCreateRequest = {
  status?: string;
  serial?: string;
  contract_id?: string;
  repayment_id?: string;
  import_record?: string;
  purchase_time?: string;
  pay_time?: string;
  pay_type?: string;
  pay_info?: string;
  customer?: string;
  receive_phone?: string;
  customer_phone?: string;
  address?: string;
  express_type?: string;
  express_company?: string;
  express_order?: string;
  platform_name?: string;
  platform_serial?: string;
  platform_order_serial?: string;
  platform_fee_total?: number;
  amount?: number;
  express_fee?: number;
  total_payment?: number;
  delivery_time?: string;
  sign_time?: string;
  complete_time?: string;
};

export type SalesOrderUpdateRequest = {
  id: string;
  status?: string;
  serial?: string;
  contract_id?: string;
  repayment_id?: string;
  import_record?: string;
  purchase_time?: string;
  pay_time?: string;
  pay_type?: string;
  pay_info?: string;
  customer?: string;
  receive_phone?: string;
  customer_phone?: string;
  address?: string;
  express_type?: string;
  express_company?: string;
  express_order?: string;
  platform_name?: string;
  platform_serial?: string;
  platform_order_serial?: string;
  platform_fee_total?: number;
  amount?: number;
  express_fee?: number;
  total_payment?: number;
  delivery_time?: string;
  sign_time?: string;
  complete_time?: string;
};

// 使用通用的ListParams类型
export type SalesOrderListParams = ListParams;

// SalesOrder管理CRUD方法
/** 获取销售订单列表 */
export const getSalesOrderList = (params: SalesOrderListParams) => {
  return http.post<ApiListResponse<SalesOrderInfo>, SalesOrderListParams>(
    "/api/sales_order/list",
    params
  );
};

/** 创建销售订单 */
export const createSalesOrder = (data: SalesOrderCreateRequest) => {
  return http.post<ApiResponse<string>, SalesOrderCreateRequest>(
    "/api/sales_order",
    data
  );
};

/** 更新销售订单 */
export const updateSalesOrder = (data: SalesOrderUpdateRequest) => {
  return http.request<ApiResponse<string>>("put", "/api/sales_order", {
    data
  });
};

/** 删除销售订单 */
export const deleteSalesOrder = (id: string) => {
  return http.request<ApiResponse<string>>("delete", `/api/sales_order/${id}`);
};

/** 获取单个销售订单信息 */
export const getSalesOrder = (id: string) => {
  return http.get<ApiResponse<SalesOrderInfo>, any>(`/api/sales_order/${id}`);
};

/** 导入销售订单 */
export const importSalesOrder = (formData: FormData) => {
  return postFormDataAction<string>("/api/sales_order/import", formData);
};

// 订单支付统计相关类型定义
export type PaymentCountRequest = {
  id: string; // 合约ID
};

export type PaymentCountResponse = {
  total_amount: number; // 合同订单总金额
  paid_amount: number; // 已绑定金额
  unpay_amount: number; // 未绑定金额
};

/** 统计订单支付情况 */
export const countOrderPayment = (data: PaymentCountRequest) => {
  return http.post<ApiResponse<PaymentCountResponse>, PaymentCountRequest>(
    "/api/sales_order/count_payment",
    data
  );
};

// 订单状态字典
export const orderStatusDict = [
  { value: "draft", label: "草稿", color: "grey" },
  { value: "pending", label: "待确认", color: "blue" },
  { value: "confirmed", label: "已确认", color: "green" },
  { value: "processing", label: "处理中", color: "orange" },
  { value: "completed", label: "已完成", color: "teal" },
  { value: "cancelled", label: "已取消", color: "red" }
];

// 支付状态字典
export const paymentStatusDict = [
  { value: "unpaid", label: "未支付", color: "red" },
  { value: "partial", label: "部分支付", color: "orange" },
  { value: "paid", label: "已支付", color: "green" },
  { value: "refunded", label: "已退款", color: "grey" }
];

// 发货状态字典
export const deliveryStatusDict = [
  { value: "pending", label: "待发货", color: "blue" },
  { value: "partial", label: "部分发货", color: "orange" },
  { value: "shipped", label: "已发货", color: "green" },
  { value: "delivered", label: "已送达", color: "teal" },
  { value: "returned", label: "已退货", color: "red" }
];

// 获取状态颜色
export const getOrderStatusColor = (status: string) => {
  const statusItem = orderStatusDict.find(item => item.value === status);
  return statusItem ? statusItem.color : "grey";
};

// 获取状态文本
export const getOrderStatusText = (status: string) => {
  const statusItem = orderStatusDict.find(item => item.value === status);
  return statusItem ? statusItem.label : status || "未知";
};

// 获取支付状态颜色
export const getPaymentStatusColor = (status: string) => {
  const statusItem = paymentStatusDict.find(item => item.value === status);
  return statusItem ? statusItem.color : "grey";
};

// 获取支付状态文本
export const getPaymentStatusText = (status: string) => {
  const statusItem = paymentStatusDict.find(item => item.value === status);
  return statusItem ? statusItem.label : status || "未知";
};

// 获取发货状态颜色
export const getDeliveryStatusColor = (status: string) => {
  const statusItem = deliveryStatusDict.find(item => item.value === status);
  return statusItem ? statusItem.color : "grey";
};

// 获取发货状态文本
export const getDeliveryStatusText = (status: string) => {
  const statusItem = deliveryStatusDict.find(item => item.value === status);
  return statusItem ? statusItem.label : status || "未知";
};
