import { http } from "@/utils/http";
import type { ApiListResponse, ApiResponse, ListParams } from "./types";

// Contract相关类型定义
export type ContractInfo = {
  id: string;
  name: string;
  serial: string;
  applier: string;
  applier_id?: string;
  contract_type: string;
  begin_time: string;
  end_time: string;
  funder: string;
  funder_id?: string;
  scf_company?: string;
  scf_company_id?: string;
  status: string;
  application_quota?: number;
  confirm_quota?: number;
  used_quota?: number;
  desc?: string;
  sign_time?: string;
  supplier_id?: string;
  supplier_name?: string;
  bid_winner_id?: string;
  bid_winner_name?: string;
  bid_owner_id?: string;
  bid_owner_name?: string;
  warehouse_id?: string;
  warehouse_name?: string;
  profit_calc_fee?: number;
  penalty_calc_fee?: number;
  profit_calc_period?: string;
  penalty_calc_period?: string;
  product_category?: string[];
  created_at?: string;
  updated_at?: string;
  remark?: string;
};

export type ContractCreateRequest = {
  name: string;
  serial?: string;
  applier?: string;
  applier_id?: string;
  contract_type?: string;
  begin_time?: string;
  end_time?: string;
  funder?: string;
  funder_id?: string;
  scf_company?: string;
  scf_company_id?: string;
  status?: string;
  application_quota?: number;
  confirm_quota?: number;
  used_quota?: number;
  desc?: string;
  sign_time?: string;
  supplier_id?: string;
  supplier_name?: string;
  bid_winner_id?: string;
  bid_winner_name?: string;
  bid_owner_id?: string;
  bid_owner_name?: string;
  warehouse_id?: string;
  warehouse_name?: string;
  profit_calc_fee?: number;
  penalty_calc_fee?: number;
  profit_calc_period?: string;
  penalty_calc_period?: string;
  product_category?: string[];
  remark?: string;
};

export type ContractUpdateRequest = {
  id: string;
  name?: string;
  serial?: string;
  applier?: string;
  applier_id?: string;
  contract_type?: string;
  begin_time?: string;
  end_time?: string;
  funder?: string;
  funder_id?: string;
  scf_company?: string;
  scf_company_id?: string;
  status?: string;
  application_quota?: number;
  confirm_quota?: number;
  used_quota?: number;
  desc?: string;
  sign_time?: string;
  supplier_id?: string;
  supplier_name?: string;
  bid_winner_id?: string;
  bid_winner_name?: string;
  bid_owner_id?: string;
  bid_owner_name?: string;
  warehouse_id?: string;
  warehouse_name?: string;
  profit_calc_fee?: number;
  penalty_calc_fee?: number;
  profit_calc_period?: string;
  penalty_calc_period?: string;
  product_category?: string[];
  remark?: string;
};

// 使用通用的ListParams类型
export type ContractListParams = ListParams;

// Contract管理CRUD方法
/** 获取合约列表 */
export const getContractList = (params: ContractListParams) => {
  return http.post<ApiListResponse<ContractInfo>, ContractListParams>(
    "/api/financial_contract/list",
    params
  );
};

/** 创建合约 */
export const createContract = (data: ContractCreateRequest) => {
  return http.post<ApiResponse<string>, ContractCreateRequest>(
    "/api/financial_contract",
    data
  );
};

/** 更新合约 */
export const updateContract = (data: ContractUpdateRequest) => {
  return http.request<ApiResponse<string>>("put", "/api/financial_contract", {
    data
  });
};

/** 删除合约 */
export const deleteContract = (id: string) => {
  return http.request<ApiResponse<string>>(
    "delete",
    `/api/financial_contract/${id}`
  );
};

/** 获取单个合约信息 */
export const getContract = (id: string) => {
  return http.get<ApiResponse<ContractInfo>, any>(
    `/api/financial_contract/${id}`
  );
};

// 合约审核相关类型定义
export type ContractReviewRequest = {
  confirm: boolean; // true为审核通过，false为审核拒绝
  remark?: string; // 审核备注
};

/** 审核合约 */
export const reviewContract = (id: string, data: ContractReviewRequest) => {
  return http.post<ApiResponse<string>, ContractReviewRequest>(
    `/api/financial_contract/${id}`,
    data
  );
};

// 合约状态字典
export const contractStatusDict = [
  { value: "draft", label: "草稿", color: "grey" },
  { value: "new", label: "待审核", color: "blue" },
  { value: "processing", label: "已审核", color: "green" },
  { value: "done", label: "已完成", color: "teal" },
  { value: "expired", label: "已过期", color: "red" }
];

// 获取状态颜色
export const getContractStatusColor = (status: string) => {
  const statusItem = contractStatusDict.find(item => item.value === status);
  return statusItem ? statusItem.color : "grey";
};

// 获取状态文本
export const getContractStatusText = (status: string) => {
  const statusItem = contractStatusDict.find(item => item.value === status);
  return statusItem ? statusItem.label : status || "未知";
};
