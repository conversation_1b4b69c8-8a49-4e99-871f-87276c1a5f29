import { http } from "@/utils/http";

// 示例：如何使用基于环境变量的代理配置

// 1. 需要认证的API接口
export const loginApi = (data: any) => {
  // 开发环境：根据 VITE_API_PROXY_PREFIX 自动添加前缀
  // 如 VITE_API_PROXY_PREFIX=/server 时：/api/login -> /server/api/login -> 代理到后端
  // 生产环境：直接请求 /api/login
  return http.post("/api/login", data);
};

export const getUserInfo = () => {
  // 开发环境：根据环境变量自动处理代理前缀
  // 生产环境：直接请求 /api/user/info
  return http.get("/api/user/info");
};

// 2. 公共接口（不需要token）
export const getCaptcha = () => {
  // 开发环境：根据环境变量自动处理代理前缀
  // 生产环境：直接请求 /public/captcha
  return http.get("/public/captcha");
};

export const getPublicConfig = () => {
  // 开发环境：根据环境变量自动处理代理前缀
  // 生产环境：直接请求 /public/config
  return http.get("/public/config");
};

// 配置说明：
// .env.development: VITE_API_PROXY_PREFIX=/server, VITE_API_TARGET=http://127.0.0.1:6310
// .env.production: VITE_API_PROXY_PREFIX=（空）, VITE_API_TARGET=（空）
// 这样可以灵活配置不同环境的代理设置
