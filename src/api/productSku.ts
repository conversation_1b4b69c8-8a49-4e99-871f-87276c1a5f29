import { http } from "@/utils/http";
import type { ApiListResponse, ApiResponse, ListParams } from "./types";

// UploadFileResponse 类型定义
export type UploadFileResponse = {
  id: string;
  filename: string;
  url: string;
  size: number;
  mime_type: string;
};

// ProductSku相关类型定义
export type ProductSkuInfo = {
  id: string;
  sort: number;
  status: string;
  serial: string;
  product_serial?: string;
  external_serial?: string;
  product_type?: string;
  sales_price: number;
  market_price: number;
  cost_price: number;
  name?: string;
  brand?: string;
  barcode?: string;
  model?: string;
  specification?: string;
  size?: string;
  package?: string;
  unit?: string;
  source_area?: string;
  desc?: string;
  content?: string;
  supplier_id?: string;
  created_at?: number;
  updated_at?: number;
  image?: UploadFileResponse[];
  attachment?: UploadFileResponse[];
};

export type ProductSkuCreateRequest = {
  sort: number;
  status: string;
  serial: string;
  product_serial?: string;
  external_serial?: string;
  product_type?: string;
  sales_price: number;
  market_price: number;
  cost_price: number;
  name?: string;
  brand?: string;
  barcode?: string;
  model?: string;
  specification?: string;
  size?: string;
  package?: string;
  unit?: string;
  source_area?: string;
  desc?: string;
  content?: string;
  supplier_id?: string;
};

export type ProductSkuUpdateRequest = {
  id: string;
  sort: number;
  status: string;
  serial: string;
  product_serial?: string;
  external_serial?: string;
  product_type?: string;
  sales_price: number;
  market_price: number;
  cost_price: number;
  name?: string;
  brand?: string;
  barcode?: string;
  model?: string;
  specification?: string;
  size?: string;
  package?: string;
  unit?: string;
  source_area?: string;
  desc?: string;
  content?: string;
  supplier_id?: string;
};

// 使用通用的ListParams类型
export type ProductSkuListParams = ListParams;

// ProductSku管理CRUD方法
/** 获取产品SKU列表 */
export const getProductSkuList = (params: ProductSkuListParams) => {
  return http.post<ApiListResponse<ProductSkuInfo>, ProductSkuListParams>(
    "/api/product_sku/list",
    params
  );
};

/** 创建产品SKU */
export const createProductSku = (data: ProductSkuCreateRequest) => {
  return http.post<ApiResponse<string>, ProductSkuCreateRequest>(
    "/api/product_sku",
    data
  );
};

/** 更新产品SKU */
export const updateProductSku = (data: ProductSkuUpdateRequest) => {
  return http.request<ApiResponse<string>>("put", "/api/product_sku", {
    data
  });
};

/** 删除产品SKU */
export const deleteProductSku = (id: string) => {
  return http.request<ApiResponse<string>>("delete", `/api/product_sku/${id}`);
};

/** 获取单个产品SKU信息 */
export const getProductSku = (id: string) => {
  return http.get<ApiResponse<ProductSkuInfo>, any>(`/api/product_sku/${id}`);
};

/** 创建草稿产品SKU */
export const draftProductSku = () => {
  return http.post<ApiResponse<string>, any>("/api/product_sku/draft", {});
};

/** 发布产品SKU */
export const publishProductSku = (data: ProductSkuUpdateRequest) => {
  return http.post<ApiResponse<string>, ProductSkuUpdateRequest>(
    "/api/product_sku/publish",
    data
  );
};
