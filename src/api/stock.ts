import { http } from "@/utils/http";
import type { ApiListResponse, ApiResponse, ListParams } from "./types";

// Stock相关类型定义
export type StockInfo = {
  id: string;
  sku_serial: string;
  warehouse: string;
  position: string;
  company?: string;
  quantity: number;
  location?: string[];
  remark?: string;
  created_at: number;
  updated_at: number;
};

export type StockCreateRequest = {
  sku_serial: string;
  warehouse: string;
  position: string;
  company?: string;
  quantity: number;
  location?: string[];
  remark?: string;
};

export type StockUpdateRequest = {
  id: string;
  sku_serial: string;
  warehouse: string;
  position: string;
  company?: string;
  quantity: number;
  location?: string[];
  remark?: string;
};

// 使用通用的ListParams类型
export type StockListParams = ListParams;

// Stock管理CRUD方法
/** 获取库存列表 */
export const getStockList = (params: StockListParams) => {
  return http.post<ApiListResponse<StockInfo>, StockListParams>(
    "/api/stock/list",
    params
  );
};

/** 创建库存 */
export const createStock = (data: StockCreateRequest) => {
  return http.post<ApiResponse<string>, StockCreateRequest>(
    "/api/stock",
    data
  );
};

/** 更新库存 */
export const updateStock = (data: StockUpdateRequest) => {
  return http.request<ApiResponse<string>>("put", "/api/stock", {
    data
  });
};

/** 删除库存 */
export const deleteStock = (id: string) => {
  return http.request<ApiResponse<string>>("delete", `/api/stock/${id}`);
};

/** 获取单个库存信息 */
export const getStock = (id: string) => {
  return http.get<ApiResponse<StockInfo>, any>(`/api/stock/${id}`);
};
