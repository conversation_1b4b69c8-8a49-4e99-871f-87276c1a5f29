import { http, postFormDataAction } from "@/utils/http";
import type { responseData } from "@/utils/http/types.d";
import type { ApiListResponse, ApiResponse, ListParams } from "./types";

// ContractLog相关类型定义（基于后端 ContractLogResponse 结构）
export type ContractLogInfo = {
  id: string;
  parent_id: string; // 合同ID
  log_type: string; // 日志类型，审核、状态变更等
  log_value?: string; // 日志记录值，根据类型来做解析
  log_date?: string; // 额外补充日期
  log_status?: string; // 日志状态，通过、拒绝、完成、确认等
  remark?: string; // 操作备注
  creater_id?: string; // 操作人ID
  creater_name?: string; // 操作人姓名
  created_at: number; // 创建时间（时间戳）
  updated_at: number; // 更新时间（时间戳）
};

export type ContractLogCreateRequest = {
  parent_id: string; // 合同ID
  log_type: string; // 日志类型
  log_value?: string; // 日志记录值
  log_date?: string; // 额外补充日期
  log_status?: string; // 日志状态
  remark?: string; // 操作备注
  creater_id?: string; // 操作人ID
  creater_name?: string; // 操作人姓名
};

export type ContractLogUpdateRequest = {
  id: string;
  parent_id?: string;
  log_type?: string;
  log_value?: string;
  log_date?: string;
  log_status?: string;
  remark?: string;
  creater_id?: string;
  creater_name?: string;
};

// 使用通用的ListParams类型
export type ContractLogListParams = ListParams;

// ContractLog管理CRUD方法
/** 获取合同日志列表 */
export const getContractLogList = (params: ContractLogListParams) => {
  return http.post<ApiListResponse<ContractLogInfo>, ContractLogListParams>(
    "/api/contract_log/list",
    params
  );
};

/** 创建合同日志 */
export const createContractLog = (data: ContractLogCreateRequest) => {
  return http.post<ApiResponse<string>, ContractLogCreateRequest>(
    "/api/contract_log",
    data
  );
};

/** 创建合同日志（带文件上传） */
export const createContractLogWithFile = (
  formData: FormData
): Promise<responseData<string>> => {
  return postFormDataAction<string>("/api/contract_log", formData);
};

/** 更新合同日志 */
export const updateContractLog = (data: ContractLogUpdateRequest) => {
  return http.request<ApiResponse<string>>("put", "/api/contract_log", {
    data
  });
};

/** 删除合同日志 */
export const deleteContractLog = (id: string) => {
  return http.request<ApiResponse<string>>("delete", `/api/contract_log/${id}`);
};

/** 获取单个合同日志信息 */
export const getContractLog = (id: string) => {
  return http.get<ApiResponse<ContractLogInfo>, any>(`/api/contract_log/${id}`);
};

// 合同日志类型字典
export const contractLogTypeDict = [
  { value: "UPLOAD", label: "文件上传", color: "blue" },
  { value: "REVIEW", label: "审核", color: "green" },
  { value: "STATUS_CHANGE", label: "状态变更", color: "orange" },
  { value: "EDIT", label: "编辑", color: "purple" },
  { value: "DELETE", label: "删除", color: "red" },
  { value: "OTHER", label: "其他", color: "grey" }
];

// 合同日志状态字典
export const contractLogStatusDict = [
  { value: "approved", label: "通过", color: "green" },
  { value: "rejected", label: "拒绝", color: "red" },
  { value: "pending", label: "待处理", color: "orange" },
  { value: "completed", label: "完成", color: "teal" },
  { value: "confirmed", label: "确认", color: "blue" },
  { value: "cancelled", label: "取消", color: "grey" }
];

// 获取日志类型颜色
export const getContractLogTypeColor = (type: string) => {
  const typeItem = contractLogTypeDict.find(item => item.value === type);
  return typeItem ? typeItem.color : "grey";
};

// 获取日志类型文本
export const getContractLogTypeText = (type: string) => {
  const typeItem = contractLogTypeDict.find(item => item.value === type);
  return typeItem ? typeItem.label : type || "未知";
};

// 获取日志状态颜色
export const getContractLogStatusColor = (status: string) => {
  const statusItem = contractLogStatusDict.find(item => item.value === status);
  return statusItem ? statusItem.color : "grey";
};

// 获取日志状态文本
export const getContractLogStatusText = (status: string) => {
  const statusItem = contractLogStatusDict.find(item => item.value === status);
  return statusItem ? statusItem.label : status || "未知";
};

// 格式化时间戳
export const formatContractLogTime = (timestamp: number): string => {
  if (!timestamp) return "";
  const date = new Date(timestamp);
  return date.toLocaleString("zh-CN", {
    year: "numeric",
    month: "2-digit",
    day: "2-digit",
    hour: "2-digit",
    minute: "2-digit",
    second: "2-digit"
  });
};
