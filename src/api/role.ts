import { http } from "@/utils/http";
import type { ApiListResponse, ApiResponse, ListParams } from "./types";

// Role相关类型定义
export type RoleInfo = {
  id: string;
  name: string;
  code: string;
  order: number;
  stable?: string;
  desc?: string;
  remark?: string;
};

export type RoleCreateRequest = {
  name: string;
  code: string;
  order: number;
  stable?: string;
  desc?: string;
  remark?: string;
};

export type RoleUpdateRequest = {
  id: string;
  name?: string;
  code?: string;
  order?: number;
  stable?: string;
  desc?: string;
  remark?: string;
};

// 角色菜单关联类型
export type RoleMenuRelateRequest = {
  role_id: string;
  menu_ids: string[];
};

// 角色权限关联类型
export type RolePermissionRelateRequest = {
  role_id: string;
  permission_ids: string[];
};

// 使用通用的ListParams类型
export type RoleListParams = ListParams;

// 角色列表响应类型
export type RoleListResult = ApiListResponse<RoleInfo>;

// Role管理CRUD方法
/** 获取角色列表 */
export const getRoleList = (params: RoleListParams) => {
  return http.post<RoleListResult, RoleListParams>("/api/role/list", params);
};

/** 创建角色 */
export const createRole = (data: RoleCreateRequest) => {
  return http.post<ApiResponse<string>, RoleCreateRequest>("/api/role", data);
};

/** 更新角色 */
export const updateRole = (data: RoleUpdateRequest) => {
  return http.request<ApiResponse<string>>("put", "/api/role", {
    data
  });
};

/** 删除角色 */
export const deleteRole = (id: string) => {
  return http.request<ApiResponse<string>>("delete", `/api/role/${id}`);
};

/** 获取单个角色信息 */
export const getRole = (id: string) => {
  return http.get<ApiResponse<RoleInfo>, any>(`/api/role/${id}`);
};

/** 角色绑定菜单 */
export const roleRelateMenu = (data: RoleMenuRelateRequest) => {
  return http.post<ApiResponse<string>, RoleMenuRelateRequest>(
    "/api/role/relate/menu",
    data
  );
};

/** 角色绑定权限 */
export const roleRelatePermission = (data: RolePermissionRelateRequest) => {
  return http.post<ApiResponse<string>, RolePermissionRelateRequest>(
    "/api/role/relate/permission",
    data
  );
};
