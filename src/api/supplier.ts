import { http } from "@/utils/http";
import type { ApiListResponse, ApiResponse } from "./types";

// Supplier相关类型定义
export type SupplierInfo = {
  id: string;
  serial: string;
  name: string;
  address?: string;
  website?: string;
  status: "on" | "off";
  order?: number;
  created_at?: string;
  updated_at?: string;
};

export type SupplierCreateRequest = {
  serial: string;
  name: string;
  address?: string;
  website?: string;
  status: "on" | "off";
  order?: number;
};

export type SupplierUpdateRequest = {
  id: string;
  serial?: string;
  name?: string;
  address?: string;
  website?: string;
  status?: "on" | "off";
  order?: number;
};

// 供应商列表查询参数类型
export type SupplierListParams = {
  options?: {
    order_by?: string;
    desc?: boolean;
  };
  page?: {
    page: number;
    limit: number;
  };
  params?: any[];
};

// Supplier管理CRUD方法
/** 获取供应商列表 */
export const getSupplierList = (params: SupplierListParams) => {
  return http.post<ApiListResponse<SupplierInfo>, SupplierListParams>(
    "/api/supplier/list",
    params
  );
};

/** 创建供应商 */
export const createSupplier = (data: SupplierCreateRequest) => {
  return http.post<ApiResponse<string>, SupplierCreateRequest>(
    "/api/supplier",
    data
  );
};

/** 更新供应商 */
export const updateSupplier = (data: SupplierUpdateRequest) => {
  return http.request<ApiResponse<string>>("put", "/api/supplier", {
    data
  });
};

/** 删除供应商 */
export const deleteSupplier = (id: string) => {
  return http.request<ApiResponse<string>>("delete", `/api/supplier/${id}`);
};

/** 获取单个供应商信息 */
export const getSupplier = (id: string) => {
  return http.get<ApiResponse<SupplierInfo>, any>(`/api/supplier/${id}`);
};

/** 更新供应商状态 */
export const updateSupplierStatus = (data: {
  id: string;
  status: "on" | "off";
}) => {
  return http.request<ApiResponse<string>>("put", "/api/supplier", {
    data
  });
};
