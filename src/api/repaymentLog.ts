import { http } from "@/utils/http";
import type { ApiListResponse, ApiResponse, ListParams } from "./types";

// RepaymentLog相关类型定义
export type RepaymentLogInfo = {
  id: string;
  parent_id: string; // 还款计划ID
  log_type: string; // 日志类型，还款、审核
  log_value?: string; // 日志记录值，根据类型来做解析
  log_date?: string; // 额外补充日期
  log_status?: string; // 日志状态，通过、拒绝、完成、确认等
  remark?: string; // 还款备注
  creater_id?: string; // 操作人ID
  creater_name?: string; // 操作人姓名
  created_at: number; // 创建时间（时间戳）
  updated_at: number; // 更新时间（时间戳）
};

export type RepaymentLogCreateRequest = {
  parent_id: string; // 还款计划ID
  log_type: string; // 日志类型，还款、审核
  log_value?: string; // 日志记录值，根据类型来做解析
  log_date?: string; // 额外补充日期
  log_status?: string; // 日志状态，通过、拒绝、完成、确认等
  remark?: string; // 还款备注
  creater_id?: string; // 操作人ID
  creater_name?: string; // 操作人姓名
};

export type RepaymentLogUpdateRequest = {
  id: string;
  parent_id?: string;
  log_type?: string;
  log_value?: string;
  log_date?: string;
  log_status?: string;
  remark?: string;
  creater_id?: string;
  creater_name?: string;
};

// 使用通用的ListParams类型
export type RepaymentLogListParams = ListParams;

// RepaymentLog管理CRUD方法
/** 获取还款记录列表 */
export const getRepaymentLogList = (params: RepaymentLogListParams) => {
  return http.post<ApiListResponse<RepaymentLogInfo>, RepaymentLogListParams>(
    "/api/repayment_log/list",
    params
  );
};

/** 创建还款记录 */
export const createRepaymentLog = (data: RepaymentLogCreateRequest) => {
  return http.post<ApiResponse<string>, RepaymentLogCreateRequest>(
    "/api/repayment_log",
    data
  );
};

/** 更新还款记录 */
export const updateRepaymentLog = (data: RepaymentLogUpdateRequest) => {
  return http.request<ApiResponse<string>>("put", "/api/repayment_log", {
    data
  });
};

/** 删除还款记录 */
export const deleteRepaymentLog = (id: string) => {
  return http.request<ApiResponse<string>>(
    "delete",
    `/api/repayment_log/${id}`
  );
};

/** 获取单个还款记录信息 */
export const getRepaymentLog = (id: string) => {
  return http.get<ApiResponse<RepaymentLogInfo>, any>(
    `/api/repayment_log/${id}`
  );
};

// 还款记录类型字典
export const repaymentLogTypeDict = [
  { value: "还款", label: "还款", color: "green" },
  { value: "审核", label: "审核", color: "blue" },
  { value: "调整", label: "调整", color: "orange" },
  { value: "退款", label: "退款", color: "red" },
  { value: "其他", label: "其他", color: "grey" }
];

// 还款记录状态字典
export const repaymentLogStatusDict = [
  { value: "通过", label: "通过", color: "green" },
  { value: "拒绝", label: "拒绝", color: "red" },
  { value: "完成", label: "完成", color: "blue" },
  { value: "确认", label: "确认", color: "orange" },
  { value: "待处理", label: "待处理", color: "grey" }
];

// 获取还款记录类型颜色
export const getRepaymentLogTypeColor = (type: string) => {
  const typeItem = repaymentLogTypeDict.find(item => item.value === type);
  return typeItem ? typeItem.color : "grey";
};

// 获取还款记录类型文本
export const getRepaymentLogTypeText = (type: string) => {
  const typeItem = repaymentLogTypeDict.find(item => item.value === type);
  return typeItem ? typeItem.label : type || "未知";
};

// 获取还款记录状态颜色
export const getRepaymentLogStatusColor = (status: string) => {
  const statusItem = repaymentLogStatusDict.find(item => item.value === status);
  return statusItem ? statusItem.color : "grey";
};

// 获取还款记录状态文本
export const getRepaymentLogStatusText = (status: string) => {
  const statusItem = repaymentLogStatusDict.find(item => item.value === status);
  return statusItem ? statusItem.label : status || "未知";
};

// 格式化还款金额
export const formatRepaymentAmount = (amount: string | number): string => {
  if (!amount) return "0.00";
  return Number(amount).toLocaleString("zh-CN", {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  });
};

// 格式化时间戳
export const formatRepaymentLogTime = (timestamp: number): string => {
  if (!timestamp) return "";
  const date = new Date(timestamp);
  return date.toLocaleString("zh-CN", {
    year: "numeric",
    month: "2-digit",
    day: "2-digit",
    hour: "2-digit",
    minute: "2-digit",
    second: "2-digit"
  });
};
