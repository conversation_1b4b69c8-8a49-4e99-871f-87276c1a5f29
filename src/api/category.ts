import { http } from "@/utils/http";
import type { ApiListResponse, ApiResponse, ListParams } from "./types";

// Category相关类型定义
export type CategoryInfo = {
  id: string;
  name: string;
  description?: string;
  parent?: string;
  category_type: string;
  order: number;
  children?: CategoryInfo[];
};

export type CategoryCreateRequest = {
  name: string;
  description?: string;
  parent?: string;
  category_type: string;
  order: number;
};

export type CategoryUpdateRequest = {
  id: string;
  name?: string;
  description?: string;
  parent?: string;
  category_type?: string;
  order?: number;
};

// 使用通用的ListParams类型
export type CategoryListParams = ListParams;

// Category管理CRUD方法
/** 获取分类列表 */
export const getCategoryList = (params: CategoryListParams) => {
  return http.post<ApiListResponse<CategoryInfo>, CategoryListParams>(
    "/api/category/list",
    params
  );
};

/** 获取分类树形结构 */
export const getCategoryTree = () => {
  return http.post<ApiListResponse<CategoryInfo>, any>("/api/category/tree");
};

/** 创建分类 */
export const createCategory = (data: CategoryCreateRequest) => {
  return http.post<ApiResponse<string>, CategoryCreateRequest>(
    "/api/category",
    data
  );
};

/** 更新分类 */
export const updateCategory = (data: CategoryUpdateRequest) => {
  return http.request<ApiResponse<string>>("put", "/api/category", {
    data
  });
};

/** 删除分类 */
export const deleteCategory = (id: string) => {
  return http.request<ApiResponse<string>>("delete", `/api/category/${id}`);
};

/** 获取单个分类信息 */
export const getCategory = (id: string) => {
  return http.get<ApiResponse<CategoryInfo>, any>(`/api/category/${id}`);
};
