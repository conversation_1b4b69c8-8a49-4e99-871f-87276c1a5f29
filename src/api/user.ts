import { http } from "@/utils/http";
import type {
  ApiListResponse,
  ApiResponse,
  FlexibleParams,
  ListParams,
  UpdateFields
} from "./types";

export type UserResult = {
  code: number;
  data: {
    /** JWT token */
    token: string;
    /** token过期时间戳 */
    exp: number;
  };
  msg: string;
};

export type LoginRequest = {
  username: string;
  password: string;
  captcha_str: string;
  captcha_id: string;
};

export type CaptchaResponse = {
  code: number;
  data: {
    captcha_id: string;
    captcha_img: string;
  };
};

export type RefreshTokenResult = {
  success: boolean;
  data: {
    /** `token` */
    accessToken: string;
    /** 用于调用刷新`accessToken`的接口时所需的`token` */
    refreshToken: string;
    /** `accessToken`的过期时间（格式'xxxx/xx/xx xx:xx:xx'） */
    expires: Date;
  };
};

/** 获取验证码 */
export const getCaptcha = () => {
  return http.get<CaptchaResponse, any>("/public/captcha");
};

/** 登录 */
export const getLogin = (data: LoginRequest) => {
  return http.post<UserResult, LoginRequest>("/public/login", data);
};

/** 刷新`token` */
export const refreshTokenApi = (data?: object) => {
  return http.post<RefreshTokenResult, any>("/refresh-token", data);
};

/** 获取当前用户信息 */
export const getCurrentUser = () => {
  return http.post<{ code: number; data: UserInfo; msg: string }, any>(
    "/api/user/current"
  );
};

// 用户管理相关类型定义
export type UserInfo = {
  id: string;
  login_name: string; // 前端显示为"用户名"，对应后端的login_name
  username: string; // 前端显示为"真实姓名"，对应后端的username
  is_admin: boolean;
  is_active: boolean;
  created_at: string;
  role_id?: string; // 用户绑定的角色ID
  remark?: string;
};

export type UserListResult = ApiListResponse<UserInfo>;

export type UserCreateRequest = {
  login_name: string; // 对应后端的login_name字段
  username: string; // 对应后端的username字段
  password: string;
  is_admin: boolean;
  is_active: boolean;
  remark?: string;
};

export type UserUpdateRequest = {
  id: string;
  login_name?: string; // 对应后端的login_name字段
  username?: string; // 对应后端的username字段
  password?: string;
  is_admin?: boolean;
  is_active?: boolean;
  remark?: string;
};

export type UserListParams = ListParams;

// 用户管理CRUD方法
/** 获取用户列表 */
export const getUserList = (params: UserListParams) => {
  return http.post<UserListResult, UserListParams>("/api/user/list", params);
};

/** 创建用户 */
export const createUser = (data: UserCreateRequest) => {
  return http.post<ApiResponse<string>, UserCreateRequest>("/api/user", data);
};

/** 更新用户 */
export const updateUser = (data: UserUpdateRequest) => {
  return http.request<ApiResponse<string>>("put", "/api/user", {
    data
  });
};

/** 删除用户 */
export const deleteUser = (id: string) => {
  return http.request<ApiResponse<string>>("delete", `/api/user/${id}`);
};

/** 获取单个用户信息 */
export const getUser = (id: string) => {
  return http.get<ApiResponse<UserInfo>, any>(`/api/user/${id}`);
};

/** 根据角色获取用户（使用FlexibleParams） */
export const getUserByRole = (data: FlexibleParams) => {
  return http.post<ApiListResponse<UserInfo>, FlexibleParams>(
    "/api/user/role",
    data
  );
};

/** 更新用户字段 */
export const updateUserField = (id: string, fields: UpdateFields) => {
  return http.request<ApiResponse<string>>("put", `/api/user/${id}`, {
    data: fields
  });
};
