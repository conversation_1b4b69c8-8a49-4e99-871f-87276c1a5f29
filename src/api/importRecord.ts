import { http } from "@/utils/http";
import type { ApiListResponse, ApiResponse, ListParams } from "./types";

// ImportRecord相关类型定义
export type ImportRecordInfo = {
  id: string;
  file_name: string;
  file_path: string;
  file_size: number;
  import_type: string;
  status: string;
  total_count: number;
  success_count: number;
  failed_count: number;
  error_message?: string;
  created_at: string;
  updated_at: string;
  created_by: string;
  remark?: string;
};

export type ImportRecordCreateRequest = {
  file_name: string;
  file_path: string;
  file_size: number;
  import_type: string;
  status?: string;
  total_count?: number;
  success_count?: number;
  failed_count?: number;
  error_message?: string;
  remark?: string;
};

export type ImportRecordUpdateRequest = {
  id: string;
  file_name?: string;
  file_path?: string;
  file_size?: number;
  import_type?: string;
  status?: string;
  total_count?: number;
  success_count?: number;
  failed_count?: number;
  error_message?: string;
  remark?: string;
};

// 使用通用的ListParams类型
export type ImportRecordListParams = ListParams;

// ImportRecord管理CRUD方法
/** 获取导入记录列表 */
export const getImportRecordList = (params: ImportRecordListParams) => {
  return http.post<ApiListResponse<ImportRecordInfo>, ImportRecordListParams>(
    "/api/import_record/list",
    params
  );
};

/** 创建导入记录 */
export const createImportRecord = (data: ImportRecordCreateRequest) => {
  return http.post<ApiResponse<string>, ImportRecordCreateRequest>(
    "/api/import_record",
    data
  );
};

/** 更新导入记录 */
export const updateImportRecord = (data: ImportRecordUpdateRequest) => {
  return http.request<ApiResponse<string>>("put", "/api/import_record", {
    data
  });
};

/** 删除导入记录 */
export const deleteImportRecord = (id: string) => {
  return http.request<ApiResponse<string>>(
    "delete",
    `/api/import_record/${id}`
  );
};

/** 获取单个导入记录信息 */
export const getImportRecord = (id: string) => {
  return http.get<ApiResponse<ImportRecordInfo>, any>(
    `/api/import_record/${id}`
  );
};

// 导入状态字典
export const importStatusDict = [
  { value: "pending", label: "待处理", color: "grey" },
  { value: "processing", label: "处理中", color: "blue" },
  { value: "success", label: "成功", color: "green" },
  { value: "failed", label: "失败", color: "red" },
  { value: "partial", label: "部分成功", color: "orange" }
];

// 导入类型字典
export const importTypeDict = [
  { value: "sales_order", label: "销售订单", color: "blue" },
  { value: "purchase_order", label: "采购订单", color: "green" },
  { value: "inventory", label: "库存", color: "purple" },
  { value: "customer", label: "客户", color: "cyan" },
  { value: "supplier", label: "供应商", color: "orange" }
];

// 获取状态颜色
export const getImportStatusColor = (status: string) => {
  const statusItem = importStatusDict.find(item => item.value === status);
  return statusItem ? statusItem.color : "grey";
};

// 获取状态文本
export const getImportStatusText = (status: string) => {
  const statusItem = importStatusDict.find(item => item.value === status);
  return statusItem ? statusItem.label : status || "未知";
};

// 获取导入类型颜色
export const getImportTypeColor = (type: string) => {
  const typeItem = importTypeDict.find(item => item.value === type);
  return typeItem ? typeItem.color : "grey";
};

// 获取导入类型文本
export const getImportTypeText = (type: string) => {
  const typeItem = importTypeDict.find(item => item.value === type);
  return typeItem ? typeItem.label : type || "未知";
};
