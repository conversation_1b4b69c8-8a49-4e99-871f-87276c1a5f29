import { http } from "@/utils/http";
import type { ApiListResponse, ApiResponse, ListParams } from "./types";

// Company相关类型定义
export type CompanyInfo = {
  id: string;
  name: string;
  serial: string;
  company_type?: string;
  corporate?: string;
  social_code?: string;
  industry_type?: string;
  address?: string;
  phone?: string;
  email?: string;
  website?: string;
  description?: string;
  status?: string;
  created_at?: string;
  updated_at?: string;
  creator_id?: string;
  updater_id?: string;
};

export type CompanyCreateRequest = {
  name: string;
  serial: string;
  company_type?: string;
  corporate?: string;
  social_code?: string;
  industry_type?: string;
  address?: string;
  phone?: string;
  email?: string;
  website?: string;
  description?: string;
  status?: string;
};

export type CompanyUpdateRequest = {
  id: string;
  name?: string;
  serial?: string;
  company_type?: string;
  corporate?: string;
  social_code?: string;
  industry_type?: string;
  address?: string;
  phone?: string;
  email?: string;
  website?: string;
  description?: string;
  status?: string;
};

// 使用通用的ListParams类型
export type CompanyListParams = ListParams;

// Company管理CRUD方法
/** 获取公司列表 */
export const getCompanyList = (params: CompanyListParams) => {
  return http.post<ApiListResponse<CompanyInfo>, CompanyListParams>(
    "/api/company/list",
    params
  );
};

/** 创建公司 */
export const createCompany = (data: CompanyCreateRequest) => {
  return http.post<ApiResponse<string>, CompanyCreateRequest>("/api/company", data);
};

/** 更新公司 */
export const updateCompany = (data: CompanyUpdateRequest) => {
  return http.request<ApiResponse<string>>("put", "/api/company", {
    data
  });
};

/** 删除公司 */
export const deleteCompany = (id: string) => {
  return http.request<ApiResponse<string>>("delete", `/api/company/${id}`);
};

/** 获取单个公司信息 */
export const getCompany = (id: string) => {
  return http.get<ApiResponse<CompanyInfo>, any>(`/api/company/${id}`);
};

/** 更新公司状态 */
export const updateCompanyStatus = (id: string, status: string) => {
  return http.post<ApiResponse<string>, { id: string; status: string }>(
    "/api/company/status",
    { id, status }
  );
};

// 公司类型字典
export const companyTypeDict = [
  { label: "有限责任公司", value: "limited_liability" },
  { label: "股份有限公司", value: "joint_stock" },
  { label: "个人独资企业", value: "sole_proprietorship" },
  { label: "合伙企业", value: "partnership" },
  { label: "外商投资企业", value: "foreign_investment" },
  { label: "其他", value: "other" }
];

// 行业类型字典
export const industryTypeDict = [
  { label: "制造业", value: "manufacturing" },
  { label: "信息技术", value: "information_technology" },
  { label: "金融服务", value: "financial_services" },
  { label: "房地产", value: "real_estate" },
  { label: "零售贸易", value: "retail_trade" },
  { label: "建筑工程", value: "construction" },
  { label: "教育培训", value: "education" },
  { label: "医疗健康", value: "healthcare" },
  { label: "交通运输", value: "transportation" },
  { label: "其他", value: "other" }
];

// 公司状态字典
export const companyStatusDict = [
  { label: "正常", value: "active" },
  { label: "暂停", value: "suspended" },
  { label: "注销", value: "cancelled" }
];
