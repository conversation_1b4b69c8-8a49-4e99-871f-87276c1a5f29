import { http } from "@/utils/http";
import type { ContractInfo } from "./contract";
import type {
  ApiListResponse,
  ApiResponse,
  IdParams,
  ListParams
} from "./types";

// Repayment相关类型定义
export type RepaymentInfo = {
  id: string;
  serial: string; // 还款计划编号
  contract_id: string; // 合作项目ID
  target_amount?: number | string; // 关联订单金额（后端可能返回字符串）
  profit_amount?: number | string; // 预计利润（后端可能返回字符串）
  principal_amount?: number | string; // 预计本金（后端可能返回字符串）
  total_amount?: number | string; // 预计总额（后端可能返回字符串）
  profit_remain?: number | string; // 剩余利润（后端可能返回字符串）
  principal_remain?: number | string; // 剩余本金（后端可能返回字符串）
  total_remain?: number | string; // 剩余总额（后端可能返回字符串）
  profit_calc_fee?: number; // 利润计算费用
  penalty_calc_fee?: number; // 违约金计算费用
  profit_calc_period?: string; // 利润计算周期
  penalty_calc_period?: string; // 违约金计算周期
  begin_date?: string; // 开始日期
  end_date?: string; // 结束日期
  grace_period?: number | string; // 宽限天数（后端可能返回字符串）
  status: string; // 计划状态
  created_at?: string;
  updated_at?: string;
  remark?: string;
  contract?: ContractInfo; // 关联的合同信息
};

export type RepaymentCreateRequest = {
  serial?: string; // 还款计划编号
  contract_id: string; // 合作项目ID
  target_amount?: number; // 关联订单金额
  principal_amount?: number; // 预计本金
  profit_calc_fee?: number; // 利润计算费用
  penalty_calc_fee?: number; // 违约金计算费用
  profit_calc_period?: string; // 利润计算周期
  penalty_calc_period?: string; // 违约金计算周期
  begin_date?: string; // 开始日期
  end_date?: string; // 结束日期
  grace_period?: number; // 宽限天数
  status?: string; // 状态
  remark?: string;
};

export type RepaymentUpdateRequest = {
  id: string;
  serial?: string;
  contract_id?: string;
  target_amount?: number;
  profit_amount?: number;
  principal_amount?: number;
  total_amount?: number;
  profit_remain?: number;
  principal_remain?: number;
  total_remain?: number;
  profit_calc_fee?: number;
  penalty_calc_fee?: number;
  profit_calc_period?: string;
  penalty_calc_period?: string;
  begin_date?: string;
  end_date?: string;
  grace_period?: number;
  status?: string;
  remark?: string;
};

// 使用通用的ListParams类型
export type RepaymentListParams = ListParams;

// Repayment管理CRUD方法
/** 获取还款计划列表 */
export const getRepaymentList = (params: RepaymentListParams) => {
  return http.post<ApiListResponse<RepaymentInfo>, RepaymentListParams>(
    "/api/repayment/list",
    params
  );
};

/** 创建还款计划 */
export const createRepayment = (data: RepaymentCreateRequest) => {
  return http.post<ApiResponse<string>, RepaymentCreateRequest>(
    "/api/repayment",
    data
  );
};

/** 更新还款计划 */
export const updateRepayment = (data: RepaymentUpdateRequest) => {
  return http.request<ApiResponse<string>>("put", "/api/repayment", {
    data
  });
};

/** 删除还款计划 */
export const deleteRepayment = (id: string) => {
  return http.request<ApiResponse<string>>("delete", `/api/repayment/${id}`);
};

/** 获取单个还款计划信息 */
export const getRepayment = (id: string) => {
  return http.get<ApiResponse<RepaymentInfo>, any>(`/api/repayment/${id}`);
};

// 还款计划关联订单相关类型定义
export type RepaymentRelateRequest = {
  id: string; // 还款计划ID
  ids: string[]; // 订单ID列表
};

export type RepaymentUnrelateRequest = {
  id: string; // 还款计划ID
  order_id: string; // 订单ID
};

// 还款计划审核相关类型定义
export type RepaymentReviewRequest = {
  confirm: boolean; // true为审核通过，false为审核拒绝
  remark?: string; // 审核备注
};

/** 关联订单到还款计划 */
export const relateOrdersToRepayment = (data: RepaymentRelateRequest) => {
  return http.post<ApiResponse<string>, RepaymentRelateRequest>(
    "/api/repayment/relate",
    data
  );
};

/** 取消关联订单 */
export const unrelateOrderFromRepayment = (data: IdParams) => {
  return http.post<ApiResponse<string>, IdParams>(
    "/api/repayment/unrelate",
    data
  );
};

/** 审核还款计划 */
export const reviewRepayment = (id: string, data: RepaymentReviewRequest) => {
  return http.post<ApiResponse<string>, RepaymentReviewRequest>(
    `/api/repayment/${id}`,
    data
  );
};

// 还款状态字典
export const repaymentStatusDict = [
  { value: "draft", label: "草稿", color: "grey" },
  { value: "new", label: "待审核", color: "blue" },
  { value: "processing", label: "已审核", color: "green" },
  { value: "pending", label: "待还款", color: "orange" },
  { value: "partial", label: "部分还款", color: "yellow" },
  { value: "completed", label: "已完成", color: "teal" },
  { value: "overdue", label: "逾期", color: "red" }
];

// 获取还款状态颜色
export const getRepaymentStatusColor = (status: string) => {
  const statusItem = repaymentStatusDict.find(item => item.value === status);
  return statusItem ? statusItem.color : "grey";
};

// 获取还款状态文本
export const getRepaymentStatusText = (status: string) => {
  const statusItem = repaymentStatusDict.find(item => item.value === status);
  return statusItem ? statusItem.label : status || "未知";
};

// 计算周期字典（对象格式）
export const calcPeriodDict = Object.freeze({
  单次: "ONCE",
  按周: "WEEK",
  按天: "DAY",
  按月: "MONTH",
  按年: "YEAR",
  按季度: "QUARTER"
});

// 计算周期选项（数组格式，用于下拉选择）
export const calcPeriodOptions = [
  { label: "单次", value: "ONCE" },
  { label: "按天", value: "DAY" },
  { label: "按月", value: "MONTH" },
  { label: "按年", value: "YEAR" },
  { label: "按季度", value: "QUARTER" }
];

// 计算周期反向字典
export const calcPeriodReverseDict = Object.freeze({
  ONCE: "单次",
  WEEK: "按周",
  DAY: "按天",
  MONTH: "按月",
  YEAR: "按年",
  QUARTER: "按季度"
});
