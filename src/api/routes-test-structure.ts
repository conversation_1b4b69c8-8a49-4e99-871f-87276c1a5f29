// 测试路由数据结构转换
import { getAsyncRoutes } from "./routes";

// 模拟后端返回的用户管理数据
const mockUserMenuData = {
  "code": 200,
  "data": [
    {
      "id": "menu:7pdw8rpn8h85602p7giy",
      "name": "userList",
      "order": 99,
      "path": "/user/list",
      "component": "pages/user/index",
      "redirect": "",
      "active": "yes",
      "title": "UserList",
      "icon": "user",
      "keep_alive": "no",
      "hidden": "no",
      "is_link": "no",
      "parent": "''",
      "remark": "用户列表",
      "children": null
    },
    {
      "id": "menu:g0d56xhoobz6vsp2ah1z",
      "name": "userDetail",
      "order": 1,
      "path": "/user/detail",
      "component": "pages/user/detail",
      "redirect": "",
      "active": "yes",
      "title": "UserDetail",
      "icon": "user-detail",
      "keep_alive": "yes",
      "hidden": "yes",
      "is_link": "no",
      "parent": "userList",
      "remark": "用户详情",
      "children": null
    }
  ],
  "msg": "success"
};

// 预期的转换结果（符合项目路由标准）
const expectedRouteStructure = [
  {
    path: "/user/list",
    name: "userList",
    component: "pages/user/index",
    meta: {
      title: "UserList",
      icon: "user",
      rank: 99,
      showLink: true,  // hidden !== "yes"
      keepAlive: false, // keep_alive !== "yes"
      roles: ["admin", "common"]
    },
    children: [
      {
        path: "/user/detail",
        name: "userDetail",
        component: "pages/user/detail",
        meta: {
          title: "UserDetail",
          icon: "user-detail",
          rank: 1,
          showLink: false, // hidden === "yes"
          keepAlive: true,  // keep_alive === "yes"
          roles: ["admin", "common"]
        }
      }
    ]
  }
];

// 测试函数
export function testRouteStructure() {
  console.log("=== 路由数据结构测试 ===");
  console.log("原始后端数据:", mockUserMenuData);
  console.log("预期转换结果:", expectedRouteStructure);
  
  // 实际使用时的调用方式
  console.log("实际调用: getAsyncRoutes()");
  
  // 验证数据结构是否符合项目标准
  console.log("\n=== 数据结构验证 ===");
  console.log("✅ 路径格式: /user/list");
  console.log("✅ 组件路径: pages/user/index (不带前缀/)");
  console.log("✅ meta.title: 菜单标题");
  console.log("✅ meta.icon: 菜单图标");
  console.log("✅ meta.rank: 排序权重");
  console.log("✅ meta.showLink: 是否显示菜单");
  console.log("✅ meta.keepAlive: 是否缓存页面");
  console.log("✅ meta.roles: 权限角色");
  console.log("✅ children: 子路由数组");
  
  return expectedRouteStructure;
}

// 路由结构说明
export const routeStructureDoc = {
  description: "项目路由数据结构说明",
  structure: {
    path: "路由路径，必须以/开头",
    name: "路由名称，对应组件name",
    component: "组件路径，相对于src/views或src/pages",
    redirect: "重定向路径（可选）",
    meta: {
      title: "菜单标题，支持国际化",
      icon: "菜单图标",
      rank: "排序权重，数字越小越靠前",
      showLink: "是否在菜单中显示",
      keepAlive: "是否缓存页面",
      roles: "权限角色数组"
    },
    children: "子路由数组（可选）"
  },
  examples: {
    simpleRoute: {
      path: "/user/list",
      name: "userList",
      component: "pages/user/index",
      meta: {
        title: "用户列表",
        icon: "user",
        rank: 1,
        showLink: true,
        keepAlive: false,
        roles: ["admin"]
      }
    },
    parentWithChildren: {
      path: "/system",
      name: "system",
      meta: {
        title: "系统管理",
        icon: "setting",
        rank: 100
      },
      children: [
        {
          path: "/system/user",
          name: "systemUser",
          component: "system/user/index",
          meta: {
            title: "用户管理",
            showLink: true
          }
        }
      ]
    }
  }
};

console.log("路由结构文档:", routeStructureDoc);
