import { http } from "@/utils/http";
import type { ApiListResponse, ApiResponse, ListParams } from "./types";

// Warehouse相关类型定义
export type WarehouseInfo = {
  id: string;
  serial: string;
  name: string;
  company?: string;
  contacts_person?: string;
  contacts_phone?: string;
  contact_address?: string;
  location?: string;
  remark?: string;
  created_at: number;
  updated_at: number;
};

export type WarehouseCreateRequest = {
  serial: string;
  name: string;
  company?: string;
  contacts_person?: string;
  contacts_phone?: string;
  contact_address?: string;
  location?: string;
  remark?: string;
};

export type WarehouseUpdateRequest = {
  id: string;
  serial: string;
  name: string;
  company?: string;
  contacts_person?: string;
  contacts_phone?: string;
  contact_address?: string;
  location?: string;
  remark?: string;
};

// 使用通用的ListParams类型
export type WarehouseListParams = ListParams;

// Warehouse管理CRUD方法
/** 获取仓库列表 */
export const getWarehouseList = (params: WarehouseListParams) => {
  return http.post<ApiListResponse<WarehouseInfo>, WarehouseListParams>(
    "/api/warehouse/list",
    params
  );
};

/** 创建仓库 */
export const createWarehouse = (data: WarehouseCreateRequest) => {
  return http.post<ApiResponse<string>, WarehouseCreateRequest>(
    "/api/warehouse",
    data
  );
};

/** 更新仓库 */
export const updateWarehouse = (data: WarehouseUpdateRequest) => {
  return http.request<ApiResponse<string>>("put", "/api/warehouse", {
    data
  });
};

/** 删除仓库 */
export const deleteWarehouse = (id: string) => {
  return http.request<ApiResponse<string>>("delete", `/api/warehouse/${id}`);
};

/** 获取单个仓库信息 */
export const getWarehouse = (id: string) => {
  return http.get<ApiResponse<WarehouseInfo>, any>(`/api/warehouse/${id}`);
};


