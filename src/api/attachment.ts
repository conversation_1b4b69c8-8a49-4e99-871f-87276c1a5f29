import { http } from "@/utils/http";
import type { ApiResponse, ListParams } from "./types";

// Attachment相关类型定义（基于后端 AttachmentResponse 结构）
export type AttachmentInfo = {
  id: string;
  title?: string; // 附件标题
  entity_type: string; // 实体类型
  entity_id?: string; // 实体ID
  attachment_type?: string; // 附件类型
  file_type?: string; // 文件类型
  save_dir: string; // 保存目录
  file_name: string; // 文件名
  file_link: string; // 文件链接
  thumb_name?: string; // 缩略图名称
  thumb_link?: string; // 缩略图链接
  status: string; // 状态
  sort: number; // 排序
  created_at: number; // 创建时间（时间戳）
  updated_at: number; // 更新时间（时间戳）
};

export type AttachmentCreateRequest = {
  title?: string;
  entity_type: string;
  entity_id?: string;
  attachment_type?: string;
  file_type?: string;
  save_dir: string;
  file_name: string;
  file_link: string;
  thumb_name?: string;
  thumb_link?: string;
  status: string;
  sort?: number;
};

export type AttachmentUpdateRequest = {
  id: string;
  title?: string;
  entity_type?: string;
  entity_id?: string;
  attachment_type?: string;
  file_type?: string;
  save_dir?: string;
  file_name?: string;
  file_link?: string;
  thumb_name?: string;
  thumb_link?: string;
  status?: string;
  sort?: number;
};

// 使用通用的ListParams类型
export type AttachmentListParams = ListParams;

// Attachment管理CRUD方法
/** 获取附件列表 */
export const getAttachmentList = (params: AttachmentListParams) => {
  return http.post<ApiResponse<AttachmentInfo[]>, AttachmentListParams>(
    "/api/attachment/list",
    params
  );
};

/** 创建附件 */
export const createAttachment = (data: AttachmentCreateRequest) => {
  return http.post<ApiResponse<string>, AttachmentCreateRequest>(
    "/api/attachment",
    data
  );
};

/** 更新附件 */
export const updateAttachment = (data: AttachmentUpdateRequest) => {
  return http.request<ApiResponse<string>>("put", "/api/attachment", {
    data
  });
};

/** 删除附件 */
export const deleteAttachment = (id: string) => {
  return http.request<ApiResponse<string>>("delete", `/api/attachment/${id}`);
};

/** 获取单个附件信息 */
export const getAttachment = (id: string) => {
  return http.get<ApiResponse<AttachmentInfo>, any>(`/api/attachment/${id}`);
};

// 附件状态字典
export const attachmentStatusDict = [
  { value: "active", label: "正常", color: "green" },
  { value: "deleted", label: "已删除", color: "red" },
  { value: "archived", label: "已归档", color: "grey" }
];

// 获取附件状态颜色
export const getAttachmentStatusColor = (status: string) => {
  const statusItem = attachmentStatusDict.find(item => item.value === status);
  return statusItem ? statusItem.color : "grey";
};

// 获取附件状态文本
export const getAttachmentStatusText = (status: string) => {
  const statusItem = attachmentStatusDict.find(item => item.value === status);
  return statusItem ? statusItem.label : status || "未知";
};

// 文件类型字典
export const fileTypeDict = [
  {
    value: "image",
    label: "图片",
    extensions: ["jpg", "jpeg", "png", "gif", "bmp", "webp"]
  },
  {
    value: "document",
    label: "文档",
    extensions: ["pdf", "doc", "docx", "xls", "xlsx", "ppt", "pptx", "txt"]
  },
  {
    value: "video",
    label: "视频",
    extensions: ["mp4", "avi", "mov", "wmv", "flv", "mkv"]
  },
  {
    value: "audio",
    label: "音频",
    extensions: ["mp3", "wav", "flac", "aac", "ogg"]
  },
  {
    value: "archive",
    label: "压缩包",
    extensions: ["zip", "rar", "7z", "tar", "gz"]
  },
  { value: "other", label: "其他", extensions: [] }
];

// 根据文件扩展名获取文件类型
export const getFileTypeByExtension = (fileName: string): string => {
  const extension = fileName.split(".").pop()?.toLowerCase();
  if (!extension) return "other";

  for (const type of fileTypeDict) {
    if (type.extensions.includes(extension)) {
      return type.value;
    }
  }
  return "other";
};

// 格式化文件大小
export const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return "0 B";

  const k = 1024;
  const sizes = ["B", "KB", "MB", "GB", "TB"];
  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
};
