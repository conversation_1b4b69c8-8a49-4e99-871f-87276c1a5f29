/**
 * 时间格式化工具函数
 */

/**
 * 格式化日期时间
 * @param dateTime 日期时间字符串或Date对象
 * @param format 格式化模板，默认为 'YYYY-MM-DD HH:mm:ss'
 * @returns 格式化后的时间字符串
 */
export function formatDateTime(
  dateTime: string | Date | number,
  format: string = "YYYY-MM-DD HH:mm:ss"
): string {
  if (!dateTime) return "";

  let date: Date;

  if (typeof dateTime === "number") {
    // 如果是数字，判断是秒级还是毫秒级时间戳
    // 秒级时间戳通常小于 10^12，毫秒级时间戳大于 10^12
    const timestamp = dateTime < 1e12 ? dateTime * 1000 : dateTime;
    date = new Date(timestamp);
  } else {
    date = new Date(dateTime);
  }

  // 检查日期是否有效
  if (isNaN(date.getTime())) {
    return "";
  }

  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, "0");
  const day = String(date.getDate()).padStart(2, "0");
  const hours = String(date.getHours()).padStart(2, "0");
  const minutes = String(date.getMinutes()).padStart(2, "0");
  const seconds = String(date.getSeconds()).padStart(2, "0");

  return format
    .replace("YYYY", String(year))
    .replace("MM", month)
    .replace("DD", day)
    .replace("HH", hours)
    .replace("mm", minutes)
    .replace("ss", seconds);
}

/**
 * 格式化日期
 * @param date 日期字符串或Date对象
 * @returns 格式化后的日期字符串 YYYY-MM-DD
 */
export function formatDate(date: string | Date | number): string {
  return formatDateTime(date, "YYYY-MM-DD");
}

/**
 * 格式化时间
 * @param time 时间字符串或Date对象
 * @returns 格式化后的时间字符串 HH:mm:ss
 */
export function formatTime(time: string | Date | number): string {
  return formatDateTime(time, "HH:mm:ss");
}

/**
 * 相对时间格式化（如：刚刚、1分钟前、1小时前等）
 * @param dateTime 日期时间字符串或Date对象
 * @returns 相对时间字符串
 */
export function formatRelativeTime(dateTime: string | Date | number): string {
  if (!dateTime) return "";

  const date = new Date(dateTime);
  const now = new Date();
  const diff = now.getTime() - date.getTime();

  // 检查日期是否有效
  if (isNaN(date.getTime())) {
    return "";
  }

  const seconds = Math.floor(diff / 1000);
  const minutes = Math.floor(seconds / 60);
  const hours = Math.floor(minutes / 60);
  const days = Math.floor(hours / 24);

  if (seconds < 60) {
    return "刚刚";
  } else if (minutes < 60) {
    return `${minutes}分钟前`;
  } else if (hours < 24) {
    return `${hours}小时前`;
  } else if (days < 7) {
    return `${days}天前`;
  } else {
    return formatDateTime(dateTime, "YYYY-MM-DD");
  }
}
