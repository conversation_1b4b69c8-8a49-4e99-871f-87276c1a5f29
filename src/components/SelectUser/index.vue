<script setup lang="ts">
// 移除不需要的RoleInfo导入
import type { ListParams, WhereOptions } from "@/api/types";
import { getUserList, type UserInfo } from "@/api/user";
import { IconifyIconOnline } from "@/components/ReIcon";
import { message } from "@/utils/message";
import { computed, onMounted, ref } from "vue";

// 声明 props 类型
export interface SelectUserProps {
  filterParams?: WhereOptions[]; // 外部传入的过滤参数
}

// 声明 props
const props = withDefaults(defineProps<SelectUserProps>(), {
  filterParams: () => []
});

// 声明 emits
const emit = defineEmits<{
  userSelected: [user: UserInfo];
  close: [];
}>();

// 响应式数据
const loading = ref(false);
const userList = ref<UserInfo[]>([]);
const searchKeyword = ref("");

// 搜索表单
const searchForm = ref({
  username: "",
  login_name: ""
});

// 分页配置
const pagination = ref({
  currentPage: 1,
  pageSize: 10,
  total: 0
});

// 过滤后的用户列表
const filteredUserList = computed(() => {
  if (!searchKeyword.value) {
    return userList.value;
  }
  return userList.value.filter(
    user =>
      user.login_name
        .toLowerCase()
        .includes(searchKeyword.value.toLowerCase()) ||
      user.username.toLowerCase().includes(searchKeyword.value.toLowerCase())
  );
});

// 获取用户列表
const getUserListData = async () => {
  loading.value = true;
  try {
    const params: ListParams = {
      options: {
        order_by: "created_at",
        desc: true
      },
      page: {
        page: pagination.value.currentPage,
        limit: pagination.value.pageSize
      },
      params: []
    };

    // 添加外部传入的过滤参数
    if (props.filterParams && props.filterParams.length > 0) {
      params.params.push(...props.filterParams);
    }

    // 添加搜索条件
    if (searchForm.value.username) {
      params.params.push({
        var: "username",
        val: searchForm.value.username
      });
    }
    if (searchForm.value.login_name) {
      params.params.push({
        var: "login_name",
        val: searchForm.value.login_name
      });
    }

    const response = await getUserList(params);
    if (response.code === 200) {
      userList.value = response.data.data;
      pagination.value.total = response.data.total;
    } else {
      message(response.msg || "获取用户列表失败", { type: "error" });
    }
  } catch (error) {
    console.error("获取用户列表失败:", error);
    message("获取用户列表失败", { type: "error" });
  } finally {
    loading.value = false;
  }
};

// 选择用户
const handleSelect = (user: UserInfo) => {
  emit("userSelected", user);
};

// 搜索用户
const handleSearch = () => {
  pagination.value.currentPage = 1;
  getUserListData();
};

// 重置搜索
const handleReset = () => {
  searchForm.value = {
    username: "",
    login_name: ""
  };
  searchKeyword.value = "";
  pagination.value.currentPage = 1;
  getUserListData();
};

// 分页改变
const handlePageChange = (page: number) => {
  pagination.value.currentPage = page;
  getUserListData();
};

// 页面大小改变
const handleSizeChange = (size: number) => {
  pagination.value.pageSize = size;
  pagination.value.currentPage = 1;
  getUserListData();
};

// 组件挂载时获取数据
onMounted(() => {
  getUserListData();
});
</script>

<template>
  <div class="select-user-drawer">
    <div class="header mb-4">
      <span class="font-medium">选择用户</span>
    </div>

    <!-- 搜索区域 -->
    <div class="search-section mb-4">
      <el-form :inline="true" :model="searchForm" class="demo-form-inline">
        <el-form-item label="用户名">
          <el-input
            v-model="searchForm.username"
            placeholder="请输入用户名"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="真实姓名">
          <el-input
            v-model="searchForm.login_name"
            placeholder="请输入真实姓名"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>

      <!-- 快速搜索 -->
      <el-input
        v-model="searchKeyword"
        placeholder="快速搜索用户名或真实姓名..."
        clearable
        class="mt-2"
        style="width: 300px"
      >
        <template #prefix>
          <IconifyIconOnline icon="ep:search" />
        </template>
      </el-input>
    </div>

    <!-- 用户列表 -->
    <div class="user-list">
      <el-table
        :data="filteredUserList"
        :loading="loading"
        style="width: 100%"
        border
        stripe
      >
        <el-table-column prop="login_name" label="用户名" min-width="120" />
        <el-table-column prop="username" label="真实姓名" min-width="120" />
        <el-table-column
          prop="is_admin"
          label="是否管理员"
          width="100"
          align="center"
        >
          <template #default="{ row }">
            <el-tag :type="row.is_admin ? 'success' : 'info'">
              {{ row.is_admin ? "是" : "否" }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column
          prop="is_active"
          label="状态"
          width="100"
          align="center"
        >
          <template #default="{ row }">
            <el-tag :type="row.is_active ? 'success' : 'danger'">
              {{ row.is_active ? "启用" : "禁用" }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="created_at" label="创建时间" width="160" />
        <el-table-column label="操作" width="100" fixed="right" align="center">
          <template #default="{ row }">
            <el-button
              link
              type="primary"
              size="small"
              @click="handleSelect(row)"
            >
              选择
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 分页 -->
    <div class="pagination mt-4" style="text-align: right">
      <el-pagination
        v-model:current-page="pagination.currentPage"
        v-model:page-size="pagination.pageSize"
        :total="pagination.total"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handlePageChange"
      />
    </div>

    <div v-if="filteredUserList.length === 0 && !loading" class="empty-state">
      <el-empty description="暂无用户数据" />
    </div>
  </div>
</template>

<style scoped>
.select-user-drawer {
  padding: 20px;
}

.header {
  font-size: 16px;
  margin-bottom: 16px;
}

.font-medium {
  font-weight: 500;
}

.mb-4 {
  margin-bottom: 16px;
}

.mt-2 {
  margin-top: 8px;
}

.mt-4 {
  margin-top: 16px;
}

.empty-state {
  margin-top: 40px;
  text-align: center;
}
</style>
