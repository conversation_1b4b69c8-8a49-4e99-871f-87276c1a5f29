<template>
  <el-dialog
    v-model="selectItemVisible"
    :title="dialogTitle"
    width="800px"
    :close-on-click-modal="false"
    destroy-on-close
  >
    <template #header>
      <div class="flex justify-between items-center w-full">
        <span class="text-lg font-medium">{{ dialogTitle }}</span>
        <el-button type="primary" @click="handleSelectItem()">
          选择
        </el-button>
      </div>
    </template>

    <!-- 搜索表单 -->
    <el-form
      ref="formRef"
      :inline="true"
      :model="queryParams"
      class="search-form mb-4"
    >
      <el-form-item label="名称：" prop="name">
        <el-input
          v-model="queryParams.name"
          placeholder="请输入名称"
          clearable
          class="w-[200px]!"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="handleSearch">
          搜索
        </el-button>
        <el-button @click="resetSearch">
          重置
        </el-button>
      </el-form-item>
    </el-form>

    <!-- 表格 -->
    <pure-table
      ref="tableRef"
      row-key="id"
      align-whole="center"
      showOverflowTooltip
      :loading="loading"
      :data="tableData"
      :columns="columns"
      :pagination="pagination"
      :selection="selection"
      @page-size-change="onSizeChange"
      @page-current-change="onCurrentChange"
      @selection-change="handleSelectionChange"
    >
      <template #operation="{ row }">
        <el-button
          link
          type="primary"
          size="small"
          @click="handleSelectItem(row)"
        >
          选择
        </el-button>
      </template>
    </pure-table>
  </el-dialog>
</template>

<script setup lang="ts">
import { http } from "@/utils/http";
import { PureTable } from "@pureadmin/table";
import "@pureadmin/table/dist/style.css";
import { ElMessage, type FormInstance } from "element-plus";
import { computed, onMounted, reactive, ref, toRefs } from "vue";

// Props定义
interface Props {
  selection: "single" | "multiple";
  title?: string;
  urlList: string;
}

const props = withDefaults(defineProps<Props>(), {
  title: "选择窗口"
});

const { selection, title, urlList } = toRefs(props);

// 响应式数据
const loading = ref(false);
const tableData = ref<any[]>([]);
const selectItemVisible = ref(false);
const selected = ref<any[]>([]);
const tableRef = ref();
const formRef = ref<FormInstance>();

// 搜索参数
const queryParams = reactive({
  name: ""
});

// 分页配置
const pagination = reactive({
  total: 0,
  pageSize: 10,
  currentPage: 1,
  background: true,
  pageSizes: [10, 20, 50, 100]
});

// 对话框标题
const dialogTitle = computed(() => {
  return `${title.value} - ${selection.value === "multiple" ? "多选项目" : "单选项目"}`;
});

// 表格列配置
const columns = computed(() => [
  {
    label: "ID",
    prop: "id",
    minWidth: 80
  },
  {
    label: "名称",
    prop: "name",
    minWidth: 150
  },
  {
    label: "编号",
    prop: "serial",
    minWidth: 120
  },
  {
    label: "类型",
    prop: "type",
    minWidth: 100
  },
  {
    label: "操作",
    fixed: "right",
    width: 100,
    slot: "operation"
  }
]);

// 获取表格数据
const getTableData = async () => {
  loading.value = true;
  try {
    const params: any = {
      options: {
        order_by: "id",
        desc: false
      },
      page: {
        page: pagination.currentPage,
        limit: pagination.pageSize
      }
    };

    // 添加搜索参数
    if (queryParams.name) {
      params.params = [
        {
          var: "name",
          val: queryParams.name
        }
      ];
    }

    const response = await http.post(urlList.value, params);
    if (response.code === 200) {
      tableData.value = response.data.data;
      pagination.total = response.data.total;
    } else {
      ElMessage.error(response.msg || "获取数据失败");
      tableData.value = [];
    }
  } catch (error) {
    console.error("获取数据失败:", error);
    ElMessage.error("获取数据失败");
    tableData.value = [];
  } finally {
    loading.value = false;
  }
};

// 搜索
const handleSearch = () => {
  pagination.currentPage = 1;
  getTableData();
};

// 重置搜索
const resetSearch = () => {
  if (formRef.value) {
    formRef.value.resetFields();
  }
  handleSearch();
};

// 分页相关
const onSizeChange = (size: number) => {
  pagination.pageSize = size;
  getTableData();
};

const onCurrentChange = (current: number) => {
  pagination.currentPage = current;
  getTableData();
};

// 处理选择变化（多选模式）
const handleSelectionChange = (selection: any[]) => {
  selected.value = selection;
};

// 显示对话框
const show = (selectItem?: any) => {
  selected.value = [];
  selectItemVisible.value = true;
  getTableData();
  
  if (selection.value === "multiple") {
    if (Array.isArray(selectItem)) {
      selected.value = selectItem;
    } else {
      selected.value = [];
    }
  }
};

// 暴露方法
defineExpose({
  show
});

// 事件定义
const emit = defineEmits<{
  handleSelectItem: [value: any];
}>();

// 处理选择项目
const handleSelectItem = (row?: any) => {
  if (row?.id) {
    // 单行选择
    emit("handleSelectItem", row);
    console.log("选择单行:", row);
  } else {
    // 批量选择（多选模式）
    emit("handleSelectItem", selected.value);
    console.log("批量选择:", selected.value);
  }
  selectItemVisible.value = false;
};
</script>

<style lang="scss" scoped>
.search-form {
  :deep(.el-form-item) {
    margin-bottom: 12px;
  }
}
</style>
