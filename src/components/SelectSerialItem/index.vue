<template>
  <div>
    <el-input
      v-model="returnItem"
      :placeholder="label"
      :class="className"
      readonly
    >
      <template #append>
        <el-button
          v-if="canEdit"
          type="primary"
          @click="showSelectDialog"
        >
          选择
        </el-button>
      </template>
    </el-input>
    <SelectDialog
      ref="selectItemDialog"
      :selection="selection"
      :url-list="url"
      :title="label"
      @handleSelectItem="handleSelectItem"
    />
  </div>
</template>

<script setup lang="ts">
import { computed, ref, toRefs } from "vue";
import SelectDialog from "./SelectItemDialog.vue";

// Props定义
interface Props {
  selectItem?: any[] | any;
  label?: string;
  url: string;
  className?: string;
  canEdit?: boolean;
  selection?: "single" | "multiple";
}

const props = withDefaults(defineProps<Props>(), {
  selectItem: () => ({}),
  label: "选择数据项目",
  className: "",
  canEdit: false,
  selection: "single"
});

const { selectItem, label, className, selection, canEdit, url } = toRefs(props);

// 计算显示文本
const returnItem = computed(() => {
  if (selection.value === "multiple") {
    if (!Array.isArray(selectItem.value) || selectItem.value.length === 0) {
      return "未选择相关项目";
    }
    
    let serial = "";
    let id = "";
    for (const u of selectItem.value) {
      if (u.serial) {
        serial += u.serial + " ";
      } else if (u.id) {
        id += u.id + " ";
      } else {
        return "";
      }
    }
    return serial || id;
  } else {
    if (selectItem.value?.name) {
      return selectItem.value.serial + "：" + selectItem.value.name;
    } else {
      return "未选择相关项目";
    }
  }
});

// 对话框引用
const selectItemDialog = ref<InstanceType<typeof SelectDialog>>();

// 显示选择对话框
const showSelectDialog = () => {
  selectItemDialog.value?.show(selectItem.value);
};

// 事件定义
const emit = defineEmits<{
  "update:selectItem": [value: any];
}>();

// 处理选择项目
const handleSelectItem = (event: any) => {
  if (selection.value === "multiple") {
    emit("update:selectItem", event);
  } else {
    if (event.id) {
      console.log("get event", event);
      emit("update:selectItem", event);
    } else {
      emit("update:selectItem", {});
    }
  }
};
</script>

<style lang="scss" scoped>
// 可以根据需要添加样式
</style>
