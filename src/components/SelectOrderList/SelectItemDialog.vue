<template>
  <el-dialog
    v-model="visible"
    title="选择订单进行关联"
    width="80%"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    top="5vh"
  >
    <!-- 搜索区域 -->
    <div class="mb-4 flex items-center gap-4">
      <el-input
        v-model="queryParams.name"
        placeholder="请输入订单名称或编号"
        style="width: 300px"
        clearable
      />
      <el-button type="primary" @click="handleSearch">搜索</el-button>
      <el-button @click="resetSearch">重置</el-button>
      <div class="flex-1" />
      <el-button
        type="primary"
        :disabled="!hasSelected"
        @click="handleSelectItem()"
      >
        选择 {{ selectedCount > 0 ? `(${selectedCount})` : "" }}
      </el-button>
    </div>

    <!-- 表格区域 -->
    <PureTable
      ref="tableRef"
      :data="tableData"
      :columns="columns"
      :pagination="pagination"
      :loading="loading"
      :show-overflow-tooltip="false"
      height="500"
      @page-size-change="handlePageSizeChange"
      @page-current-change="handlePageCurrentChange"
      @selection-change="handleSelectionChange"
    >
      <template #selection="{ row }">
        <el-checkbox
          :model-value="isSelected(row)"
          @change="handleRowSelect(row, $event)"
        />
      </template>

      <template #platform_order_serial="{ row }">
        <span class="text-blue-600">{{
          row.platform_order_serial || "-"
        }}</span>
      </template>

      <template #total_payment="{ row }">
        <span class="font-medium text-green-600">
          ¥{{ formatAmount(row.total_payment) }}
        </span>
      </template>

      <template #status="{ row }">
        <el-tag :type="getStatusType(row.status)" size="small">
          {{ getStatusText(row.status) }}
        </el-tag>
      </template>

      <template #actions="{ row }">
        <el-button
          type="primary"
          size="small"
          @click="handleSelectSingleItem(row)"
        >
          选择
        </el-button>
      </template>
    </PureTable>

    <template #footer>
      <div class="flex justify-between items-center">
        <div class="text-sm text-gray-600">
          已选择 {{ selectedCount }} 个订单
        </div>
        <div class="flex gap-2">
          <el-button @click="handleCancel">取消</el-button>
          <el-button
            type="primary"
            :disabled="!hasSelected"
            @click="handleSelectItem()"
          >
            确定选择
          </el-button>
        </div>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { getSalesOrderList, type SalesOrderInfo } from "@/api/salesOrder";
import { PureTable } from "@pureadmin/table";
import { ElMessage } from "element-plus";
import { computed, ref, toRefs } from "vue";

defineOptions({
  name: "SelectItemDialog"
});

// Props
const props = defineProps<{
  urlList: string;
  contractId: string;
}>();

const { contractId } = toRefs(props);

// Emits
const emit = defineEmits<{
  handleSelectItem: [selectedItems: SalesOrderInfo[]];
}>();

// 响应式数据
const visible = ref(false);
const loading = ref(false);
const tableData = ref<SalesOrderInfo[]>([]);
const selectedItems = ref<SalesOrderInfo[]>([]);

// 查询参数
const queryParams = ref({
  name: ""
});

// 分页参数
const pagination = ref({
  page: 1,
  size: 50,
  total: 0
});

// 表格列定义
const columns = computed(() => [
  {
    type: "selection",
    width: 50,
    slot: "selection"
  },
  {
    label: "平台订单编号",
    prop: "platform_order_serial",
    minWidth: 150,
    slot: "platform_order_serial"
  },
  {
    label: "销售平台",
    prop: "platform_name",
    minWidth: 120
  },
  {
    label: "订单编号",
    prop: "serial",
    minWidth: 150
  },
  {
    label: "订单总额",
    prop: "total_payment",
    minWidth: 120,
    slot: "total_payment"
  },
  {
    label: "订单状态",
    prop: "status",
    minWidth: 100,
    slot: "status"
  },
  {
    label: "操作",
    fixed: "right",
    width: 80,
    slot: "actions"
  }
]);

// 计算属性
const selectedCount = computed(() => selectedItems.value.length);
const hasSelected = computed(() => selectedCount.value > 0);

// 工具函数
const formatAmount = (amount: number | string) => {
  if (!amount) return "0.00";
  return Number(amount).toLocaleString("zh-CN", {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  });
};

const getStatusType = (
  status: string
): "success" | "warning" | "info" | "primary" | "danger" => {
  const statusMap: Record<
    string,
    "success" | "warning" | "info" | "primary" | "danger"
  > = {
    pending: "warning",
    processing: "primary",
    completed: "success",
    cancelled: "danger"
  };
  return statusMap[status] || "info";
};

const getStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    pending: "待处理",
    processing: "处理中",
    completed: "已完成",
    cancelled: "已取消"
  };
  return statusMap[status] || status || "未知";
};

const isSelected = (row: SalesOrderInfo) => {
  return selectedItems.value.some(item => item.id === row.id);
};

// 数据加载方法
const getTableData = async () => {
  loading.value = true;
  try {
    const params = {
      page: {
        page: pagination.value.page,
        limit: pagination.value.size
      },
      options: {
        order_by: "created_at",
        desc: true
      },
      params: [
        {
          var: "contract_id",
          val: contractId.value
        },
        {
          var: "repayment_id",
          val: "repayment:None",
          operator: "=" // 空值表示未关联还款计划
        }
      ]
    };

    // 添加搜索条件
    if (queryParams.value.name) {
      params.params.push({
        var: "platform_order_serial",
        val: queryParams.value.name,
        operator: "like"
      });
    }

    const { data } = await getSalesOrderList(params);
    if (data) {
      tableData.value = data.data || [];
      pagination.value.total = data.total || 0;
    }
  } catch (error) {
    console.error("获取订单列表失败:", error);
    ElMessage.error("获取订单列表失败");
    tableData.value = [];
  } finally {
    loading.value = false;
  }
};

// 事件处理方法
const handleSearch = () => {
  pagination.value.page = 1;
  getTableData();
};

const resetSearch = () => {
  queryParams.value.name = "";
  pagination.value.page = 1;
  getTableData();
};

const handlePageSizeChange = (size: number) => {
  pagination.value.size = size;
  pagination.value.page = 1;
  getTableData();
};

const handlePageCurrentChange = (page: number) => {
  pagination.value.page = page;
  getTableData();
};

const handleSelectionChange = (selection: SalesOrderInfo[]) => {
  selectedItems.value = selection;
};

const handleRowSelect = (
  row: SalesOrderInfo,
  checked: boolean | string | number
) => {
  const isChecked = Boolean(checked);
  if (isChecked) {
    if (!isSelected(row)) {
      selectedItems.value.push(row);
    }
  } else {
    const index = selectedItems.value.findIndex(item => item.id === row.id);
    if (index > -1) {
      selectedItems.value.splice(index, 1);
    }
  }
};

const handleSelectSingleItem = (row: SalesOrderInfo) => {
  if (!isSelected(row)) {
    selectedItems.value.push(row);
  }
};

const handleSelectItem = () => {
  if (selectedItems.value.length === 0) {
    ElMessage.warning("请选择要关联的订单");
    return;
  }

  emit("handleSelectItem", [...selectedItems.value]);
  handleCancel();
};

const handleCancel = () => {
  visible.value = false;
  selectedItems.value = [];
  queryParams.value.name = "";
  pagination.value.page = 1;
};

// 公开方法
const show = () => {
  selectedItems.value = [];
  pagination.value.page = 1;
  queryParams.value.name = "";
  visible.value = true;
  getTableData();
};

// 暴露方法
defineExpose({
  show
});
</script>

<style lang="scss" scoped>
:deep(.el-dialog__body) {
  padding: 20px;
}

:deep(.el-dialog__footer) {
  padding: 15px 20px;
}
</style>
