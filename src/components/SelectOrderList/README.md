# SelectOrderList 组件规格说明

## 概述
SelectOrderList 是一个用于批量选择订单的对话框组件，主要用于还款计划关联订单功能。

## 组件位置
```
src/components/SelectOrderList/
├── SelectItemDialog.vue    # 主组件文件
├── index.ts               # 导出文件
└── README.md              # 说明文档
```

## 组件规格

### Props
| 属性名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| contract-id | string | 是 | - | 合同ID，用于过滤相关订单 |
| url-list | string | 否 | '/api/sales_order/list' | 订单列表API地址 |
| multiple | boolean | 否 | true | 是否支持多选 |
| exclude-ids | string[] | 否 | [] | 排除的订单ID列表 |

### Events
| 事件名 | 参数 | 说明 |
|--------|------|------|
| select | selectedOrders: SalesOrderInfo[] | 选择完成事件，返回选中的订单列表 |
| cancel | - | 取消选择事件 |

### Methods
| 方法名 | 参数 | 返回值 | 说明 |
|--------|------|--------|------|
| show | - | void | 显示选择对话框 |
| hide | - | void | 隐藏选择对话框 |
| reset | - | void | 重置选择状态 |

## 使用示例

### 基本用法
```vue
<template>
  <div>
    <el-button @click="handleSelectOrders">选择订单</el-button>
    
    <SelectOrderList 
      ref="selectOrderListRef"
      :contract-id="contractId"
      @select="handleOrdersSelected"
      @cancel="handleSelectCancel"
    />
  </div>
</template>

<script setup>
import SelectOrderList from '@/components/SelectOrderList';

const selectOrderListRef = ref();
const contractId = ref('contract-123');

const handleSelectOrders = () => {
  selectOrderListRef.value?.show();
};

const handleOrdersSelected = (selectedOrders) => {
  console.log('选中的订单:', selectedOrders);
  // 处理选中的订单
};

const handleSelectCancel = () => {
  console.log('取消选择');
};
</script>
```

### 高级用法
```vue
<SelectOrderList 
  ref="selectOrderListRef"
  :contract-id="contractId"
  :url-list="'/api/custom_order/list'"
  :exclude-ids="excludeOrderIds"
  @select="handleOrdersSelected"
/>
```

## 数据结构

### SalesOrderInfo 类型
```typescript
interface SalesOrderInfo {
  id: string;
  platform_order_serial: string;  // 订单编号
  total_payment: number;           // 订单金额
  platform_fee_total: number;     // 平台费用
  delivery_time: string;           // 发货时间
  sign_time: string;               // 签收时间
  complete_time: string;           // 完成时间
  // ... 其他字段
}
```

## API 接口

### 订单列表接口
- **URL**: `/api/sales_order/list`
- **方法**: POST
- **参数**:
```typescript
{
  page: {
    page: number;
    limit: number;
  },
  options?: {
    order_by?: string;
    desc?: boolean;
  },
  params?: Array<{
    var: string;
    val: any;
  }>
}
```

### 过滤条件示例
```typescript
// 按合同ID过滤
{
  var: "contract_id",
  val: "contract-123"
}

// 排除已关联的订单
{
  var: "repayment_id",
  val: "",
  operator: "="  // 空值表示未关联
}
```

## UI 设计要求

### 对话框规格
- 宽度：800px
- 高度：600px
- 标题：选择订单
- 位置：居中显示

### 表格功能
- 支持分页
- 支持搜索（订单编号、金额范围）
- 支持多选/单选
- 显示字段：订单编号、订单金额、平台费用、发货时间、签收时间

### 操作按钮
- 确定：提交选中的订单
- 取消：关闭对话框
- 重置：清空选择

## 实现优先级
1. **高优先级**：基本的订单列表展示和选择功能
2. **中优先级**：搜索和过滤功能
3. **低优先级**：高级筛选和排序功能

## 注意事项
1. 组件应该是响应式的，支持不同屏幕尺寸
2. 需要处理加载状态和错误状态
3. 选择的订单数据需要包含完整的订单信息
4. 支持键盘操作（ESC关闭、Enter确认）
5. 需要防止重复提交

## 测试用例
1. 正常选择单个订单
2. 正常选择多个订单
3. 取消选择操作
4. 空数据状态
5. 加载错误状态
6. 分页功能测试
7. 搜索功能测试
