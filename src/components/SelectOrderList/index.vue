<template>
  <div class="select-order-list">
    <el-input
      :model-value="displayValue"
      :placeholder="label"
      :class="className"
      readonly
    >
      <template #append>
        <el-button v-if="canEdit" type="primary" @click="showSelectDialog">
          选择
        </el-button>
      </template>
    </el-input>

    <SelectItemDialog
      ref="selectItemDialogRef"
      :url-list="url"
      :contract-id="contractId"
      @handle-select-item="handleSelectItem"
    />
  </div>
</template>

<script setup lang="ts">
import type { SalesOrderInfo } from "@/api/salesOrder";
import { computed, ref, toRefs } from "vue";
import SelectItemDialog from "./SelectItemDialog.vue";

defineOptions({
  name: "SelectOrderList"
});

// Props
const props = defineProps<{
  selectItem?: SalesOrderInfo | SalesOrderInfo[];
  label?: string;
  url: string;
  contractId: string;
  className?: string;
  canEdit?: boolean;
  selection?: "single" | "multiple";
}>();

const {
  selectItem,
  label = "选择订单",
  className = "",
  selection = "multiple",
  canEdit = true,
  url,
  contractId
} = toRefs(props);

// Emits
const emit = defineEmits<{
  "update:selectItem": [value: SalesOrderInfo | SalesOrderInfo[]];
}>();

// 响应式数据
const selectItemDialogRef = ref();

// 计算属性
const displayValue = computed(() => {
  if (!selectItem?.value) {
    return "未选择相关订单";
  }

  if (selection.value === "multiple") {
    const items = Array.isArray(selectItem.value)
      ? selectItem.value
      : [selectItem.value];
    if (items.length === 0) {
      return "未选择相关订单";
    }

    const serials = items
      .map(item => item.platform_order_serial || item.serial || item.id)
      .filter(Boolean)
      .join(", ");

    return serials || `已选择 ${items.length} 个订单`;
  } else {
    const item = Array.isArray(selectItem.value)
      ? selectItem.value[0]
      : selectItem.value;
    if (!item) {
      return "未选择相关订单";
    }

    const serial = item.platform_order_serial || item.serial;
    const name = item.platform_name || "";

    if (serial && name) {
      return `${serial}：${name}`;
    } else if (serial) {
      return serial;
    } else {
      return "已选择订单";
    }
  }
});

// 方法
const showSelectDialog = () => {
  if (selectItemDialogRef.value) {
    selectItemDialogRef.value.show();
  }
};

const handleSelectItem = (selectedItems: SalesOrderInfo[]) => {
  if (selection.value === "multiple") {
    emit("update:selectItem", selectedItems);
  } else {
    // 单选模式，只取第一个
    const item = selectedItems.length > 0 ? selectedItems[0] : undefined;
    emit("update:selectItem", item);
  }
};

// 暴露方法
defineExpose({
  show: showSelectDialog
});
</script>

<style lang="scss" scoped>
.select-order-list {
  width: 100%;
}

:deep(.el-input-group__append) {
  padding: 0;
}

:deep(.el-input-group__append .el-button) {
  border-radius: 0;
  border-left: none;
}
</style>
