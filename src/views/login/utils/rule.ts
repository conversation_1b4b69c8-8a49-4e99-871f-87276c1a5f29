import type { FormRules } from "element-plus";
import { reactive } from "vue";

/** 密码正则（密码格式应为4-18位） */
export const REGEXP_PWD = /^.{4,18}$/;

/** 登录校验 */
const loginRules = reactive<FormRules>({
  password: [
    {
      validator: (rule, value, callback) => {
        if (value === "") {
          callback(new Error("请输入密码"));
        } else if (!REGEXP_PWD.test(value)) {
          callback(new Error("密码长度应为4-18位"));
        } else {
          callback();
        }
      },
      trigger: "blur"
    }
  ],
  captcha_str: [
    {
      required: true,
      message: "请输入验证码",
      trigger: "blur"
    }
  ]
});

export { loginRules };
