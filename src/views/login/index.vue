<script setup lang="ts">
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import { useDataThemeChange } from "@/layout/hooks/useDataThemeChange";
import { useLayout } from "@/layout/hooks/useLayout";
import { useNav } from "@/layout/hooks/useNav";
import { getTopMenu, initRouter } from "@/router/utils";
import { useUserStoreHook } from "@/store/modules/user";
import { message } from "@/utils/message";
import { debounce } from "@pureadmin/utils";
import { useEventListener } from "@vueuse/core";
import type { FormInstance } from "element-plus";
import { onMounted, reactive, ref, toRaw } from "vue";
import { useRouter } from "vue-router";
import Motion from "./utils/motion";
import { loginRules } from "./utils/rule";
import { avatar, bg, illustration } from "./utils/static";

import darkIcon from "@/assets/svg/dark.svg?component";
import dayIcon from "@/assets/svg/day.svg?component";
import Lock from "~icons/ri/lock-fill";
import User from "~icons/ri/user-3-fill";

defineOptions({
  name: "Login"
});

const router = useRouter();
const loading = ref(false);
const disabled = ref(false);
const ruleFormRef = ref<FormInstance>();
const captchaImage = ref("");

const { initStorage } = useLayout();
initStorage();

const { dataTheme, overallStyle, dataThemeChange } = useDataThemeChange();
dataThemeChange(overallStyle.value);
const { title } = useNav();

const ruleForm = reactive({
  username: "",
  password: "",
  captcha_str: "",
  captcha_id: ""
});

// 获取验证码
const getCaptcha = async () => {
  try {
    // 动态导入API函数
    const { getCaptcha: getCaptchaApi } = await import("@/api/user");
    const response = await getCaptchaApi();
    if (response.code === 200) {
      captchaImage.value = response.data.captcha_img;
      ruleForm.captcha_id = response.data.captcha_id;
    }
  } catch (error) {
    console.error("获取验证码失败:", error);
    message("获取验证码失败", { type: "error" });
  }
};

// 页面加载时获取验证码
onMounted(() => {
  getCaptcha();
});

const onLogin = async (formEl: FormInstance | undefined) => {
  if (!formEl) return;
  await formEl.validate(valid => {
    if (valid) {
      loading.value = true;
      useUserStoreHook()
        .loginByUsername({
          username: ruleForm.username,
          password: ruleForm.password,
          captcha_str: ruleForm.captcha_str,
          captcha_id: ruleForm.captcha_id
        })
        .then(res => {
          if (res.code === 200) {
            // 获取后端路由
            return initRouter().then(() => {
              disabled.value = true;
              router
                .push(getTopMenu(true).path)
                .then(() => {
                  message("登录成功", { type: "success" });
                })
                .finally(() => (disabled.value = false));
            });
          } else {
            message(res.msg || "登录失败", { type: "error" });
            // 登录失败时重新获取验证码
            ruleForm.captcha_str = "";
            getCaptcha();
          }
        })
        .finally(() => (loading.value = false));
    }
  });
};

const immediateDebounce: any = debounce(
  formRef => onLogin(formRef),
  1000,
  true
);

useEventListener(document, "keydown", ({ code }) => {
  if (
    ["Enter", "NumpadEnter"].includes(code) &&
    !disabled.value &&
    !loading.value
  )
    immediateDebounce(ruleFormRef.value);
});
</script>

<template>
  <div class="select-none">
    <img :src="bg" class="wave" />
    <div class="flex-c absolute right-5 top-3">
      <!-- 主题 -->
      <el-switch
        v-model="dataTheme"
        inline-prompt
        :active-icon="dayIcon"
        :inactive-icon="darkIcon"
        @change="dataThemeChange"
      />
    </div>
    <div class="login-container">
      <div class="img">
        <component :is="toRaw(illustration)" />
      </div>
      <div class="login-box">
        <div class="login-form">
          <avatar class="avatar" />
          <Motion>
            <h2 class="outline-hidden">{{ title }}</h2>
          </Motion>

          <el-form
            ref="ruleFormRef"
            :model="ruleForm"
            :rules="loginRules"
            size="large"
          >
            <Motion :delay="100">
              <el-form-item
                :rules="[
                  {
                    required: true,
                    message: '请输入账号',
                    trigger: 'blur'
                  }
                ]"
                prop="username"
              >
                <el-input
                  v-model="ruleForm.username"
                  clearable
                  placeholder="账号"
                  :prefix-icon="useRenderIcon(User)"
                />
              </el-form-item>
            </Motion>

            <Motion :delay="150">
              <el-form-item prop="password">
                <el-input
                  v-model="ruleForm.password"
                  clearable
                  show-password
                  placeholder="密码"
                  :prefix-icon="useRenderIcon(Lock)"
                />
              </el-form-item>
            </Motion>

            <Motion :delay="200">
              <el-form-item prop="captcha_str">
                <div class="flex w-full gap-2">
                  <el-input
                    v-model="ruleForm.captcha_str"
                    clearable
                    placeholder="验证码"
                    class="flex-1"
                  />
                  <div
                    class="w-28 h-10 border border-gray-300 rounded cursor-pointer flex items-center justify-center bg-gray-50 hover:bg-gray-100"
                    @click="getCaptcha"
                  >
                    <img
                      v-if="captchaImage"
                      :src="captchaImage"
                      class="w-full h-full object-cover rounded"
                      alt="验证码"
                    />
                    <span v-else class="text-xs text-gray-500">获取验证码</span>
                  </div>
                </div>
              </el-form-item>
            </Motion>

            <Motion :delay="250">
              <el-button
                class="w-full mt-4!"
                size="default"
                type="primary"
                :loading="loading"
                :disabled="disabled"
                @click="onLogin(ruleFormRef)"
              >
                登录
              </el-button>
            </Motion>
          </el-form>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
@import url("@/style/login.css");
</style>

<style lang="scss" scoped>
:deep(.el-input-group__append, .el-input-group__prepend) {
  padding: 0;
}
</style>
