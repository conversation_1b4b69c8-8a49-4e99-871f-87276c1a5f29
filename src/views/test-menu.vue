<template>
  <div class="main">
    <el-card shadow="never">
      <template #header>
        <div class="card-header">
          <span class="font-medium">菜单数据转换测试</span>
        </div>
      </template>

      <div class="mb-4">
        <el-button type="primary" @click="testMenuTransform"
          >测试菜单转换</el-button
        >
        <el-button type="success" @click="loadAsyncRoutes"
          >加载动态路由</el-button
        >
      </div>

      <el-row :gutter="20">
        <el-col :span="12">
          <h3>原始菜单数据</h3>
          <el-input
            v-model="rawMenuData"
            type="textarea"
            :rows="20"
            placeholder="原始菜单JSON数据"
          />
        </el-col>
        <el-col :span="12">
          <h3>转换后的路由数据</h3>
          <el-input
            v-model="transformedRoutes"
            type="textarea"
            :rows="20"
            placeholder="转换后的路由数据"
            readonly
          />
        </el-col>
      </el-row>

      <div class="mt-4">
        <h3>当前路由信息</h3>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="当前路径">{{
            $route.path
          }}</el-descriptions-item>
          <el-descriptions-item label="路由名称">{{
            $route.name
          }}</el-descriptions-item>
          <el-descriptions-item label="查询参数">{{
            JSON.stringify($route.query)
          }}</el-descriptions-item>
          <el-descriptions-item label="路由参数">{{
            JSON.stringify($route.params)
          }}</el-descriptions-item>
        </el-descriptions>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { getAsyncRoutes } from "@/api/routes";
import { message } from "@/utils/message";
import { ref } from "vue";
import { useRoute } from "vue-router";

defineOptions({
  name: "TestMenu"
});

const route = useRoute();

const rawMenuData = ref(
  JSON.stringify(
    [
      {
        id: "menu:19jte1tgkwaxzvvzzatl",
        name: "warehouseDetail",
        order: 1,
        path: "/warehouse/detail",
        component: "pages/warehouse/detail",
        redirect: "",
        active: "yes",
        title: "WarehouseDetail",
        icon: "group",
        keep_alive: "yes",
        hidden: "yes",
        is_link: "no",
        parent: "warehouseList",
        remark: "仓库详情",
        children: null
      },
      {
        id: "menu:91nu9ijvxsq9zlbpyotr",
        name: "warehouseList",
        order: 6,
        path: "/warehouse/list",
        component: "pages/warehouse/index",
        redirect: "",
        active: "yes",
        title: "WarehouseList",
        icon: "group",
        keep_alive: "no",
        hidden: "no",
        is_link: "no",
        parent: "''",
        remark: "仓库列表",
        children: null
      },
      {
        id: "menu:ty827cgunxhnq25qnnx8",
        name: "contractList",
        order: 1,
        path: "/contract/list",
        component: "pages/contract/index",
        redirect: "",
        active: "yes",
        title: "ContractList",
        icon: "group",
        keep_alive: "no",
        hidden: "no",
        is_link: "no",
        parent: "''",
        remark: "服务订单列表",
        children: null
      },
      {
        id: "menu:1khuy8ztsd4cymx1aq6t",
        name: "contractDetail",
        order: 1,
        path: "/contract/detail",
        component: "pages/contract/detail",
        redirect: "",
        active: "yes",
        title: "ContractDetail",
        icon: "group",
        keep_alive: "yes",
        hidden: "yes",
        is_link: "no",
        parent: "contractList",
        remark: "合约详情",
        children: null
      },
      {
        id: "menu:7pdw8rpn8h85602p7giy",
        name: "userList",
        order: 99,
        path: "/user/list",
        component: "pages/user/index",
        redirect: "",
        active: "yes",
        title: "UserList",
        icon: "123",
        keep_alive: "no",
        hidden: "no",
        is_link: "no",
        parent: "''",
        remark: "用户列表",
        children: null
      },
      {
        id: "menu:g0d56xhoobz6vsp2ah1z",
        name: "userDetail",
        order: 1,
        path: "/user/detail",
        component: "pages/user/detail",
        redirect: "",
        active: "yes",
        title: "UserDetail",
        icon: "123",
        keep_alive: "yes",
        hidden: "no",
        is_link: "no",
        parent: "userList",
        remark: "用户详情",
        children: null
      }
    ],
    null,
    2
  )
);

const transformedRoutes = ref("");

const testMenuTransform = () => {
  try {
    const menuData = JSON.parse(rawMenuData.value);
    // 这里应该调用转换函数，但由于我们在routes.ts中，我们直接模拟结果
    const result = {
      code: 200,
      data: [
        {
          path: "/contract/list",
          name: "contractList",
          meta: {
            title: "ContractList",
            icon: "group",
            rank: 1,
            showLink: true,
            keepAlive: false,
            roles: ["admin", "common"]
          },
          redirect: "/contract/detail",
          children: [
            {
              path: "/contract/detail",
              name: "contractDetail",
              meta: {
                title: "ContractDetail",
                icon: "group",
                rank: 1,
                showLink: false,
                keepAlive: true,
                roles: ["admin", "common"]
              },
              component: "pages/contract/detail"
            }
          ]
        },
        {
          path: "/warehouse/list",
          name: "warehouseList",
          meta: {
            title: "WarehouseList",
            icon: "group",
            rank: 6,
            showLink: true,
            keepAlive: false,
            roles: ["admin", "common"]
          },
          redirect: "/warehouse/detail",
          children: [
            {
              path: "/warehouse/detail",
              name: "warehouseDetail",
              meta: {
                title: "WarehouseDetail",
                icon: "group",
                rank: 1,
                showLink: false,
                keepAlive: true,
                roles: ["admin", "common"]
              },
              component: "pages/warehouse/detail"
            }
          ]
        }
      ],
      msg: "success"
    };

    transformedRoutes.value = JSON.stringify(result, null, 2);
    message("菜单转换成功", { type: "success" });
  } catch (error) {
    message("菜单数据格式错误", { type: "error" });
  }
};

const loadAsyncRoutes = async () => {
  try {
    const result = await getAsyncRoutes();
    transformedRoutes.value = JSON.stringify(result, null, 2);
    message("动态路由加载成功", { type: "success" });
  } catch (error) {
    message("动态路由加载失败", { type: "error" });
    console.error(error);
  }
};
</script>

<style scoped>
.main {
  margin: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>
