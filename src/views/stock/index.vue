<template>
  <div class="main">
    <el-card shadow="never">
      <template #header>
        <div class="card-header">
          <span class="font-medium">库存列表</span>
        </div>
      </template>

      <div class="mb-4">
        <el-button
          type="primary"
          :icon="useRenderIcon('ep:plus')"
          @click="handleCreate"
        >
          添加库存
        </el-button>
      </div>

      <pure-table
        ref="tableRef"
        row-key="id"
        align-whole="center"
        showOverflowTooltip
        :loading="loading"
        :data="dataList"
        :columns="columns"
        :pagination="pagination"
        @page-size-change="onSizeChange"
        @page-current-change="onCurrentChange"
      >
        <template #created_at="{ row }">
          <span>{{ formatDateTime(row.created_at) }}</span>
        </template>
        <template #operation="{ row }">
          <el-dropdown trigger="click">
            <el-button type="primary" size="small">
              操作<el-icon class="el-icon--right"><ArrowDown /></el-icon>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item :icon="View" @click="handleView(row)">
                  查看
                </el-dropdown-item>
                <el-dropdown-item :icon="Edit" @click="handleEdit(row)">
                  编辑
                </el-dropdown-item>
                <el-dropdown-item :icon="Delete" @click="confirmDelete(row)">
                  删除
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </template>
      </pure-table>

      <!-- 库存表单抽屉 -->
      <StockForm
        v-model="showForm"
        :stock-id="currentStockId"
        @success="handleFormSuccess"
      />
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { deleteStock, getStockList, type StockInfo } from "@/api/stock";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import { formatDateTime } from "@/utils/formatTime";
import { ArrowDown, Delete, Edit, View } from "@element-plus/icons-vue";
import { PureTable } from "@pureadmin/table";
import { ElMessage, ElMessageBox } from "element-plus";
import { computed, onMounted, reactive, ref } from "vue";
import { useRouter } from "vue-router";
import StockForm from "./modules/StockForm.vue";

defineOptions({
  name: "StockList"
});

const router = useRouter();
const tableRef = ref();

// 响应式数据
const loading = ref(false);
const dataList = ref<StockInfo[]>([]);
const showForm = ref(false);
const currentStockId = ref("");

// 分页配置
const pagination = reactive({
  total: 0,
  pageSize: 10,
  currentPage: 1,
  background: true,
  pageSizes: [10, 20, 50, 100]
});

// 表格列配置
const columns = computed(() => [
  {
    label: "SKU编号",
    prop: "sku_serial",
    minWidth: 150
  },
  {
    label: "仓库",
    prop: "warehouse",
    minWidth: 120
  },
  {
    label: "位置",
    prop: "position",
    minWidth: 120
  },
  {
    label: "公司",
    prop: "company",
    minWidth: 120
  },
  {
    label: "数量",
    prop: "quantity",
    minWidth: 100
  },
  {
    label: "位置信息",
    prop: "location",
    minWidth: 150,
    formatter: (row: StockInfo) => {
      return row.location ? row.location.join(", ") : "-";
    }
  },
  {
    label: "备注",
    prop: "remark",
    minWidth: 150
  },
  {
    label: "创建时间",
    prop: "created_at",
    minWidth: 160,
    slot: "created_at"
  },
  {
    label: "操作",
    fixed: "right",
    width: 100,
    slot: "operation"
  }
]);

// 获取库存列表数据
const getStockListData = async () => {
  try {
    loading.value = true;
    const params = {
      options: {
        order_by: "created_at",
        desc: true
      },
      page: {
        page: pagination.currentPage,
        limit: pagination.pageSize
      },
      params: []
    };

    const response = await getStockList(params);
    if (response.code === 200) {
      dataList.value = response.data.data || [];
      pagination.total = response.data.total || 0;
    } else {
      ElMessage.error(response.msg || "获取库存列表失败");
    }
  } catch (error) {
    console.error("获取库存列表失败:", error);
    ElMessage.error("获取库存列表失败");
  } finally {
    loading.value = false;
  }
};

// 分页事件处理
const onSizeChange = (size: number) => {
  pagination.pageSize = size;
  getStockListData();
};

const onCurrentChange = (page: number) => {
  pagination.currentPage = page;
  getStockListData();
};

// 操作方法
const handleCreate = () => {
  currentStockId.value = "";
  showForm.value = true;
};

const handleView = (row: StockInfo) => {
  router.push({
    path: "/stock/detail",
    query: { id: row.id }
  });
};

const handleEdit = (row: StockInfo) => {
  currentStockId.value = row.id;
  showForm.value = true;
};

const confirmDelete = (row: StockInfo) => {
  ElMessageBox.confirm(`是否确认删除库存记录？`, "删除确认", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning"
  })
    .then(() => {
      handleDelete(row);
    })
    .catch(() => {
      // 用户取消删除
    });
};

const handleDelete = async (row: StockInfo) => {
  try {
    const response = await deleteStock(row.id);
    if (response.code === 200) {
      ElMessage.success("删除成功");
      getStockListData();
    } else {
      ElMessage.error(response.msg || "删除失败");
    }
  } catch (error) {
    console.error("删除库存失败:", error);
    ElMessage.error("删除失败");
  }
};

// 表单成功回调
const handleFormSuccess = () => {
  getStockListData();
};

// 初始化
onMounted(() => {
  getStockListData();
});
</script>

<style scoped>
.main {
  margin: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.text-right {
  text-align: right;
}

.text-red-500 {
  color: #ef4444;
}

.text-orange-500 {
  color: #f97316;
}

.text-green-500 {
  color: #22c55e;
}

.font-bold {
  font-weight: bold;
}
</style>
