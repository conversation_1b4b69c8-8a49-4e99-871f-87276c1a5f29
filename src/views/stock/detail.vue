<template>
  <div class="main">
    <el-card shadow="never">
      <template #header>
        <div class="flex justify-between items-center">
          <h2 class="text-xl font-bold">库存详情</h2>
          <el-button :icon="useRenderIcon('ep:back')" @click="goBack">
            返回列表
          </el-button>
        </div>
      </template>

      <div v-if="stockDetail?.id" class="stock-detail">
        <!-- 基本信息 -->
        <el-descriptions title="基本信息" :column="3" border class="mb-6">
          <el-descriptions-item label="SKU编号">
            {{ stockDetail.sku_serial }}
          </el-descriptions-item>
          <el-descriptions-item label="仓库">
            {{ stockDetail.warehouse }}
          </el-descriptions-item>
          <el-descriptions-item label="位置">
            {{ stockDetail.position }}
          </el-descriptions-item>
          <el-descriptions-item label="公司">
            {{ stockDetail.company || "-" }}
          </el-descriptions-item>
          <el-descriptions-item label="数量">
            {{ stockDetail.quantity }}
          </el-descriptions-item>
          <el-descriptions-item label="位置信息">
            {{ stockDetail.location ? stockDetail.location.join(", ") : "-" }}
          </el-descriptions-item>
          <el-descriptions-item label="创建时间">
            {{ formatDateTime(stockDetail.created_at) }}
          </el-descriptions-item>
          <el-descriptions-item label="更新时间">
            {{ formatDateTime(stockDetail.updated_at) }}
          </el-descriptions-item>
        </el-descriptions>

        <!-- 备注信息 -->
        <el-descriptions title="备注信息" :column="1" border>
          <el-descriptions-item label="备注内容">
            <div class="remark-content">
              {{ stockDetail.remark || "暂无备注" }}
            </div>
          </el-descriptions-item>
        </el-descriptions>
      </div>

      <el-empty v-else description="库存信息不存在" />
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { getStock, type StockInfo } from "@/api/stock";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import { formatDateTime } from "@/utils/formatTime";
import { ElMessage } from "element-plus";
import { onMounted, ref } from "vue";
import { useRoute, useRouter } from "vue-router";

defineOptions({
  name: "StockDetail"
});

const router = useRouter();
const route = useRoute();

const stockDetail = ref<StockInfo>({} as StockInfo);
const loading = ref(false);

// 返回列表
const goBack = () => {
  router.push({ path: "/stock/list" });
};

// 获取库存详情
const getStockDetail = async () => {
  try {
    loading.value = true;
    const response = await getStock(route.query.id as string);
    if (response.code === 200) {
      stockDetail.value = response.data;
    } else {
      ElMessage.error(response.msg || "获取库存详情失败");
    }
  } catch (error) {
    console.error("获取库存详情失败:", error);
    ElMessage.error("获取库存详情失败");
  } finally {
    loading.value = false;
  }
};

// 初始化
onMounted(() => {
  if (route.query.id) {
    getStockDetail();
  } else {
    ElMessage.error("缺少库存ID参数");
    goBack();
  }
});
</script>

<style lang="scss" scoped>
.main {
  margin: 20px;
}

.stock-detail {
  .mb-6 {
    margin-bottom: 24px;
  }

  .remark-content {
    white-space: pre-wrap;
    word-break: break-word;
    line-height: 1.6;
  }
}
</style>
