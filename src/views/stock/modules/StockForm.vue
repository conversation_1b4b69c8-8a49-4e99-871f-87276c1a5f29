<template>
  <el-drawer
    v-model="visible"
    :title="isEdit ? '编辑库存' : '添加库存'"
    direction="rtl"
    size="600px"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @close="handleClose"
  >
    <template #default>
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="120px"
        label-position="left"
      >
        <el-form-item label="SKU编号" prop="sku_serial">
          <el-input
            v-model="formData.sku_serial"
            placeholder="请输入SKU编号"
            clearable
          />
        </el-form-item>

        <el-form-item label="仓库" prop="warehouse">
          <el-input
            v-model="formData.warehouse"
            placeholder="请输入仓库"
            clearable
          />
        </el-form-item>

        <el-form-item label="位置" prop="position">
          <el-input
            v-model="formData.position"
            placeholder="请输入位置"
            clearable
          />
        </el-form-item>

        <el-form-item label="公司" prop="company">
          <el-input
            v-model="formData.company"
            placeholder="请输入公司"
            clearable
          />
        </el-form-item>

        <el-form-item label="数量" prop="quantity">
          <el-input-number
            v-model="formData.quantity"
            :min="0"
            :precision="2"
            placeholder="请输入数量"
            style="width: 100%"
          />
        </el-form-item>

        <el-form-item label="位置信息" prop="location">
          <el-select
            v-model="formData.location"
            multiple
            filterable
            allow-create
            placeholder="请选择或输入位置信息"
            style="width: 100%"
          >
            <el-option
              v-for="item in locationOptions"
              :key="item"
              :label="item"
              :value="item"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="备注" prop="remark">
          <el-input
            v-model="formData.remark"
            type="textarea"
            :rows="4"
            placeholder="请输入备注信息"
          />
        </el-form-item>
      </el-form>
    </template>

    <template #footer>
      <div class="flex justify-end gap-4">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" :loading="loading" @click="handleSubmit">
          {{ isEdit ? "更新" : "创建" }}
        </el-button>
      </div>
    </template>
  </el-drawer>
</template>

<script setup lang="ts">
import {
  createStock,
  getStock,
  updateStock,
  type StockCreateRequest,
  type StockUpdateRequest
} from "@/api/stock";
import { ElMessage, type FormInstance, type FormRules } from "element-plus";
import { reactive, ref, watch } from "vue";

// Props
interface Props {
  modelValue: boolean;
  stockId?: string;
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: false,
  stockId: ""
});

// Emits
const emit = defineEmits<{
  "update:modelValue": [value: boolean];
  success: [];
}>();

// 响应式数据
const visible = ref(false);
const loading = ref(false);
const formRef = ref<FormInstance>();
const isEdit = ref(false);

// 位置选项
const locationOptions = ref([
  "A区-1层",
  "A区-2层",
  "B区-1层",
  "B区-2层",
  "C区-1层",
  "C区-2层"
]);

// 表单数据
const formData = reactive<StockCreateRequest>({
  sku_serial: "",
  warehouse: "",
  position: "",
  company: "",
  quantity: 0,
  location: [],
  remark: ""
});

// 表单验证规则
const formRules: FormRules = {
  sku_serial: [{ required: true, message: "请输入SKU编号", trigger: "blur" }],
  warehouse: [{ required: true, message: "请输入仓库", trigger: "blur" }],
  position: [{ required: true, message: "请输入位置", trigger: "blur" }],
  quantity: [{ required: true, message: "请输入数量", trigger: "blur" }]
};

// 监听 modelValue 变化
watch(
  () => props.modelValue,
  newVal => {
    visible.value = newVal;
    if (newVal) {
      isEdit.value = !!props.stockId;
      if (isEdit.value) {
        getStockDetail();
      } else {
        resetForm();
      }
    }
  },
  { immediate: true }
);

// 监听 visible 变化
watch(visible, newVal => {
  emit("update:modelValue", newVal);
});

// 重置表单
const resetForm = () => {
  Object.assign(formData, {
    sku_serial: "",
    warehouse: "",
    position: "",
    company: "",
    quantity: 0,
    location: [],
    remark: ""
  });
  formRef.value?.clearValidate();
};

// 获取库存详情
const getStockDetail = async () => {
  if (!props.stockId) return;

  try {
    loading.value = true;
    const response = await getStock(props.stockId);
    if (response.code === 200) {
      const data = response.data;
      Object.assign(formData, {
        sku_serial: data.sku_serial || "",
        warehouse: data.warehouse || "",
        position: data.position || "",
        company: data.company || "",
        quantity: data.quantity || 0,
        location: data.location || [],
        remark: data.remark || ""
      });
    } else {
      ElMessage.error(response.msg || "获取库存信息失败");
    }
  } catch (error) {
    console.error("获取库存详情失败:", error);
    ElMessage.error("获取库存信息失败");
  } finally {
    loading.value = false;
  }
};

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return;

  try {
    const valid = await formRef.value.validate();
    if (!valid) return;

    loading.value = true;

    if (isEdit.value) {
      // 编辑
      const updateData: StockUpdateRequest = {
        id: props.stockId!,
        ...formData
      };
      const response = await updateStock(updateData);
      if (response.code === 200) {
        ElMessage.success("更新成功");
        emit("success");
        handleClose();
      } else {
        ElMessage.error(response.msg || "更新失败");
      }
    } else {
      // 创建
      const response = await createStock(formData);
      if (response.code === 200) {
        ElMessage.success("创建成功");
        emit("success");
        handleClose();
      } else {
        ElMessage.error(response.msg || "创建失败");
      }
    }
  } catch (error) {
    console.error("提交失败:", error);
    ElMessage.error("操作失败");
  } finally {
    loading.value = false;
  }
};

// 关闭抽屉
const handleClose = () => {
  visible.value = false;
  resetForm();
};
</script>

<style lang="scss" scoped>
:deep(.el-drawer__header) {
  margin-bottom: 20px;
}

:deep(.el-drawer__body) {
  padding: 0 20px;
}

:deep(.el-drawer__footer) {
  padding: 20px;
  border-top: 1px solid var(--el-border-color-light);
}
</style>
