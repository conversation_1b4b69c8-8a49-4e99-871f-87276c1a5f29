<script setup lang="ts">
import type { SupplierInfo } from "@/api/supplier";
import { reactive, ref, watch } from "vue";

// 声明 props 类型
export interface SupplierFormProps {
  formData: Partial<SupplierInfo>;
}

// 声明 props 默认值
const props = withDefaults(defineProps<SupplierFormProps>(), {});

// 创建本地响应式表单数据
const localFormData = reactive({
  id: "",
  serial: "",
  name: "",
  address: "",
  website: "",
  status: "on" as "on" | "off",
  order: 1
});

// 表单引用
const formRef = ref();

// 表单验证规则
const rules = {
  serial: [{ required: true, message: "请输入供应商编号", trigger: "blur" }],
  name: [{ required: true, message: "请输入供应商名称", trigger: "blur" }],
  status: [{ required: true, message: "请选择状态", trigger: "change" }]
};

// 状态选项
const statusOptions = [
  { label: "启用", value: "on" },
  { label: "停用", value: "off" }
];

// 监听props变化，同步到本地数据
watch(
  () => props.formData,
  (newData) => {
    Object.assign(localFormData, {
      id: newData.id || "",
      serial: newData.serial || "",
      name: newData.name || "",
      address: newData.address || "",
      website: newData.website || "",
      status: newData.status || "on",
      order: newData.order || 1
    });
  },
  { immediate: true, deep: true }
);

// 监听本地数据变化，同步回props
watch(
  localFormData,
  (newData) => {
    Object.assign(props.formData, newData);
  },
  { deep: true }
);

// 验证表单
const validateForm = () => {
  return new Promise(resolve => {
    formRef.value?.validate((valid: boolean) => {
      resolve(valid);
    });
  });
};

// 暴露验证方法给父组件
defineExpose({
  validateForm
});
</script>

<template>
  <div class="supplier-form">
    <el-form
      ref="formRef"
      :model="localFormData"
      :rules="rules"
      label-width="100px"
      label-position="left"
    >
      <el-form-item label="供应商编号" prop="serial">
        <el-input
          v-model="localFormData.serial"
          placeholder="请输入供应商编号"
          clearable
        />
      </el-form-item>
      
      <el-form-item label="供应商名称" prop="name">
        <el-input
          v-model="localFormData.name"
          placeholder="请输入供应商名称"
          clearable
        />
      </el-form-item>
      
      <el-form-item label="联系地址">
        <el-input
          v-model="localFormData.address"
          placeholder="请输入联系地址"
          clearable
        />
      </el-form-item>
      
      <el-form-item label="企业网址">
        <el-input
          v-model="localFormData.website"
          placeholder="请输入企业网址"
          clearable
        />
      </el-form-item>
      
      <el-form-item label="状态" prop="status">
        <el-radio-group v-model="localFormData.status">
          <el-radio
            v-for="option in statusOptions"
            :key="option.value"
            :label="option.value"
          >
            {{ option.label }}
          </el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>
  </div>
</template>

<style scoped>
.supplier-form {
  padding: 20px;
}
</style>
