import type {
  SupplierCreateRequest,
  SupplierInfo,
  SupplierUpdateRequest
} from "@/api/supplier";
import { createSupplier, updateSupplier } from "@/api/supplier";
import { addDrawer } from "@/components/ReDrawer";
import { message } from "@/utils/message";
import { cloneDeep } from "@pureadmin/utils";
import SupplierForm, { type SupplierFormProps } from "./SupplierForm.vue";

export interface UseSupplierFormDrawerOptions {
  onSuccess?: () => void;
}

/**
 * 打开供应商表单抽屉
 * @param supplierData 供应商数据（编辑时传入）
 * @param options 配置选项
 */
export function openSupplierFormDrawer(
  supplierData?: Partial<SupplierInfo>,
  options: UseSupplierFormDrawerOptions = {}
) {
  const { onSuccess } = options;
  const isEdit = !!supplierData?.id;

  // 准备表单数据
  const formData = {
    id: supplierData?.id || "",
    serial: supplierData?.serial || "",
    name: supplierData?.name || "",
    address: supplierData?.address || "",
    website: supplierData?.website || "",
    status: supplierData?.status || "on",
    order: supplierData?.order || 1
  };

  addDrawer({
    title: isEdit ? "编辑供应商" : "新增供应商",
    size: "500px",
    closeOnClickModal: false,
    contentRenderer: () => SupplierForm,
    props: {
      formData: cloneDeep(formData)
    },
    beforeSure: async (done, { options }) => {
      const { formData: currentFormData } = options.props as SupplierFormProps;

      try {
        if (isEdit) {
          // 编辑时确保有必需的字段
          const updateData: SupplierUpdateRequest = {
            id: currentFormData.id!,
            serial: currentFormData.serial,
            name: currentFormData.name,
            address: currentFormData.address,
            website: currentFormData.website,
            status: currentFormData.status,
            order: currentFormData.order
          };
          await updateSupplier(updateData);
          message("供应商更新成功", { type: "success" });
        } else {
          // 新增时确保有必需的字段
          const createData: SupplierCreateRequest = {
            serial: currentFormData.serial!,
            name: currentFormData.name!,
            address: currentFormData.address,
            website: currentFormData.website,
            status: currentFormData.status!,
            order: currentFormData.order
          };
          await createSupplier(createData);
          message("供应商创建成功", { type: "success" });
        }

        onSuccess?.();
        done(); // 关闭抽屉
      } catch (error) {
        console.error("提交失败:", error);
        message("操作失败，请重试", { type: "error" });
        // 不调用 done()，保持抽屉打开
      }
    }
  });
}

/**
 * 打开新增供应商抽屉
 */
export function openAddSupplierDrawer(
  options: UseSupplierFormDrawerOptions = {}
) {
  return openSupplierFormDrawer(undefined, options);
}

/**
 * 打开编辑供应商抽屉
 */
export function openEditSupplierDrawer(
  supplierData: SupplierInfo,
  options: UseSupplierFormDrawerOptions = {}
) {
  return openSupplierFormDrawer(supplierData, options);
}
