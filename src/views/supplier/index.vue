<template>
  <div class="main">
    <el-card shadow="never">
      <template #header>
        <div class="card-header">
          <span class="font-medium">供应商管理</span>
        </div>
      </template>

      <div class="mb-4">
        <el-button type="primary" @click="handleAdd">
          <template #icon>
            <IconifyIconOnline icon="ep:plus" />
          </template>
          新增供应商
        </el-button>
        <el-button @click="getSupplierListData">
          <template #icon>
            <IconifyIconOnline icon="ep:refresh" />
          </template>
          刷新
        </el-button>
      </div>

      <pure-table
        ref="tableRef"
        :data="tableData"
        :columns="columns"
        :pagination="pagination"
        :loading="loading"
        @page-size-change="handleSizeChange"
        @page-current-change="handleCurrentChange"
      >
        <template #status="{ row }">
          <el-tag :type="row.status === 'on' ? 'success' : 'danger'">
            {{ row.status === "on" ? "启用" : "停用" }}
          </el-tag>
        </template>

        <template #operation="{ row }">
          <el-button-group>
            <el-button size="small" type="primary" @click="handleEdit(row)">
              编辑
            </el-button>
            <el-button
              size="small"
              :type="row.status === 'on' ? 'warning' : 'success'"
              @click="handleStatusChange(row)"
            >
              {{ row.status === "on" ? "停用" : "启用" }}
            </el-button>
            <el-button size="small" type="danger" @click="handleDelete(row)">
              删除
            </el-button>
          </el-button-group>
        </template>
      </pure-table>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import {
  deleteSupplier,
  getSupplierList,
  updateSupplierStatus,
  type SupplierInfo,
  type SupplierListParams
} from "@/api/supplier";
import { message } from "@/utils/message";
import type { PaginationProps } from "@pureadmin/table";
import { PureTable } from "@pureadmin/table";
import { computed, onMounted, reactive, ref } from "vue";
import { openSupplierFormDrawer } from "./modules/useSupplierFormDrawer";

defineOptions({
  name: "SupplierList"
});

const tableRef = ref();
const loading = ref(false);
const tableData = ref<SupplierInfo[]>([]);

// 分页配置
const pagination = reactive<PaginationProps>({
  total: 0,
  pageSize: 10,
  currentPage: 1,
  background: true
});

// 表格列配置
const columns = computed(() => [
  {
    label: "供应商名称",
    prop: "name",
    minWidth: 150
  },
  {
    label: "供应商编号",
    prop: "serial",
    minWidth: 120
  },
  {
    label: "联系地址",
    prop: "address",
    minWidth: 200,
    showOverflowTooltip: true
  },
  {
    label: "企业网址",
    prop: "website",
    minWidth: 150,
    showOverflowTooltip: true
  },
  {
    label: "状态",
    prop: "status",
    minWidth: 80,
    slot: "status"
  },
  {
    label: "操作",
    fixed: "right",
    width: 200,
    slot: "operation"
  }
]);

// 获取供应商列表数据
const getSupplierListData = async () => {
  try {
    loading.value = true;
    const params: SupplierListParams = {
      options: {
        order_by: "created_at",
        desc: true
      },
      page: {
        page: pagination.currentPage,
        limit: pagination.pageSize
      },
      params: []
    };

    const response = await getSupplierList(params);
    if (response.code === 200) {
      tableData.value = response.data.data || [];
      pagination.total = response.data.total || 0;
    } else {
      message(response.msg || "获取供应商数据失败", { type: "error" });
    }
  } catch (error) {
    console.error("获取供应商数据失败:", error);
    message("获取供应商数据失败", { type: "error" });
  } finally {
    loading.value = false;
  }
};

// 处理新增供应商
const handleAdd = () => {
  openSupplierFormDrawer(undefined, {
    onSuccess: () => {
      getSupplierListData();
    }
  });
};

// 处理编辑供应商
const handleEdit = (row: SupplierInfo) => {
  openSupplierFormDrawer(row, {
    onSuccess: () => {
      getSupplierListData();
    }
  });
};

// 处理状态变更
const handleStatusChange = async (row: SupplierInfo) => {
  try {
    const newStatus = row.status === "on" ? "off" : "on";
    const response = await updateSupplierStatus({
      id: row.id,
      status: newStatus
    });

    if (response.code === 200) {
      message(`供应商${newStatus === "on" ? "启用" : "停用"}成功`, {
        type: "success"
      });
      getSupplierListData();
    } else {
      message(response.msg || "状态更新失败", { type: "error" });
    }
  } catch (error) {
    console.error("状态更新失败:", error);
    message("状态更新失败", { type: "error" });
  }
};

// 处理删除供应商
const handleDelete = async (row: SupplierInfo) => {
  try {
    const response = await deleteSupplier(row.id);
    if (response.code === 200) {
      message("删除供应商成功", { type: "success" });
      getSupplierListData();
    } else {
      message(response.msg || "删除供应商失败", { type: "error" });
    }
  } catch (error) {
    console.error("删除供应商失败:", error);
    message("删除供应商失败", { type: "error" });
  }
};

// 分页处理
const handleSizeChange = (val: number) => {
  pagination.pageSize = val;
  getSupplierListData();
};

const handleCurrentChange = (val: number) => {
  pagination.currentPage = val;
  getSupplierListData();
};

// 页面加载时获取数据
onMounted(() => {
  getSupplierListData();
});
</script>

<style scoped>
.main {
  margin: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.text-right {
  text-align: right;
}
</style>
