<template>
  <el-drawer
    v-model="visible"
    :title="isEdit ? '编辑公司' : '添加公司'"
    direction="rtl"
    size="600px"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @close="handleClose"
  >
    <template #default>
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="120px"
        label-position="left"
      >
        <el-form-item label="公司名称" prop="name">
          <el-input
            v-model="formData.name"
            placeholder="请输入公司名称"
            clearable
          />
        </el-form-item>

        <el-form-item label="公司编号" prop="serial">
          <el-input
            v-model="formData.serial"
            placeholder="请输入公司编号"
            clearable
          />
        </el-form-item>

        <el-form-item label="公司类型" prop="company_type">
          <el-select
            v-model="formData.company_type"
            placeholder="请选择公司类型"
            clearable
            style="width: 100%"
          >
            <el-option
              v-for="item in companyTypeDict"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="法人代表" prop="corporate">
          <el-input
            v-model="formData.corporate"
            placeholder="请输入法人代表"
            clearable
          />
        </el-form-item>

        <el-form-item label="社会信用代码" prop="social_code">
          <el-input
            v-model="formData.social_code"
            placeholder="请输入社会信用代码"
            clearable
          />
        </el-form-item>

        <el-form-item label="行业类型" prop="industry_type">
          <el-select
            v-model="formData.industry_type"
            placeholder="请选择行业类型"
            clearable
            style="width: 100%"
          >
            <el-option
              v-for="item in industryTypeDict"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="注册地址" prop="address">
          <el-input
            v-model="formData.address"
            type="textarea"
            :rows="3"
            placeholder="请输入注册地址"
          />
        </el-form-item>

        <el-form-item label="联系电话" prop="phone">
          <el-input
            v-model="formData.phone"
            placeholder="请输入联系电话"
            clearable
          />
        </el-form-item>

        <el-form-item label="邮箱地址" prop="email">
          <el-input
            v-model="formData.email"
            placeholder="请输入邮箱地址"
            clearable
          />
        </el-form-item>

        <el-form-item label="官网地址" prop="website">
          <el-input
            v-model="formData.website"
            placeholder="请输入官网地址"
            clearable
          />
        </el-form-item>

        <el-form-item label="公司状态" prop="status">
          <el-select
            v-model="formData.status"
            placeholder="请选择公司状态"
            clearable
            style="width: 100%"
          >
            <el-option
              v-for="item in companyStatusDict"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="公司描述" prop="description">
          <el-input
            v-model="formData.description"
            type="textarea"
            :rows="4"
            placeholder="请输入公司描述"
          />
        </el-form-item>
      </el-form>
    </template>

    <template #footer>
      <div class="flex justify-end gap-4">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" :loading="loading" @click="handleSubmit">
          {{ isEdit ? "更新" : "创建" }}
        </el-button>
      </div>
    </template>
  </el-drawer>
</template>

<script setup lang="ts">
import {
  companyStatusDict,
  companyTypeDict,
  createCompany,
  getCompany,
  industryTypeDict,
  updateCompany,
  type CompanyCreateRequest,
  type CompanyUpdateRequest
} from "@/api/company";
import { ElMessage, type FormInstance, type FormRules } from "element-plus";
import { reactive, ref, watch } from "vue";

// Props
interface Props {
  modelValue: boolean;
  companyId?: string;
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: false,
  companyId: ""
});

// Emits
const emit = defineEmits<{
  "update:modelValue": [value: boolean];
  success: [];
}>();

// 响应式数据
const visible = ref(false);
const loading = ref(false);
const formRef = ref<FormInstance>();
const isEdit = ref(false);

// 表单数据
const formData = reactive<CompanyCreateRequest>({
  name: "",
  serial: "",
  company_type: "",
  corporate: "",
  social_code: "",
  industry_type: "",
  address: "",
  phone: "",
  email: "",
  website: "",
  description: "",
  status: "active"
});

// 表单验证规则
const formRules: FormRules = {
  name: [{ required: true, message: "请输入公司名称", trigger: "blur" }],
  serial: [{ required: true, message: "请输入公司编号", trigger: "blur" }],
  email: [{ type: "email", message: "请输入正确的邮箱地址", trigger: "blur" }],
  phone: [
    {
      pattern: /^1[3-9]\d{9}$/,
      message: "请输入正确的手机号码",
      trigger: "blur"
    }
  ]
};

// 监听 modelValue 变化
watch(
  () => props.modelValue,
  newVal => {
    visible.value = newVal;
    if (newVal) {
      isEdit.value = !!props.companyId;
      if (isEdit.value) {
        getCompanyDetail();
      } else {
        resetForm();
      }
    }
  },
  { immediate: true }
);

// 监听 visible 变化
watch(visible, newVal => {
  emit("update:modelValue", newVal);
});

// 重置表单
const resetForm = () => {
  Object.assign(formData, {
    name: "",
    serial: "",
    company_type: "",
    corporate: "",
    social_code: "",
    industry_type: "",
    address: "",
    phone: "",
    email: "",
    website: "",
    description: "",
    status: "active"
  });
  formRef.value?.clearValidate();
};

// 获取公司详情
const getCompanyDetail = async () => {
  if (!props.companyId) return;

  try {
    loading.value = true;
    const response = await getCompany(props.companyId);
    if (response.code === 200) {
      const data = response.data;
      Object.assign(formData, {
        name: data.name || "",
        serial: data.serial || "",
        company_type: data.company_type || "",
        corporate: data.corporate || "",
        social_code: data.social_code || "",
        industry_type: data.industry_type || "",
        address: data.address || "",
        phone: data.phone || "",
        email: data.email || "",
        website: data.website || "",
        description: data.description || "",
        status: data.status || "active"
      });
    } else {
      ElMessage.error(response.msg || "获取公司信息失败");
    }
  } catch (error) {
    console.error("获取公司详情失败:", error);
    ElMessage.error("获取公司信息失败");
  } finally {
    loading.value = false;
  }
};

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return;

  try {
    const valid = await formRef.value.validate();
    if (!valid) return;

    loading.value = true;

    if (isEdit.value) {
      // 编辑
      const updateData: CompanyUpdateRequest = {
        id: props.companyId!,
        ...formData
      };
      const response = await updateCompany(updateData);
      if (response.code === 200) {
        ElMessage.success("更新成功");
        emit("success");
        handleClose();
      } else {
        ElMessage.error(response.msg || "更新失败");
      }
    } else {
      // 创建
      const response = await createCompany(formData);
      if (response.code === 200) {
        ElMessage.success("创建成功");
        emit("success");
        handleClose();
      } else {
        ElMessage.error(response.msg || "创建失败");
      }
    }
  } catch (error) {
    console.error("提交失败:", error);
    ElMessage.error("操作失败");
  } finally {
    loading.value = false;
  }
};

// 关闭抽屉
const handleClose = () => {
  visible.value = false;
  resetForm();
};
</script>

<style lang="scss" scoped>
:deep(.el-drawer__header) {
  margin-bottom: 20px;
}

:deep(.el-drawer__body) {
  padding: 0 20px;
}

:deep(.el-drawer__footer) {
  padding: 20px;
  border-top: 1px solid var(--el-border-color-light);
}
</style>
