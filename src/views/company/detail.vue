<template>
  <div class="main">
    <el-card shadow="never">
      <template #header>
        <div class="flex justify-between items-center">
          <h2 class="text-xl font-bold">公司详情</h2>
          <el-button :icon="useRenderIcon('ep:back')" @click="goBack">
            返回列表
          </el-button>
        </div>
      </template>

      <div v-if="companyDetail?.id" class="company-detail">
        <!-- 基本信息 -->
        <el-descriptions title="基本信息" :column="3" border class="mb-6">
          <el-descriptions-item label="公司名称">
            {{ companyDetail.name }}
          </el-descriptions-item>
          <el-descriptions-item label="公司编号">
            {{ companyDetail.serial }}
          </el-descriptions-item>
          <el-descriptions-item label="公司类型">
            {{ getCompanyTypeText(companyDetail.company_type) }}
          </el-descriptions-item>
          <el-descriptions-item label="法人代表">
            {{ companyDetail.corporate || "-" }}
          </el-descriptions-item>
          <el-descriptions-item label="社会信用代码">
            {{ companyDetail.social_code || "-" }}
          </el-descriptions-item>
          <el-descriptions-item label="行业类型">
            {{ getIndustryTypeText(companyDetail.industry_type) }}
          </el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="getStatusTagType(companyDetail.status)">
              {{ getStatusText(companyDetail.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="创建时间">
            {{ formatDateTime(companyDetail.created_at) }}
          </el-descriptions-item>
          <el-descriptions-item label="更新时间">
            {{ formatDateTime(companyDetail.updated_at) }}
          </el-descriptions-item>
        </el-descriptions>

        <!-- 联系信息 -->
        <el-descriptions title="联系信息" :column="2" border class="mb-6">
          <el-descriptions-item label="联系电话">
            {{ companyDetail.phone || "-" }}
          </el-descriptions-item>
          <el-descriptions-item label="邮箱地址">
            {{ companyDetail.email || "-" }}
          </el-descriptions-item>
          <el-descriptions-item label="官网地址" :span="2">
            <el-link
              v-if="companyDetail.website"
              :href="companyDetail.website"
              target="_blank"
              type="primary"
            >
              {{ companyDetail.website }}
            </el-link>
            <span v-else>-</span>
          </el-descriptions-item>
          <el-descriptions-item label="注册地址" :span="2">
            {{ companyDetail.address || "-" }}
          </el-descriptions-item>
        </el-descriptions>

        <!-- 公司描述 -->
        <el-descriptions title="公司描述" :column="1" border>
          <el-descriptions-item label="描述信息">
            <div class="description-content">
              {{ companyDetail.description || "暂无描述" }}
            </div>
          </el-descriptions-item>
        </el-descriptions>
      </div>

      <el-empty v-else description="公司信息不存在" />
    </el-card>
  </div>
</template>

<script setup lang="ts">
import {
  getCompany,
  type CompanyInfo,
  companyTypeDict,
  industryTypeDict,
  companyStatusDict
} from "@/api/company";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import { formatDateTime } from "@/utils/formatTime";
import { ElMessage } from "element-plus";
import { onMounted, ref } from "vue";
import { useRoute, useRouter } from "vue-router";

defineOptions({
  name: "CompanyDetail"
});

const router = useRouter();
const route = useRoute();

const companyDetail = ref<CompanyInfo>({} as CompanyInfo);
const loading = ref(false);

// 获取公司类型文本
const getCompanyTypeText = (type: string) => {
  const item = companyTypeDict.find(item => item.value === type);
  return item ? item.label : type || "-";
};

// 获取行业类型文本
const getIndustryTypeText = (type: string) => {
  const item = industryTypeDict.find(item => item.value === type);
  return item ? item.label : type || "-";
};

// 获取状态文本
const getStatusText = (status: string) => {
  const item = companyStatusDict.find(item => item.value === status);
  return item ? item.label : status || "-";
};

// 获取状态标签类型
const getStatusTagType = (status: string) => {
  switch (status) {
    case "active":
      return "success";
    case "suspended":
      return "warning";
    case "cancelled":
      return "danger";
    default:
      return "info";
  }
};

// 返回列表
const goBack = () => {
  router.push({ path: "/company/list" });
};

// 获取公司详情
const getCompanyDetail = async () => {
  try {
    loading.value = true;
    const response = await getCompany(route.query.id as string);
    if (response.code === 200) {
      companyDetail.value = response.data;
    } else {
      ElMessage.error(response.msg || "获取公司详情失败");
    }
  } catch (error) {
    console.error("获取公司详情失败:", error);
    ElMessage.error("获取公司详情失败");
  } finally {
    loading.value = false;
  }
};

// 初始化
onMounted(() => {
  if (route.query.id) {
    getCompanyDetail();
  } else {
    ElMessage.error("缺少公司ID参数");
    goBack();
  }
});
</script>

<style lang="scss" scoped>
.main {
  margin: 20px;
}

.company-detail {
  .mb-6 {
    margin-bottom: 24px;
  }

  .description-content {
    white-space: pre-wrap;
    word-break: break-word;
    line-height: 1.6;
  }
}
</style>
