<template>
  <div class="main">
    <el-card shadow="never">
      <template #header>
        <div class="card-header">
          <span class="font-medium">公司列表</span>
        </div>
      </template>

      <div class="mb-4">
        <el-button
          type="primary"
          :icon="useRenderIcon('ep:plus')"
          @click="handleCreate"
        >
          添加公司
        </el-button>
      </div>

      <pure-table
        ref="tableRef"
        row-key="id"
        align-whole="center"
        showOverflowTooltip
        :loading="loading"
        :data="dataList"
        :columns="columns"
        :pagination="pagination"
        @page-size-change="onSizeChange"
        @page-current-change="onCurrentChange"
      >
        <template #company_type="{ row }">
          <span>{{ getCompanyTypeText(row.company_type) }}</span>
        </template>
        <template #industry_type="{ row }">
          <span>{{ getIndustryTypeText(row.industry_type) }}</span>
        </template>
        <template #status="{ row }">
          <el-tag :type="getStatusTagType(row.status)">
            {{ getStatusText(row.status) }}
          </el-tag>
        </template>
        <template #created_at="{ row }">
          <span>{{ formatDateTime(row.created_at) }}</span>
        </template>
        <template #operation="{ row }">
          <el-button link type="primary" size="small" @click="handleView(row)">
            查看
          </el-button>
          <el-button link type="primary" size="small" @click="handleEdit(row)">
            编辑
          </el-button>
          <el-popconfirm
            :title="`是否确认删除公司${row.name}？`"
            @confirm="handleDelete(row)"
          >
            <template #reference>
              <el-button link type="danger" size="small">删除</el-button>
            </template>
          </el-popconfirm>
        </template>
      </pure-table>
    </el-card>

    <!-- 公司表单抽屉 -->
    <CompanyForm
      v-model="showForm"
      :company-id="currentCompanyId"
      @success="handleFormSuccess"
    />
  </div>
</template>

<script setup lang="ts">
import {
  companyStatusDict,
  companyTypeDict,
  deleteCompany,
  getCompanyList,
  industryTypeDict,
  type CompanyInfo
} from "@/api/company";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import { formatDateTime } from "@/utils/formatTime";
import { PureTable } from "@pureadmin/table";
import { ElMessage } from "element-plus";
import { computed, onMounted, reactive, ref } from "vue";
import { useRouter } from "vue-router";
import CompanyForm from "./modules/CompanyForm.vue";

defineOptions({
  name: "CompanyList"
});

const router = useRouter();
const tableRef = ref();

// 响应式数据
const loading = ref(false);
const dataList = ref<CompanyInfo[]>([]);
const showForm = ref(false);
const currentCompanyId = ref("");

// 分页配置
const pagination = reactive({
  total: 0,
  pageSize: 10,
  currentPage: 1,
  background: true,
  pageSizes: [10, 20, 50, 100]
});

// 表格列配置
const columns = computed(() => [
  {
    label: "公司名称",
    prop: "name",
    minWidth: 150
  },
  {
    label: "公司编号",
    prop: "serial",
    minWidth: 120
  },
  {
    label: "公司类型",
    prop: "company_type",
    minWidth: 120,
    slot: "company_type"
  },
  {
    label: "法人代表",
    prop: "corporate",
    minWidth: 100
  },
  {
    label: "行业类型",
    prop: "industry_type",
    minWidth: 120,
    slot: "industry_type"
  },
  {
    label: "联系电话",
    prop: "phone",
    minWidth: 120
  },
  {
    label: "状态",
    prop: "status",
    minWidth: 80,
    slot: "status"
  },
  {
    label: "创建时间",
    prop: "created_at",
    minWidth: 160,
    slot: "created_at"
  },
  {
    label: "操作",
    fixed: "right",
    width: 200,
    slot: "operation"
  }
]);

// 获取公司类型文本
const getCompanyTypeText = (type: string) => {
  const item = companyTypeDict.find(item => item.value === type);
  return item ? item.label : type || "-";
};

// 获取行业类型文本
const getIndustryTypeText = (type: string) => {
  const item = industryTypeDict.find(item => item.value === type);
  return item ? item.label : type || "-";
};

// 获取状态文本
const getStatusText = (status: string) => {
  const item = companyStatusDict.find(item => item.value === status);
  return item ? item.label : status || "-";
};

// 获取状态标签类型
const getStatusTagType = (status: string) => {
  switch (status) {
    case "active":
      return "success";
    case "suspended":
      return "warning";
    case "cancelled":
      return "danger";
    default:
      return "info";
  }
};

// 获取公司列表数据
const getCompanyListData = async () => {
  try {
    loading.value = true;
    const params = {
      options: {
        order_by: "created_at",
        desc: true
      },
      page: {
        page: pagination.currentPage,
        limit: pagination.pageSize
      },
      params: []
    };

    const response = await getCompanyList(params);
    if (response.code === 200) {
      dataList.value = response.data.data || [];
      pagination.total = response.data.total || 0;
    } else {
      ElMessage.error(response.msg || "获取公司列表失败");
    }
  } catch (error) {
    console.error("获取公司列表失败:", error);
    ElMessage.error("获取公司列表失败");
  } finally {
    loading.value = false;
  }
};

// 分页事件处理
const onSizeChange = (size: number) => {
  pagination.pageSize = size;
  getCompanyListData();
};

const onCurrentChange = (page: number) => {
  pagination.currentPage = page;
  getCompanyListData();
};

// 操作方法
const handleCreate = () => {
  currentCompanyId.value = "";
  showForm.value = true;
};

const handleView = (row: CompanyInfo) => {
  router.push({
    path: "/company/detail",
    query: { id: row.id }
  });
};

const handleEdit = (row: CompanyInfo) => {
  currentCompanyId.value = row.id;
  showForm.value = true;
};

// 表单成功回调
const handleFormSuccess = () => {
  getCompanyListData();
};

const handleDelete = async (row: CompanyInfo) => {
  try {
    const response = await deleteCompany(row.id);
    if (response.code === 200) {
      ElMessage.success("删除成功");
      getCompanyListData();
    } else {
      ElMessage.error(response.msg || "删除失败");
    }
  } catch (error) {
    console.error("删除公司失败:", error);
    ElMessage.error("删除失败");
  }
};

// 初始化
onMounted(() => {
  getCompanyListData();
});
</script>

<style scoped>
.main {
  margin: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>
