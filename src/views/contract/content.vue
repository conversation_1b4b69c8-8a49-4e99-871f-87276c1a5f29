<template>
  <div class="contract-content">
    <el-card v-if="contractDetail?.id" shadow="never">
      <template #header>
        <div class="flex justify-between items-center">
          <div class="flex items-center">
            <span class="font-medium text-lg"
              >合同详情: {{ contractDetail.name }}</span
            >
            <el-tag
              :type="getStatusColor(contractDetail.status)"
              effect="dark"
              class="ml-2"
            >
              {{ getStatusText(contractDetail.status) }}
            </el-tag>
          </div>
          <div class="flex gap-2">
            <el-button type="primary" size="small" @click="editContractDetail">
              修改合同信息
            </el-button>
            <el-button type="warning" size="small" @click="showAttachment">
              查看附件
            </el-button>
            <el-button type="success" size="small" @click="printContract">
              打印合同
            </el-button>
          </div>
        </div>
      </template>

      <div class="content-section">
        <!-- 基本信息 -->
        <div class="section-item">
          <h4 class="section-title">基本信息</h4>
          <div class="section-content">
            <el-row :gutter="24">
              <el-col :span="12">
                <div class="info-item">
                  <span class="info-label">合同编号：</span>
                  <span class="info-value">{{ contractDetail.serial }}</span>
                </div>
              </el-col>
              <el-col :span="12">
                <div class="info-item">
                  <span class="info-label">合同类型：</span>
                  <span class="info-value">{{
                    contractDetail.contract_type
                  }}</span>
                </div>
              </el-col>
            </el-row>
            <el-row :gutter="24">
              <el-col :span="12">
                <div class="info-item">
                  <span class="info-label">申请人：</span>
                  <span class="info-value">{{ contractDetail.applier }}</span>
                </div>
              </el-col>
              <el-col :span="12">
                <div class="info-item">
                  <span class="info-label">资金方：</span>
                  <span class="info-value">{{ contractDetail.funder }}</span>
                </div>
              </el-col>
            </el-row>
            <el-row :gutter="24">
              <el-col :span="12">
                <div class="info-item">
                  <span class="info-label">供应链金融公司：</span>
                  <span class="info-value">{{
                    contractDetail.scf_company
                  }}</span>
                </div>
              </el-col>
              <el-col :span="12">
                <div class="info-item">
                  <span class="info-label">签约时间：</span>
                  <span class="info-value">{{ contractDetail.sign_time }}</span>
                </div>
              </el-col>
            </el-row>
            <el-row :gutter="24">
              <el-col :span="12">
                <div class="info-item">
                  <span class="info-label">合同开始时间：</span>
                  <span class="info-value">{{
                    contractDetail.begin_time
                  }}</span>
                </div>
              </el-col>
              <el-col :span="12">
                <div class="info-item">
                  <span class="info-label">合同结束时间：</span>
                  <span class="info-value">{{ contractDetail.end_time }}</span>
                </div>
              </el-col>
            </el-row>
          </div>
        </div>

        <!-- 财务信息 -->
        <div class="section-item">
          <h4 class="section-title">财务信息</h4>
          <div class="section-content">
            <el-row :gutter="24">
              <el-col :span="12">
                <div class="info-item">
                  <span class="info-label">服务费率：</span>
                  <span class="info-value"
                    >{{ contractDetail.profit_calc_fee }}%</span
                  >
                </div>
              </el-col>
              <el-col :span="12">
                <div class="info-item">
                  <span class="info-label">申请额度：</span>
                  <span class="info-value"
                    >¥{{ formatAmount(contractDetail.application_quota) }}</span
                  >
                </div>
              </el-col>
            </el-row>
            <el-row :gutter="24">
              <el-col :span="12">
                <div class="info-item">
                  <span class="info-label">确认额度：</span>
                  <span class="info-value"
                    >¥{{ formatAmount(contractDetail.confirm_quota) }}</span
                  >
                </div>
              </el-col>
              <el-col :span="12">
                <div class="info-item">
                  <span class="info-label">已用额度：</span>
                  <span class="info-value"
                    >¥{{ formatAmount(contractDetail.used_quota) }}</span
                  >
                </div>
              </el-col>
            </el-row>
            <el-row :gutter="24">
              <el-col :span="12">
                <div class="info-item">
                  <span class="info-label">利润计算方式：</span>
                  <span class="info-value">{{
                    contractDetail.profit_calc_period
                  }}</span>
                </div>
              </el-col>
              <el-col :span="12">
                <div class="info-item">
                  <span class="info-label">计算周期：</span>
                  <span class="info-value">{{
                    contractDetail.profit_calc_period
                  }}</span>
                </div>
              </el-col>
            </el-row>
            <el-row :gutter="24">
              <el-col :span="12">
                <div class="info-item">
                  <span class="info-label">违约金计算方式：</span>
                  <span class="info-value">{{
                    contractDetail.penalty_calc_period
                  }}</span>
                </div>
              </el-col>
              <el-col :span="12">
                <div class="info-item">
                  <span class="info-label">违约金计算周期：</span>
                  <span class="info-value">{{
                    contractDetail.penalty_calc_period
                  }}</span>
                </div>
              </el-col>
            </el-row>
          </div>
        </div>

        <!-- 相关方信息 -->
        <div class="section-item">
          <h4 class="section-title">相关方信息</h4>
          <div class="section-content">
            <el-row :gutter="24">
              <el-col :span="12">
                <div class="info-item">
                  <span class="info-label">供应商：</span>
                  <span class="info-value">{{
                    contractDetail.supplier_name || "未指定"
                  }}</span>
                </div>
              </el-col>
              <el-col :span="12">
                <div class="info-item">
                  <span class="info-label">中标方：</span>
                  <span class="info-value">{{
                    contractDetail.bid_winner_name || "未指定"
                  }}</span>
                </div>
              </el-col>
            </el-row>
            <el-row :gutter="24">
              <el-col :span="12">
                <div class="info-item">
                  <span class="info-label">招标方：</span>
                  <span class="info-value">{{
                    contractDetail.bid_owner_name || "未指定"
                  }}</span>
                </div>
              </el-col>
              <el-col :span="12">
                <div class="info-item">
                  <span class="info-label">仓库：</span>
                  <span class="info-value">{{
                    contractDetail.warehouse_name || "未指定"
                  }}</span>
                </div>
              </el-col>
            </el-row>
          </div>
        </div>

        <!-- 产品类别 -->
        <div class="section-item">
          <h4 class="section-title">产品类别</h4>
          <div class="section-content">
            <div class="info-item">
              <span class="info-label">产品类别：</span>
              <div class="info-value">
                <el-tag
                  v-for="category in contractDetail.product_category"
                  :key="category"
                  type="primary"
                  size="small"
                  class="mr-2"
                >
                  {{ category }}
                </el-tag>
                <span
                  v-if="!contractDetail.product_category?.length"
                  class="text-gray-400"
                >
                  暂无产品类别
                </span>
              </div>
            </div>
          </div>
        </div>

        <!-- 系统信息 -->
        <div class="section-item">
          <h4 class="section-title">系统信息</h4>
          <div class="section-content">
            <el-row :gutter="24">
              <el-col :span="12">
                <div class="info-item">
                  <span class="info-label">创建时间：</span>
                  <span class="info-value">{{
                    showDateTime(contractDetail.created_at)
                  }}</span>
                </div>
              </el-col>
              <el-col :span="12">
                <div class="info-item">
                  <span class="info-label">更新时间：</span>
                  <span class="info-value">{{
                    showDateTime(contractDetail.updated_at)
                  }}</span>
                </div>
              </el-col>
            </el-row>
            <el-row :gutter="24">
              <el-col :span="12">
                <div class="info-item">
                  <span class="info-label">创建人：</span>
                  <span class="info-value">{{
                    contractDetail.created_at
                  }}</span>
                </div>
              </el-col>
              <el-col :span="12" />
            </el-row>
          </div>
        </div>
      </div>
    </el-card>

    <!-- 合同描述 -->
    <el-card class="mt-4" shadow="never">
      <template #header>
        <h4 class="section-title">合同描述</h4>
      </template>
      <div class="section-content">
        <div v-if="contractDetail?.desc" class="contract-desc">
          {{ contractDetail.desc }}
        </div>
        <div v-else class="text-gray-400 flex items-center">
          <el-icon class="mr-1"><Warning /></el-icon>
          暂无描述信息
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { type ContractInfo, contractStatusDict } from "@/api/contract";
import { formatDateTime } from "@/utils/formatTime";
import { Warning } from "@element-plus/icons-vue";
import { ElMessage } from "element-plus";
import { computed, onMounted } from "vue";
import { useRouter } from "vue-router";

defineOptions({
  name: "ContractContent"
});

// Props
interface Props {
  contractId: string;
  contractDetail: ContractInfo;
}

const props = defineProps<Props>();
const router = useRouter();

// 获取状态颜色 - 转换为 Element Plus 支持的类型
const getStatusColor = (
  status: string
): "success" | "info" | "warning" | "primary" | "danger" => {
  const statusItem = contractStatusDict.find(item => item.value === status);
  const color = statusItem ? statusItem.color : "grey";

  // 将状态颜色映射为 Element Plus 支持的类型
  const colorMap: Record<
    string,
    "success" | "info" | "warning" | "primary" | "danger"
  > = {
    green: "success",
    teal: "success",
    blue: "primary",
    orange: "warning",
    red: "danger",
    grey: "info"
  };

  return colorMap[color] || "info";
};

// 获取状态文本
const getStatusText = (status: string) => {
  const statusItem = contractStatusDict.find(item => item.value === status);
  return statusItem ? statusItem.label : status;
};

// 格式化时间显示
const showDateTime = computed(() => {
  return (datetime: string) => {
    if (!datetime) return "-";
    return formatDateTime(datetime);
  };
});

// 格式化金额
const formatAmount = (amount: number) => {
  if (!amount) return "0";
  return Number(amount).toLocaleString();
};

// 编辑合同详情
const editContractDetail = () => {
  router.push({
    path: "/contract/edit",
    query: { id: props.contractDetail.id }
  });
};

// 显示附件
const showAttachment = () => {
  ElMessage.info("附件功能开发中...");
};

// 打印合同
const printContract = () => {
  window.print();
};

// 初始化
onMounted(() => {
  if (!props.contractId) {
    ElMessage.warning("合约ID不能为空");
  }
});
</script>

<style lang="scss" scoped>
.contract-content {
  padding: 0;
}

.content-section {
  .section-item {
    margin-bottom: 24px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .section-title {
    font-size: 16px;
    font-weight: 600;
    color: var(--el-text-color-primary);
    margin-bottom: 12px;
    padding-bottom: 8px;
    border-bottom: 1px solid var(--el-border-color-light);
  }

  .section-content {
    padding: 16px;
    background-color: var(--el-bg-color-page);
    border-radius: 6px;

    .info-item {
      display: flex;
      align-items: center;
      margin-bottom: 12px;

      &:last-child {
        margin-bottom: 0;
      }

      .info-label {
        font-weight: 600;
        color: var(--el-text-color-regular);
        min-width: 120px;
        flex-shrink: 0;
      }

      .info-value {
        color: var(--el-text-color-primary);
        flex: 1;
      }
    }
  }
}

.contract-desc {
  font-size: 14px;
  line-height: 1.6;
  color: var(--el-text-color-regular);
  background: var(--el-bg-color-page);
  padding: 16px;
  border-radius: 4px;
  white-space: pre-wrap;
}

@media print {
  .el-button {
    display: none;
  }
}
</style>
