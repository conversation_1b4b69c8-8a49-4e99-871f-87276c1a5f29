<template>
  <div class="main">
    <el-card shadow="never">
      <template #header>
        <div class="card-header">
          <span class="font-medium">合约详情</span>
          <el-button @click="goBack">返回列表</el-button>
        </div>
      </template>

      <div v-if="contractId" class="contract-detail">
        <el-tabs v-model="activeTab" class="contract-tabs">
          <el-tab-pane label="总览" name="summary">
            <Summary
              v-if="activeTab === 'summary' && contractDetail.id"
              :contract-id="contractDetail.id"
              :contract-detail="contractDetail"
            />
          </el-tab-pane>
          <el-tab-pane label="订单列表" name="order">
            <OrderList
              v-if="activeTab === 'order' && contractDetail.id"
              :contract-id="contractDetail.id"
            />
          </el-tab-pane>
          <el-tab-pane label="合同内容" name="content">
            <Content
              v-if="activeTab === 'content' && contractDetail.id"
              :contract-id="contractDetail.id"
              :contract-detail="contractDetail"
            />
          </el-tab-pane>
        </el-tabs>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { getContract, type ContractInfo } from "@/api/contract";
import { closeCurrentTagAndGo } from "@/store/modules/multiTags";
import { ElMessage } from "element-plus";
import { onMounted, ref } from "vue";
import { useRoute, useRouter } from "vue-router";
import Content from "./content.vue";
import OrderList from "./order_list.vue";
import Summary from "./summary.vue";

defineOptions({
  name: "ContractDetail"
});

const router = useRouter();
const route = useRoute();

// 响应式数据
const activeTab = ref("summary");
const contractId = ref("");
const contractDetail = ref<ContractInfo>({} as ContractInfo);

// 返回列表
const goBack = () => {
  closeCurrentTagAndGo(router, "/contract/list");
};

// 获取合约详情
const loadContractDetail = async (id: string) => {
  try {
    const response = await getContract(id);
    if (response.code === 200) {
      contractDetail.value = response.data;
      ElMessage.success("合约信息加载成功");
    } else {
      ElMessage.error(response.msg || "获取合约详情失败");
      contractDetail.value = {} as ContractInfo;
    }
  } catch (error) {
    console.error("获取合约详情失败:", error);
    ElMessage.error("获取合约详情失败");
    contractDetail.value = {} as ContractInfo;
  }
};

// 初始化
onMounted(async () => {
  const id = route.query.id as string;
  if (id) {
    contractId.value = id;
    await loadContractDetail(id);
  } else {
    ElMessage.warning("缺少合约ID参数");
    goBack();
  }
});
</script>

<style lang="scss" scoped>
.main {
  margin: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.contract-detail {
  margin-top: 16px;
}

.contract-tabs {
  :deep(.el-tabs__header) {
    margin-bottom: 20px;
  }

  :deep(.el-tabs__content) {
    padding: 0;
  }
}
</style>
