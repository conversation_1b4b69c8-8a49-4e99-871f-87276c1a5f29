<template>
  <el-drawer
    v-model="visible"
    title="添加还款记录"
    :size="600"
    direction="rtl"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
  >
    <div class="drawer-content">
      <!-- 还款信息提示 -->
      <div class="mb-6 p-4 bg-blue-50 border border-blue-200 rounded">
        <div class="text-sm text-blue-700">
          <div class="font-semibold mb-2">还款计划信息</div>
          <div>计划编号：{{ repaymentData.serial || "未设置" }}</div>
          <div>
            应付总额：{{ formatAmount(repaymentData.total_remain || 0) }}（元）
          </div>
        </div>
      </div>

      <!-- 还款记录表单 -->
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="100px"
        class="space-y-4"
      >
        <el-form-item label="日志类型" prop="log_type" required>
          <el-select
            v-model="formData.log_type"
            placeholder="请选择日志类型"
            class="w-full"
          >
            <el-option
              v-for="item in repaymentLogTypeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="还款金额" prop="log_value">
          <el-input
            v-model="formData.log_value"
            placeholder="请输入还款金额"
            clearable
          />
        </el-form-item>

        <el-form-item label="还款日期" prop="log_date">
          <el-date-picker
            v-model="formData.log_date"
            type="date"
            placeholder="选择还款日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            class="w-full"
          />
        </el-form-item>

        <el-form-item label="备注信息" prop="remark">
          <el-input
            v-model="formData.remark"
            type="textarea"
            :rows="3"
            placeholder="请输入备注信息（可选）"
            maxlength="500"
            show-word-limit
          />
        </el-form-item>

        <!-- 文件上传 -->
        <el-form-item label="上传凭证">
          <div class="w-full">
            <input
              ref="fileInputRef"
              type="file"
              accept=".pdf,.doc,.docx,.jpg,.jpeg,.png,.gif"
              style="display: none"
              @change="handleFileSelect"
            />
            <div class="flex items-center gap-3">
              <el-button @click="triggerFileSelect">
                <IconifyIconOnline
                  icon="material-symbols:attach-file"
                  class="mr-1"
                />
                选择文件
              </el-button>
              <span v-if="selectedFile" class="text-sm text-gray-600">
                {{ selectedFile.name }}
              </span>
              <el-button
                v-if="selectedFile"
                type="danger"
                size="small"
                @click="removeFile"
              >
                移除
              </el-button>
            </div>
            <div class="text-xs text-gray-500 mt-1">
              支持格式：PDF、Word、图片，最大10MB
            </div>
          </div>
        </el-form-item>
      </el-form>
    </div>

    <template #footer>
      <div class="flex justify-end gap-3">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" :loading="loading" @click="handleSubmit">
          提交还款
        </el-button>
      </div>
    </template>
  </el-drawer>
</template>

<script setup lang="ts">
import type { RepaymentInfo } from "@/api/repayment";
import {
  repaymentLogStatusDict,
  repaymentLogTypeDict,
  type RepaymentLogCreateRequest
} from "@/api/repaymentLog";
import { IconifyIconOnline } from "@/components/ReIcon";
import { postFormDataAction } from "@/utils/http";
import { ElMessage, type FormInstance, type FormRules } from "element-plus";
import { reactive, ref, watch } from "vue";

defineOptions({
  name: "RepaymentLog"
});

// Props & Emits
const emit = defineEmits<{
  success: [];
}>();

// 响应式数据
const visible = ref(false);
const loading = ref(false);
const formRef = ref<FormInstance>();
const fileInputRef = ref<HTMLInputElement>();
const selectedFile = ref<File | null>(null);

// 还款计划数据
const repaymentData = ref<RepaymentInfo>({
  id: "",
  serial: "",
  contract_id: "",
  status: "draft"
});

// 表单数据
const formData = reactive<RepaymentLogCreateRequest>({
  parent_id: "",
  log_type: "还款",
  log_value: "",
  log_date: "",
  log_status: "new",
  remark: ""
});

// 日志类型选项
const repaymentLogTypeOptions = repaymentLogTypeDict;

// 日志状态选项
const repaymentLogStatusOptions = repaymentLogStatusDict;

// 表单验证规则
const formRules: FormRules = {
  log_value: [{ required: true, message: "请输入还款金额", trigger: "blur" }]
};

// 工具函数
const formatAmount = (amount: number | string) => {
  if (!amount) return "0.00";
  return Number(amount).toLocaleString("zh-CN", {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  });
};

// 方法
const show = (data: RepaymentInfo) => {
  repaymentData.value = data;
  formData.parent_id = data.id;
  visible.value = true;
};

const handleCancel = () => {
  visible.value = false;
  resetForm();
};

const resetForm = () => {
  formRef.value?.resetFields();
  selectedFile.value = null;
  Object.assign(formData, {
    parent_id: "",
    log_type: "还款",
    log_value: "",
    log_date: "",
    log_status: "new",
    remark: ""
  });
};

const triggerFileSelect = () => {
  fileInputRef.value?.click();
};

const handleFileSelect = (event: Event) => {
  const target = event.target as HTMLInputElement;
  const file = target.files?.[0];

  if (file) {
    // 验证文件大小（10MB）
    if (file.size > 10 * 1024 * 1024) {
      ElMessage.error("文件大小不能超过10MB");
      return;
    }

    // 验证文件类型
    const allowedTypes = [
      ".pdf",
      ".doc",
      ".docx",
      ".jpg",
      ".jpeg",
      ".png",
      ".gif"
    ];
    const fileExtension = "." + file.name.split(".").pop()?.toLowerCase();
    if (!allowedTypes.includes(fileExtension)) {
      ElMessage.error("不支持的文件格式");
      return;
    }

    selectedFile.value = file;
  }
};

const removeFile = () => {
  selectedFile.value = null;
  if (fileInputRef.value) {
    fileInputRef.value.value = "";
  }
};

const handleSubmit = async () => {
  if (!formRef.value) return;

  try {
    const valid = await formRef.value.validate();
    if (!valid) return;

    loading.value = true;

    // 统一使用 FormData 提交
    const formDataToSubmit = new FormData();

    // 如果有文件，添加文件
    if (selectedFile.value) {
      formDataToSubmit.append("file", selectedFile.value);
    }

    // 添加表单数据
    const objectData = {
      parent_id: formData.parent_id,
      log_type: "REPAY",
      log_value: formData.log_value,
      log_date: formData.log_date,
      log_status: "new",
      remark: formData.remark
    };

    formDataToSubmit.append("objectData", JSON.stringify(objectData));

    const response = await postFormDataAction(
      "/api/repayment_log",
      formDataToSubmit
    );

    if (response.code === 200) {
      ElMessage.success("还款记录添加成功");
      visible.value = false;
      emit("success");
    } else {
      ElMessage.error(response.msg || "添加失败");
    }
  } catch (error) {
    console.error("提交还款记录失败:", error);
    ElMessage.error("提交失败，请重试");
  } finally {
    loading.value = false;
  }
};

// 监听抽屉关闭，重置表单
watch(visible, newVal => {
  if (!newVal) {
    resetForm();
  }
});

// 暴露方法
defineExpose({
  show
});
</script>

<style lang="scss" scoped>
.drawer-content {
  padding: 20px;
  height: calc(100vh - 120px);
  overflow-y: auto;
}
</style>
