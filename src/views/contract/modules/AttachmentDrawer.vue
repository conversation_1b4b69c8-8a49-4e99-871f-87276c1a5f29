<template>
  <el-drawer
    v-model="visible"
    title="合同附件管理"
    direction="rtl"
    size="900px"
    :close-on-click-modal="true"
    @close="handleClose"
  >
    <!-- 上传区域 -->
    <div v-if="canUpload" class="mb-4">
      <el-card shadow="never">
        <template #header>
          <span class="text-sm font-medium">上传新附件</span>
        </template>

        <div class="space-y-4">
          <!-- 标题输入 -->
          <el-input
            v-model="uploadTitle"
            placeholder="请输入附件标题"
            clearable
          />

          <!-- 文件选择和上传 -->
          <div class="flex items-center gap-3">
            <div class="flex-1">
              <input
                ref="fileInputRef"
                type="file"
                :accept="acceptedFileTypes"
                class="hidden"
                @change="handleFileSelect"
              />

              <div class="flex items-center gap-2">
                <el-button type="primary" @click="triggerFileSelect">
                  {{ selectedFile ? "重新选择" : "选择文件" }}
                </el-button>

                <span
                  v-if="selectedFile"
                  class="text-sm text-gray-600 truncate max-w-xs"
                >
                  {{ selectedFile.name }} ({{
                    formatFileSize(selectedFile.size)
                  }})
                </span>
                <span v-else class="text-sm text-gray-400"> 未选择文件 </span>
              </div>
            </div>

            <el-button
              type="success"
              :disabled="!canSubmit"
              :loading="uploading"
              @click="handleUpload"
            >
              上传文件
            </el-button>
          </div>

          <!-- 提示信息 -->
          <div class="text-xs text-gray-500">
            支持 PDF、DOC、DOCX、JPG、JPEG、PNG、GIF 格式，单个文件不超过 10MB
          </div>
        </div>
      </el-card>
    </div>

    <!-- 附件列表 -->
    <el-card shadow="never">
      <template #header>
        <span class="text-sm font-medium">附件列表</span>
      </template>

      <PureTable
        ref="tableRef"
        :data="attachmentList"
        :columns="columns"
        :loading="loading"
        :show-overflow-tooltip="false"
        height="400"
        @page-size-change="handlePageSizeChange"
        @page-current-change="handlePageCurrentChange"
      >
        <template #file_link="{ row }">
          <el-button
            type="primary"
            size="small"
            @click="handleViewFile(row.file_link)"
          >
            查看
          </el-button>
        </template>

        <template #created_at="{ row }">
          {{ formatDateTime(row.created_at) }}
        </template>

        <template #actions="{ row }">
          <el-button
            v-if="canUpload"
            type="danger"
            size="small"
            @click="handleDeleteAttachment(row)"
          >
            删除
          </el-button>
        </template>
      </PureTable>
    </el-card>
  </el-drawer>
</template>

<script setup lang="ts">
import { getAttachmentList, type AttachmentInfo } from "@/api/attachment";
import type { ContractInfo } from "@/api/contract";
import {
  createContractLogWithFile,
  type ContractLogCreateRequest
} from "@/api/contract_log";
import { formatDateTime } from "@/utils/formatTime";
import { PureTable } from "@pureadmin/table";
import { ElMessage } from "element-plus";
import { computed, ref, watch } from "vue";

defineOptions({
  name: "AttachmentDrawer"
});

// Props
interface Props {
  modelValue: boolean;
  contractDetail: ContractInfo;
}

const props = defineProps<Props>();

// Emits
const emit = defineEmits<{
  "update:modelValue": [value: boolean];
}>();

// 响应式数据
const visible = ref(false);
const attachmentList = ref<AttachmentInfo[]>([]);
const loading = ref(false);
const uploading = ref(false);
const uploadTitle = ref("");
const selectedFile = ref<File | null>(null);
const fileInputRef = ref<HTMLInputElement>();

// 分页配置
const pagination = ref({
  page: 1,
  size: 10,
  total: 0
});

// 接受的文件类型
const acceptedFileTypes = ".pdf,.doc,.docx,.jpg,.jpeg,.png,.gif";

// 监听 modelValue 变化
watch(
  () => props.modelValue,
  newVal => {
    visible.value = newVal;
    if (newVal && props.contractDetail?.id) {
      loadAttachmentList();
    }
  },
  { immediate: true }
);

// 监听 visible 变化
watch(visible, newVal => {
  emit("update:modelValue", newVal);
});

// 计算属性
const canUpload = computed(() => {
  return (
    props.contractDetail?.status === "draft" ||
    props.contractDetail?.status === "new"
  );
});

const canSubmit = computed(() => {
  return selectedFile.value && uploadTitle.value.trim();
});

// 表格列定义
const columns = computed(() => [
  {
    label: "标题",
    prop: "title",
    minWidth: 150
  },
  {
    label: "文件名",
    prop: "file_name",
    minWidth: 200
  },
  {
    label: "操作",
    prop: "file_link",
    width: 100,
    slot: "file_link"
  },
  {
    label: "上传时间",
    prop: "created_at",
    width: 160,
    slot: "created_at"
  },
  {
    label: "操作",
    fixed: "right",
    width: 80,
    slot: "actions"
  }
]);

// 方法
const handleClose = () => {
  visible.value = false;
  // 重置表单
  uploadTitle.value = "";
  selectedFile.value = null;
  if (fileInputRef.value) {
    fileInputRef.value.value = "";
  }
};

// 加载附件列表
const loadAttachmentList = async () => {
  if (!props.contractDetail?.id) return;

  loading.value = true;
  try {
    const params = {
      page: {
        page: pagination.value.page,
        limit: pagination.value.size
      },
      options: {
        order_by: "created_at",
        desc: true
      },
      params: [
        {
          var: "entity_id",
          val: props.contractDetail.id
        },
        {
          var: "attachment_type",
          val: "financial_contract_attachment"
        }
      ]
    };

    const { data } = await getAttachmentList(params);
    if (data) {
      attachmentList.value = data || [];
      pagination.value.total = 0;
    }
  } catch (error) {
    console.error("加载附件列表失败:", error);
    ElMessage.error("附件列表加载失败");
  } finally {
    loading.value = false;
  }
};

// 触发文件选择
const triggerFileSelect = () => {
  fileInputRef.value?.click();
};

// 文件选择处理
const handleFileSelect = (event: Event) => {
  const target = event.target as HTMLInputElement;
  const file = target.files?.[0];

  if (!file) {
    selectedFile.value = null;
    return;
  }

  // 文件类型验证
  const isValidType = acceptedFileTypes
    .split(",")
    .some(type => file.name?.toLowerCase().endsWith(type.replace(".", "")));

  if (!isValidType) {
    ElMessage.error("文件格式不支持");
    target.value = "";
    selectedFile.value = null;
    return;
  }

  // 文件大小验证
  const isLt10M = file.size / 1024 / 1024 < 10;
  if (!isLt10M) {
    ElMessage.error("文件大小不能超过 10MB");
    target.value = "";
    selectedFile.value = null;
    return;
  }

  selectedFile.value = file;
};

// 格式化文件大小
const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return "0 B";

  const k = 1024;
  const sizes = ["B", "KB", "MB", "GB"];
  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
};

// 上传文件
const handleUpload = async () => {
  if (!canSubmit.value || !selectedFile.value) {
    ElMessage.warning("请选择文件并填写标题");
    return;
  }

  uploading.value = true;
  try {
    const formData = new FormData();
    formData.append("file", selectedFile.value);

    const objectData: ContractLogCreateRequest = {
      parent_id: props.contractDetail.id,
      log_type: "UPLOAD",
      log_value: uploadTitle.value || selectedFile.value.name,
      remark: selectedFile.value.name
    };

    formData.append("objectData", JSON.stringify(objectData));

    const response = await createContractLogWithFile(formData);
    if (response.code !== 200) {
      throw new Error(response.msg || "文件上传失败");
    }

    ElMessage.success("文件上传成功");

    // 重置表单
    uploadTitle.value = "";
    selectedFile.value = null;
    if (fileInputRef.value) {
      fileInputRef.value.value = "";
    }

    // 重新加载列表
    await loadAttachmentList();
  } catch (error) {
    console.error("文件上传失败:", error);
    ElMessage.error("文件上传失败");
  } finally {
    uploading.value = false;
  }
};

// 查看文件
const handleViewFile = (fileLink: string) => {
  if (fileLink) {
    window.open(fileLink, "_blank");
  }
};

// 删除附件
const handleDeleteAttachment = (_attachment: AttachmentInfo) => {
  ElMessage.info("删除功能待实现");
};

// 分页处理
const handlePageSizeChange = (size: number) => {
  pagination.value.size = size;
  loadAttachmentList();
};

const handlePageCurrentChange = (page: number) => {
  pagination.value.page = page;
  loadAttachmentList();
};

// 暴露方法
defineExpose({
  loadAttachmentList
});
</script>

<style lang="scss" scoped>
.el-upload__text {
  text-align: center;
}
</style>
