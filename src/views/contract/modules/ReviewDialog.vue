<template>
  <el-dialog
    v-model="visible"
    title="审核合同"
    width="500px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div class="space-y-4">
      <div>
        <p class="text-gray-600 mb-4">
          确认要审核合同 "{{ contractDetail?.name }}" 吗？
        </p>
      </div>

      <div>
        <div class="text-sm font-medium mb-2">审核结果：</div>
        <el-radio-group v-model="reviewOption">
          <el-radio value="approve">审核通过</el-radio>
          <el-radio value="reject">审核拒绝</el-radio>
        </el-radio-group>
      </div>

      <div>
        <el-input
          v-model="reviewRemark"
          type="textarea"
          placeholder="请输入审核意见..."
          :rows="3"
        />
      </div>
    </div>

    <template #footer>
      <div class="flex justify-end gap-2">
        <el-button @click="handleCancel">取消</el-button>
        <el-button
          :type="reviewOption === 'approve' ? 'success' : 'danger'"
          :loading="loading"
          @click="handleConfirm"
        >
          确认
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { reviewContract, type ContractInfo } from "@/api/contract";
import { ElMessage } from "element-plus";
import { ref, watch } from "vue";

defineOptions({
  name: "ReviewDialog"
});

// Props
interface Props {
  modelValue: boolean;
  contractId: string;
  contractDetail: ContractInfo;
}

const props = defineProps<Props>();

// Emits
const emit = defineEmits<{
  "update:modelValue": [value: boolean];
  success: [];
}>();

// 响应式数据
const visible = ref(false);
const reviewOption = ref("approve");
const reviewRemark = ref("");
const loading = ref(false);

// 监听 modelValue 变化
watch(
  () => props.modelValue,
  (newVal) => {
    visible.value = newVal;
    if (newVal) {
      // 重置表单数据
      reviewOption.value = "approve";
      reviewRemark.value = "";
    }
  },
  { immediate: true }
);

// 监听 visible 变化
watch(visible, (newVal) => {
  emit("update:modelValue", newVal);
});

// 事件处理函数
const handleClose = () => {
  visible.value = false;
};

const handleCancel = () => {
  visible.value = false;
};

const handleConfirm = async () => {
  if (!props.contractId) {
    ElMessage.error("合同ID不能为空");
    return;
  }

  loading.value = true;
  try {
    const requestData = {
      confirm: reviewOption.value === "approve",
      remark: reviewRemark.value || ""
    };

    const response = await reviewContract(props.contractId, requestData);
    if (response.code === 200) {
      ElMessage.success(
        `合同${reviewOption.value === "approve" ? "审核通过" : "审核拒绝"}成功`
      );
      visible.value = false;
      emit("success");
    } else {
      ElMessage.error(response.msg || "审核操作失败");
    }
  } catch (error) {
    console.error("审核合同失败:", error);
    ElMessage.error("审核操作失败，请重试");
  } finally {
    loading.value = false;
  }
};
</script>

<style lang="scss" scoped>
// 可以添加特定的样式
</style>
