<template>
  <el-drawer
    v-model="visible"
    title="还款记录详情"
    :size="800"
    direction="rtl"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
  >
    <div class="drawer-content">
      <div v-if="logDetail.id" class="space-y-6">
        <!-- 基本信息 -->
        <div class="info-section">
          <h3 class="section-title">基本信息</h3>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div class="info-item">
              <div class="info-label">记录状态</div>
              <div class="info-value">
                <el-tag
                  :color="getRepaymentLogStatusColor(logDetail.log_status)"
                  effect="dark"
                  round
                >
                  {{ getRepaymentLogStatusText(logDetail.log_status) }}
                </el-tag>
              </div>
            </div>
            <div class="info-item">
              <div class="info-label">操作人</div>
              <div class="info-value">{{ logDetail.creater_name || "-" }}</div>
            </div>
            <div class="info-item">
              <div class="info-label">记录值</div>
              <div class="info-value text-red-600 font-semibold">
                {{ logDetail.log_value || "-" }}
              </div>
            </div>
            <div class="info-item">
              <div class="info-label">补充日期</div>
              <div class="info-value">{{ logDetail.log_date || "-" }}</div>
            </div>
            <div class="info-item">
              <div class="info-label">日志类型</div>
              <div class="info-value">
                {{ logDetail.log_type || "-" }}
              </div>
            </div>
            <div class="info-item">
              <div class="info-label">创建时间</div>
              <div class="info-value">
                {{ formatDateTime(logDetail.created_at) }}
              </div>
            </div>
          </div>

          <!-- 备注信息 -->
          <div class="mt-4">
            <div class="info-label">备注信息</div>
            <div
              class="bg-gray-50 p-3 rounded border min-h-20 whitespace-pre-wrap"
            >
              {{ logDetail.remark || "无备注信息" }}
            </div>
          </div>
        </div>

        <!-- 还款计划剩余金额信息 -->
        <div
          v-if="
            repaymentDetail.status &&
            !['draft', 'new'].includes(repaymentDetail.status)
          "
          class="info-section"
        >
          <h3 class="section-title">还款计划剩余金额</h3>
          <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div class="info-item">
              <div class="info-label">剩余利润</div>
              <div class="info-value text-orange-600">
                ¥{{ formatAmount(repaymentDetail.profit_remain) }}
              </div>
            </div>
            <div class="info-item">
              <div class="info-label">剩余本金</div>
              <div class="info-value text-blue-600">
                ¥{{ formatAmount(repaymentDetail.principal_remain) }}
              </div>
            </div>
            <div class="info-item">
              <div class="info-label">剩余总额</div>
              <div class="info-value text-red-600 font-semibold">
                ¥{{ formatAmount(repaymentDetail.total_remain) }}
              </div>
            </div>
          </div>
        </div>

        <!-- 相关附件 -->
        <div class="info-section">
          <h3 class="section-title">相关附件</h3>
          <div v-if="attachments.length > 0">
            <PureTable
              :data="attachments"
              :columns="attachmentColumns"
              :loading="attachmentsLoading"
              :show-overflow-tooltip="false"
              height="300"
            >
              <template #file_link="{ row }">
                <el-button
                  v-if="row.file_link"
                  type="primary"
                  size="small"
                  @click="handleViewFile(row.file_link)"
                >
                  查看
                </el-button>
                <span v-else class="text-gray-400">-</span>
              </template>

              <template #created_at="{ row }">
                {{ formatDateTime(row.created_at) }}
              </template>
            </PureTable>
          </div>
          <div v-else class="text-center py-8 text-gray-500">
            <IconifyIconOnline
              icon="material-symbols:attach-file"
              class="text-4xl mb-2"
            />
            <div>暂无相关附件</div>
          </div>
        </div>

        <!-- 审核操作 -->
        <div v-if="logDetail.log_status === '待处理'" class="info-section">
          <h3 class="section-title">审核操作</h3>
          <div class="flex justify-center">
            <el-button type="warning" @click="handleShowReview">
              审核此记录
            </el-button>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="flex justify-end">
        <el-button @click="handleClose">关闭</el-button>
      </div>
    </template>

    <!-- 审核确认对话框 -->
    <el-dialog
      v-model="showReviewDialog"
      title="审核还款记录"
      width="500px"
      :close-on-click-modal="false"
    >
      <div class="space-y-4">
        <p class="text-gray-600">
          确认要审核还款记录 "{{ formatAmount(logDetail.log_value) }}" 吗？
        </p>

        <div>
          <div class="text-sm font-medium mb-2">审核结果：</div>
          <el-radio-group v-model="reviewOption">
            <el-radio value="approve">审核通过</el-radio>
            <el-radio value="reject">审核拒绝</el-radio>
          </el-radio-group>
        </div>

        <div>
          <el-input
            v-model="reviewRemark"
            type="textarea"
            placeholder="请输入审核意见..."
            :rows="3"
          />
        </div>
      </div>

      <template #footer>
        <div class="flex justify-end gap-2">
          <el-button @click="handleCancelReview">取消</el-button>
          <el-button
            :type="reviewOption === 'approve' ? 'success' : 'danger'"
            :loading="reviewLoading"
            @click="handleConfirmReview"
          >
            确认
          </el-button>
        </div>
      </template>
    </el-dialog>
  </el-drawer>
</template>

<script setup lang="ts">
import type { RepaymentInfo } from "@/api/repayment";
import {
  getRepaymentLogStatusColor,
  getRepaymentLogStatusText,
  updateRepaymentLog,
  type RepaymentLogInfo
} from "@/api/repaymentLog";
import { IconifyIconOnline } from "@/components/ReIcon";
import { formatDateTime } from "@/utils/formatTime";
import { PureTable } from "@pureadmin/table";
import { ElMessage } from "element-plus";
import { computed, ref } from "vue";

defineOptions({
  name: "RepaymentLogDetail"
});

// Props & Emits
const emit = defineEmits<{
  reviewCompleted: [];
}>();

// 响应式数据
const visible = ref(false);
const reviewLoading = ref(false);
const attachmentsLoading = ref(false);

// 数据
const logDetail = ref<RepaymentLogInfo>({
  id: "",
  parent_id: "",
  log_type: "PAYMENT",
  created_at: 0,
  updated_at: 0
});

const repaymentDetail = ref<RepaymentInfo>({
  id: "",
  serial: "",
  contract_id: "",
  status: "draft"
});

const attachments = ref<any[]>([]);

// 审核相关
const showReviewDialog = ref(false);
const reviewOption = ref("approve");
const reviewRemark = ref("");

// 附件表格列定义
const attachmentColumns = computed(() => [
  {
    label: "标题",
    prop: "title",
    minWidth: 150
  },
  {
    label: "文件名",
    prop: "file_name",
    minWidth: 200
  },
  {
    label: "文件链接",
    prop: "file_link",
    minWidth: 100,
    slot: "file_link"
  },
  {
    label: "上传时间",
    prop: "created_at",
    minWidth: 160,
    slot: "created_at"
  }
]);

// 工具函数
const formatAmount = (amount: number | string) => {
  if (!amount) return "0.00";
  return Number(amount).toLocaleString("zh-CN", {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  });
};

// 方法
const show = async (
  logData: RepaymentLogInfo,
  repaymentData: RepaymentInfo = {} as RepaymentInfo
) => {
  logDetail.value = { ...logData };
  repaymentDetail.value = { ...repaymentData };

  // 获取附件列表
  await loadAttachments(logData.id);

  visible.value = true;
};

const handleClose = () => {
  visible.value = false;
  resetData();
};

const resetData = () => {
  logDetail.value = {
    id: "",
    parent_id: "",
    log_type: "PAYMENT",
    created_at: 0,
    updated_at: 0
  };
  repaymentDetail.value = {
    id: "",
    serial: "",
    contract_id: "",
    status: "draft"
  };
  attachments.value = [];
};

const loadAttachments = async (logId: string) => {
  if (!logId) return;

  attachmentsLoading.value = true;
  try {
    // TODO: 实现附件列表加载
    // 这里需要根据实际的附件API来实现
    attachments.value = [];
  } catch (error) {
    console.error("获取附件列表失败:", error);
    attachments.value = [];
  } finally {
    attachmentsLoading.value = false;
  }
};

const handleViewFile = (fileLink: string) => {
  if (fileLink) {
    window.open(fileLink, "_blank");
  }
};

const handleShowReview = () => {
  reviewOption.value = "approve";
  reviewRemark.value = "";
  showReviewDialog.value = true;
};

const handleCancelReview = () => {
  showReviewDialog.value = false;
  reviewOption.value = "approve";
  reviewRemark.value = "";
};

const handleConfirmReview = async () => {
  try {
    reviewLoading.value = true;

    const updateData = {
      id: logDetail.value.id,
      log_status: reviewOption.value === "approve" ? "通过" : "拒绝",
      remark: reviewRemark.value || logDetail.value.remark
    };

    const response = await updateRepaymentLog(updateData);
    if (response.code === 200) {
      ElMessage.success(
        `还款记录${reviewOption.value === "approve" ? "审核通过" : "审核拒绝"}成功`
      );

      // 更新本地状态
      logDetail.value.log_status = updateData.log_status;

      showReviewDialog.value = false;
      emit("reviewCompleted");

      // 关闭详情抽屉
      handleClose();
    } else {
      ElMessage.error(response.msg || "审核操作失败");
    }
  } catch (error) {
    console.error("审核还款记录失败:", error);
    ElMessage.error("审核操作失败，请重试");
  } finally {
    reviewLoading.value = false;
  }
};

// 暴露方法
defineExpose({
  show
});
</script>

<style lang="scss" scoped>
.drawer-content {
  padding: 20px;
  height: calc(100vh - 120px);
  overflow-y: auto;
}

.info-section {
  .section-title {
    font-size: 16px;
    font-weight: 600;
    color: #409eff;
    margin-bottom: 16px;
    padding-bottom: 8px;
    border-bottom: 1px solid #e4e7ed;
  }
}

.info-item {
  .info-label {
    font-size: 14px;
    color: #606266;
    margin-bottom: 4px;
  }

  .info-value {
    font-size: 14px;
    color: #303133;
    min-height: 20px;
  }
}
</style>
