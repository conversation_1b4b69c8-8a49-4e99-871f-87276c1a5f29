<template>
  <el-drawer
    v-model="visible"
    title="编辑还款计划"
    :size="800"
    direction="rtl"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
  >
    <div class="drawer-content">
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="120px"
        class="space-y-6"
      >
        <!-- 基本信息 -->
        <div class="form-section">
          <h3 class="section-title">基本信息</h3>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <el-form-item label="还款计划编号" prop="serial">
              <el-input
                v-model="formData.serial"
                placeholder="如果不手动填写，系统将自动生成"
                clearable
              />
            </el-form-item>
            <el-form-item label="计划状态" prop="status">
              <el-select
                v-model="formData.status"
                placeholder="请选择状态"
                class="w-full"
              >
                <el-option
                  v-for="item in statusOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </div>
        </div>

        <!-- 金额信息 -->
        <div class="form-section">
          <h3 class="section-title">金额信息</h3>
          <div class="mb-4 p-3 bg-yellow-50 border border-yellow-200 rounded">
            <div class="text-sm text-yellow-700">
              最大可用合同金额：<span class="font-semibold">{{
                formatAmount(availableAmount)
              }}</span
              >（元）
            </div>
          </div>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <el-form-item label="支取本金金额" prop="principal_amount">
              <el-input-number
                v-model="formData.principal_amount"
                :min="0"
                :max="availableAmount"
                :precision="2"
                :step="1"
                placeholder="请输入支取本金金额"
                class="w-full"
              />
              <div class="text-xs text-gray-500 mt-1">
                可用金额：{{ formatAmount(availableAmount) }} 元
              </div>
            </el-form-item>
            <div class="form-item-display">
              <div class="label">已关联订单金额：</div>
              <div class="value">
                {{ formatAmount(formData.target_amount || 0) }}（元）
              </div>
            </div>
          </div>
        </div>

        <!-- 时间限制 -->
        <div class="form-section">
          <h3 class="section-title">时间限制</h3>
          <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <el-form-item label="开始日期" prop="begin_date">
              <el-date-picker
                v-model="formData.begin_date"
                type="date"
                placeholder="选择开始日期"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                class="w-full"
              />
            </el-form-item>
            <el-form-item label="结束日期" prop="end_date">
              <el-date-picker
                v-model="formData.end_date"
                type="date"
                placeholder="选择结束日期"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                class="w-full"
              />
            </el-form-item>
            <el-form-item label="宽限天数" prop="grace_period">
              <el-input-number
                v-model="formData.grace_period"
                :min="0"
                :step="1"
                placeholder="宽限天数"
                class="w-full"
              />
            </el-form-item>
          </div>
        </div>

        <!-- 利润计算配置 -->
        <div class="form-section">
          <h3 class="section-title">利润计算配置</h3>
          <div class="grid grid-cols-1 md:grid-cols-3 gap-1">
            <el-form-item label="利润计算费率" prop="profit_calc_fee_percent">
              <el-input-number
                v-model="profitCalcFeePercent"
                :min="0"
                :max="100"
                :precision="2"
                placeholder="费率百分比"
                class="w-full"
              />
              <div class="text-xs text-gray-500 mt-1">
                输入费率（0-100，如输入7表示7%即0.07）
              </div>
            </el-form-item>
            <el-form-item label="利润计算周期" prop="profit_calc_period">
              <el-select
                v-model="formData.profit_calc_period"
                placeholder="请选择周期"
                class="w-full"
              >
                <el-option
                  v-for="item in calcPeriodOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item
              label="违约金计算费率"
              prop="penalty_calc_fee_percent"
            >
              <el-input-number
                v-model="penaltyCalcFeePercent"
                :min="0"
                :max="100"
                :precision="2"
                placeholder="违约金费率"
                class="w-full"
              />
              <div class="text-xs text-gray-500 mt-1">
                输入违约金费率（0-100，如输入5表示5%即0.05）
              </div>
            </el-form-item>
          </div>
        </div>

        <!-- 备注信息 -->
        <div class="form-section">
          <h3 class="section-title">备注信息</h3>
          <el-form-item label="还款计划备注" prop="remark">
            <el-input
              v-model="formData.remark"
              type="textarea"
              :rows="3"
              placeholder="请输入备注信息"
              maxlength="500"
              show-word-limit
            />
          </el-form-item>
        </div>
      </el-form>
    </div>

    <template #footer>
      <div class="flex justify-end gap-3">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" :loading="loading" @click="handleSave">
          保存
        </el-button>
      </div>
    </template>
  </el-drawer>
</template>

<script setup lang="ts">
import {
  calcPeriodOptions,
  repaymentStatusDict,
  updateRepayment,
  type RepaymentInfo,
  type RepaymentUpdateRequest
} from "@/api/repayment";
import { ElMessage, type FormInstance, type FormRules } from "element-plus";
import { computed, ref, watch } from "vue";

defineOptions({
  name: "RepaymentEdit"
});

// Props & Emits
const emit = defineEmits<{
  success: [data: RepaymentInfo];
}>();

// 响应式数据
const visible = ref(false);
const loading = ref(false);
const formRef = ref<FormInstance>();

// 表单数据
const formData = ref<RepaymentUpdateRequest>({
  id: "",
  serial: "",
  contract_id: "",
  target_amount: 0,
  principal_amount: 0,
  profit_calc_fee: 0,
  penalty_calc_fee: 0,
  profit_calc_period: "",
  begin_date: "",
  end_date: "",
  grace_period: 0,
  status: "draft",
  remark: ""
});

// 合同信息（用于计算可用金额）
const contractInfo = ref<any>({});

// 选项数据
const statusOptions = repaymentStatusDict;

// 表单验证规则
const formRules: FormRules = {
  principal_amount: [
    { required: true, message: "请输入支取本金金额", trigger: "blur" },
    { type: "number", min: 0, message: "支取金额必须大于0", trigger: "blur" }
  ],
  // profit_calc_fee_percent: [
  //   { required: true, message: "请输入利润计算费率", trigger: "blur" }
  // ],
  // penalty_calc_fee_percent: [
  //   { required: true, message: "请输入违约金计算费率", trigger: "blur" }
  // ],
  begin_date: [
    { required: true, message: "请选择开始日期", trigger: "change" }
  ],
  end_date: [{ required: true, message: "请选择结束日期", trigger: "change" }]
};

// 计算属性
const availableAmount = computed(() => {
  if (!contractInfo.value) return 0;
  const confirmQuota = Number(contractInfo.value.confirm_quota) || 0;
  const usedQuota = Number(contractInfo.value.used_quota) || 0;
  return Math.max(0, confirmQuota - usedQuota);
});

// 利润费率百分比（用于显示和输入）
const profitCalcFeePercent = computed({
  get: () => {
    return Math.round((formData.value.profit_calc_fee || 0) * 100 * 100) / 100;
  },
  set: (value: number) => {
    formData.value.profit_calc_fee = (value || 0) / 100;
  }
});

// 违约金费率百分比（用于显示和输入）
const penaltyCalcFeePercent = computed({
  get: () => {
    return (
      Math.round((formData.value.penalty_calc_fee || 0) * 100 * 10000) / 10000
    );
  },
  set: (value: number) => {
    formData.value.penalty_calc_fee = (value || 0) / 100;
  }
});

// 工具函数
const formatAmount = (amount: number | string) => {
  if (!amount) return "0";
  return Number(amount).toLocaleString("zh-CN", {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  });
};

// 方法
const show = (data: RepaymentInfo) => {
  // 验证合同数据是否存在
  if (!data.contract) {
    ElMessage.error("合同信息缺失，无法编辑还款计划");
    return;
  }

  // 设置表单数据（处理可能的字符串数值转换）
  formData.value = {
    id: data.id || "",
    serial: data.serial || "",
    contract_id: data.contract_id || "",
    target_amount: Number(data.target_amount) || 0,
    principal_amount: Number(data.principal_amount) || 0,
    profit_calc_fee: Number(data.profit_calc_fee) || 0,
    penalty_calc_fee: Number(data.penalty_calc_fee) || 0,
    profit_calc_period: data.profit_calc_period || "",
    begin_date: data.begin_date || "",
    end_date: data.end_date || "",
    grace_period: Number(data.grace_period) || 0,
    status: data.status || "draft",
    remark: data.remark || ""
  };

  // 设置合同信息
  contractInfo.value = data.contract;

  visible.value = true;
};

const handleCancel = () => {
  visible.value = false;
  formRef.value?.resetFields();
};

const handleSave = async () => {
  if (!formRef.value) return;

  try {
    const valid = await formRef.value.validate();
    if (!valid) return;

    // 验证支取金额不超过可用金额
    if (formData.value.principal_amount! > availableAmount.value) {
      ElMessage.error(
        `支取金额不能超过可用金额 ${formatAmount(availableAmount.value)} 元`
      );
      return;
    }

    loading.value = true;

    // 转换数值字段为字符串以匹配后端要求
    const submitData = {
      ...formData.value,
      // 将数值字段转换为字符串
      profit_amount: formData.value.profit_amount?.toString(),
      principal_amount: formData.value.principal_amount?.toString(),
      total_amount: formData.value.total_amount?.toString(),
      target_amount: formData.value.target_amount?.toString(),
      profit_remain: formData.value.profit_remain?.toString(),
      principal_remain: formData.value.principal_remain?.toString(),
      total_remain: formData.value.total_remain?.toString(),
      grace_period: formData.value.grace_period?.toString()
      // profit_calc_fee 和 penalty_calc_fee 保持数值类型，后端会自动处理
    };

    const response = await updateRepayment(submitData as any);
    if (response.code === 200) {
      ElMessage.success("保存成功");
      visible.value = false;
      emit("success", formData.value as RepaymentInfo);
    } else {
      ElMessage.error(response.msg || "保存失败");
    }
  } catch (error) {
    console.error("保存失败:", error);
    ElMessage.error("保存失败，请重试");
  } finally {
    loading.value = false;
  }
};

// 监听抽屉关闭，重置表单
watch(visible, newVal => {
  if (!newVal) {
    formRef.value?.resetFields();
  }
});

// 暴露方法
defineExpose({
  show
});
</script>

<style lang="scss" scoped>
.drawer-content {
  padding: 20px;
  height: calc(100vh - 120px);
  overflow-y: auto;
}

.form-section {
  margin-bottom: 32px;

  .section-title {
    font-size: 16px;
    font-weight: 600;
    color: #409eff;
    margin-bottom: 16px;
    padding-bottom: 8px;
    border-bottom: 1px solid #e4e7ed;
  }
}

.form-item-display {
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding: 8px 0;

  .label {
    font-size: 14px;
    color: #606266;
    margin-bottom: 4px;
  }

  .value {
    font-size: 16px;
    font-weight: 500;
    color: #f56c6c;
  }
}
</style>
