<template>
  <div class="contract-orders">
    <el-card shadow="never">
      <template #header>
        <div class="flex justify-between items-center">
          <span class="font-medium">关联订单</span>
        </div>
      </template>

      <!-- 搜索表单 -->
      <el-form
        ref="formRef"
        :inline="true"
        :model="searchForm"
        class="search-form mb-4"
      >
        <el-form-item label="平台订单编号：" prop="platform_order_serial">
          <el-input
            v-model="searchForm.platform_order_serial"
            placeholder="请输入平台订单编号"
            clearable
            class="w-[180px]!"
            @keyup.enter="onSearch"
          />
        </el-form-item>
        <el-form-item label="订单编号：" prop="serial">
          <el-input
            v-model="searchForm.serial"
            placeholder="请输入订单编号"
            clearable
            class="w-[180px]!"
            @keyup.enter="onSearch"
          />
        </el-form-item>
        <el-form-item label="销售平台：" prop="platform_name">
          <el-input
            v-model="searchForm.platform_name"
            placeholder="请输入销售平台"
            clearable
            class="w-[180px]!"
            @keyup.enter="onSearch"
          />
        </el-form-item>
        <el-form-item label="订单状态：" prop="status">
          <el-select
            v-model="searchForm.status"
            placeholder="请选择状态"
            clearable
            class="w-[180px]!"
          >
            <el-option label="已完成" value="success" />
            <el-option label="已退款" value="failed" />
            <el-option label="备货中" value="pending" />
            <el-option label="配送中" value="shipping" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onSearch"> 搜索 </el-button>
          <el-button @click="resetForm"> 重置 </el-button>
        </el-form-item>
      </el-form>

      <!-- 订单列表表格 -->
      <pure-table
        ref="tableRef"
        row-key="id"
        align-whole="center"
        showOverflowTooltip
        :loading="loading"
        :data="tableData"
        :columns="columns"
        :pagination="pagination"
        @page-size-change="onSizeChange"
        @page-current-change="onCurrentChange"
      >
        <template #status="{ row }">
          <el-tag :type="getOrderStatusType(row.status)" effect="dark" round>
            {{ getOrderStatusText(row.status) }}
          </el-tag>
        </template>
        <template #total_payment="{ row }">
          <span>¥{{ formatAmount(row.total_payment) }}</span>
        </template>
        <template #purchase_time="{ row }">
          <span>{{ showDateTime(row.purchase_time) }}</span>
        </template>
        <template #pay_time="{ row }">
          <span>{{ showDateTime(row.pay_time) }}</span>
        </template>
        <template #delivery_time="{ row }">
          <span>{{ showDateTime(row.delivery_time) }}</span>
        </template>
        <template #operation="{ row }">
          <el-button
            link
            type="primary"
            size="small"
            @click="handleDetail(row.id)"
          >
            查看详情
          </el-button>
        </template>
      </pure-table>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { formatDateTime } from "@/utils/formatTime";
import { http } from "@/utils/http";
import { PureTable } from "@pureadmin/table";
import "@pureadmin/table/dist/style.css";
import { ElMessage, type FormInstance } from "element-plus";
import { computed, onMounted, reactive, ref } from "vue";
import { useRouter } from "vue-router";

defineOptions({
  name: "ContractOrderList"
});

// Props
interface Props {
  contractId: string;
}

const props = defineProps<Props>();
const router = useRouter();

// 响应式数据
const loading = ref(false);
const tableData = ref([]);
const formRef = ref<FormInstance>();

// 搜索表单
const searchForm = reactive({
  platform_order_serial: "",
  serial: "",
  platform_name: "",
  status: ""
});

// 分页配置
const pagination = reactive({
  total: 0,
  pageSize: 10,
  currentPage: 1,
  background: true,
  pageSizes: [10, 20, 50, 100]
});

// 查询参数
const queryParams = ref<any>({});

// 表格列配置
const columns = computed(() => [
  {
    label: "平台订单编号",
    prop: "platform_order_serial",
    minWidth: 150
  },
  {
    label: "销售平台",
    prop: "platform_name",
    minWidth: 120
  },
  {
    label: "订单编号",
    prop: "serial",
    minWidth: 150
  },
  {
    label: "订单总额",
    prop: "total_payment",
    minWidth: 120,
    slot: "total_payment"
  },
  {
    label: "下单时间",
    prop: "purchase_time",
    minWidth: 120,
    slot: "purchase_time"
  },
  {
    label: "支付时间",
    prop: "pay_time",
    minWidth: 120,
    slot: "pay_time"
  },
  {
    label: "发货时间",
    prop: "delivery_time",
    minWidth: 120,
    slot: "delivery_time"
  },
  {
    label: "订单状态",
    prop: "status",
    minWidth: 100,
    slot: "status"
  },
  {
    label: "操作",
    fixed: "right",
    width: 120,
    slot: "operation"
  }
]);

// 获取订单状态类型
const getOrderStatusType = (status: string | number) => {
  if (status === "success" || status === "已完成" || status === 1) {
    return "success";
  } else if (status === "failed" || status === "已退款" || status === 0) {
    return "danger";
  } else if (status === "pending" || status === "备货" || status === 2) {
    return "warning";
  } else if (status === "shipping" || status === "配送中" || status === 3) {
    return "primary";
  }
  return "info";
};

// 获取订单状态文本
const getOrderStatusText = (status: string | number) => {
  if (status === "success" || status === "已完成" || status === 1) {
    return "已完成";
  } else if (status === "failed" || status === "已退款" || status === 0) {
    return "已退款";
  } else if (status === "pending" || status === "备货" || status === 2) {
    return "备货中";
  } else if (status === "shipping" || status === "配送中" || status === 3) {
    return "配送中";
  }
  return status?.toString() || "未知";
};

// 格式化时间显示
const showDateTime = (datetime: string) => {
  if (!datetime) return "-";
  return formatDateTime(datetime);
};

// 格式化金额
const formatAmount = (amount: number) => {
  if (!amount) return "0";
  return Number(amount).toLocaleString();
};

// 获取订单列表
const getOrderListData = async () => {
  if (!props.contractId) {
    ElMessage.warning("合约ID不能为空");
    return;
  }

  loading.value = true;
  try {
    // 构建请求参数
    const requestParams = {
      options: {
        order_by: "created_at",
        desc: true
      },
      page: {
        page: pagination.currentPage,
        limit: pagination.pageSize
      },
      params: [
        {
          var: "contract_id",
          val: props.contractId
        }
      ]
    };

    // 添加搜索条件
    Object.keys(searchForm).forEach(key => {
      if (searchForm[key] && searchForm[key].trim() !== "") {
        requestParams.params.push({
          var: key,
          val: searchForm[key].trim()
        });
      }
    });

    const response = await http.post("/api/sales_order/list", requestParams);
    if (response.code === 200) {
      tableData.value = response.data.data || [];
      pagination.total = response.data.total || 0;
    } else {
      ElMessage.error(response.msg || "获取订单列表失败");
      tableData.value = [];
      pagination.total = 0;
    }
  } catch (error) {
    console.error("获取订单列表失败:", error);
    ElMessage.error("获取订单列表失败");
    tableData.value = [];
    pagination.total = 0;
  } finally {
    loading.value = false;
  }
};

// 搜索
const onSearch = () => {
  pagination.currentPage = 1;
  getOrderListData();
};

// 重置表单
const resetForm = () => {
  if (formRef.value) {
    formRef.value.resetFields();
  }
  onSearch();
};

// 分页相关
const onSizeChange = (size: number) => {
  pagination.pageSize = size;
  getOrderListData();
};

const onCurrentChange = (current: number) => {
  pagination.currentPage = current;
  getOrderListData();
};

// 查看订单详情
const handleDetail = (id: string) => {
  router.push({
    path: "/order/detail",
    query: { id }
  });
};

// 初始化
onMounted(() => {
  if (props.contractId) {
    getOrderListData();
  }
});
</script>

<style lang="scss" scoped>
.contract-orders {
  padding: 0;
}

.search-form {
  :deep(.el-form-item) {
    margin-bottom: 12px;
  }
}
</style>
