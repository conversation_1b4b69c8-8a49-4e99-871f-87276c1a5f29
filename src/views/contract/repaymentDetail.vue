<template>
  <div class="repayment-detail">
    <!-- 页面头部 -->
    <el-card shadow="never" class="mb-4">
      <template #header>
        <div class="flex justify-between items-center">
          <div class="flex items-center gap-3">
            <h2 class="text-xl font-semibold">管理还款计划</h2>
            <el-tag
              v-if="repaymentDetail.status"
              :color="getRepaymentStatusColor(repaymentDetail.status)"
              effect="dark"
              round
            >
              {{ getRepaymentStatusText(repaymentDetail.status) }}
            </el-tag>
          </div>
          <div class="flex gap-2">
            <el-button type="primary" @click="handleEditRepayment">
              编辑基本信息
            </el-button>
            <el-button
              v-if="
                repaymentDetail.status === 'new' ||
                repaymentDetail.status === 'draft'
              "
              type="warning"
              @click="handleReviewRepayment"
            >
              审核还款计划
            </el-button>
            <el-button
              v-if="
                ['processing', 'pending', 'partial'].includes(
                  repaymentDetail.status
                )
              "
              type="success"
              @click="handleAddRepayment"
            >
              还款
            </el-button>
            <el-button @click="handleGoBack">关闭页面</el-button>
          </div>
        </div>
      </template>

      <!-- 基本信息展示 -->
      <div class="space-y-6">
        <!-- 第一行：基本信息 -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div class="info-item">
            <div class="info-label">还款计划编号</div>
            <div class="info-value">
              {{ repaymentDetail.serial || "系统自动生成" }}
            </div>
          </div>
          <div class="info-item">
            <div class="info-label">支取本金金额</div>
            <div class="info-value">
              {{
                formatAmountWithDecimals(repaymentDetail.principal_amount || 0)
              }}（元）
            </div>
          </div>
          <div class="info-item">
            <div class="info-label">关联订单金额</div>
            <div class="info-value">
              {{
                formatAmountWithDecimals(repaymentDetail.target_amount || 0)
              }}（元）
            </div>
          </div>
        </div>

        <!-- 第二行：时间限制 -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div class="info-item">
            <div class="info-label">开始日期</div>
            <div class="info-value">
              {{ repaymentDetail.begin_date || "未设置" }}
            </div>
          </div>
          <div class="info-item">
            <div class="info-label">结束日期</div>
            <div class="info-value">
              {{ repaymentDetail.end_date || "未设置" }}
            </div>
          </div>
          <div class="info-item">
            <div class="info-label">宽限天数</div>
            <div class="info-value">
              {{ repaymentDetail.grace_period || 0 }} 天
            </div>
          </div>
        </div>

        <!-- 第三行：利息计算配置 -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div class="info-item">
            <div class="info-label">利息计算费率</div>
            <div class="info-value">
              {{ formatPercentage(repaymentDetail.profit_calc_fee || 0) }}%
              <div class="info-desc">年化利率</div>
            </div>
          </div>
          <div class="info-item">
            <div class="info-label">利息计算周期</div>
            <div class="info-value">
              {{ getCalcPeriodLabel(repaymentDetail.profit_calc_period) }}
            </div>
          </div>
          <div class="info-item">
            <div class="info-label">违约金计算费率</div>
            <div class="info-value">
              {{ formatPercentage(repaymentDetail.penalty_calc_fee || 0, 4) }}%
              <div class="info-desc">日息利率</div>
            </div>
          </div>
        </div>

        <!-- 第四行：剩余金额信息（仅当状态不是draft或new时显示） -->
        <div
          v-if="
            repaymentDetail.status &&
            !['draft', 'new'].includes(repaymentDetail.status)
          "
          class="grid grid-cols-1 md:grid-cols-3 gap-6"
        >
          <div class="info-item">
            <div class="info-label">应付利息</div>
            <div class="info-value">
              {{ formatAmountWithDecimals(repaymentDetail.profit_remain || 0) }}
              <div class="info-desc">当前剩余利息金额</div>
            </div>
          </div>
          <div class="info-item">
            <div class="info-label">应付本金</div>
            <div class="info-value">
              {{
                formatAmountWithDecimals(repaymentDetail.principal_remain || 0)
              }}
              <div class="info-desc">当前剩余本金金额</div>
            </div>
          </div>
          <div class="info-item">
            <div class="info-label">应付总额</div>
            <div class="info-value">
              {{ formatAmountWithDecimals(repaymentDetail.total_remain || 0) }}
              <div class="info-desc">当前剩余总金额</div>
            </div>
          </div>
        </div>

        <!-- 备注信息 -->
        <div class="info-item">
          <div class="info-label">备注信息</div>
          <div
            class="bg-gray-50 p-4 rounded border min-h-20 whitespace-pre-wrap"
          >
            {{ repaymentDetail.remark || "无备注信息" }}
          </div>
        </div>
      </div>
    </el-card>

    <!-- 标签页内容 -->
    <el-card shadow="never">
      <el-tabs v-model="activeTab" type="border-card">
        <!-- 关联订单标签页 -->
        <el-tab-pane label="关联订单" name="orders">
          <div class="space-y-4">
            <div class="flex justify-between items-center">
              <div class="flex items-center gap-4">
                <el-button type="primary" @click="handleRelateOrders">
                  关联订单
                </el-button>
                <div class="text-sm text-gray-600">
                  订单金额统计：{{
                    formatAmountWithDecimals(
                      repaymentDetail.target_amount || 0
                    )
                  }}（元）
                </div>
              </div>
            </div>

            <PureTable
              ref="orderTableRef"
              :data="relatedOrders"
              :columns="orderColumns"
              :pagination="orderPagination"
              :loading="orderLoading"
              :show-overflow-tooltip="false"
              height="400"
              @page-size-change="handleOrderPageSizeChange"
              @page-current-change="handleOrderPageCurrentChange"
            >
              <template #actions="{ row }">
                <el-button
                  type="danger"
                  size="small"
                  @click="handleUnrelateOrder(row)"
                >
                  移除
                </el-button>
              </template>
            </PureTable>
          </div>
        </el-tab-pane>

        <!-- 还款记录标签页 -->
        <el-tab-pane label="还款记录" name="logs">
          <div class="space-y-4">
            <div class="flex justify-between items-center">
              <h3 class="text-lg font-medium">还款记录</h3>
            </div>

            <PureTable
              ref="logTableRef"
              :data="repaymentLogs"
              :columns="logColumns"
              :pagination="logPagination"
              :loading="logLoading"
              :show-overflow-tooltip="false"
              height="400"
              @page-size-change="handleLogPageSizeChange"
              @page-current-change="handleLogPageCurrentChange"
            >
              <template #log_status="{ row }">
                <el-tag
                  :color="getRepaymentLogStatusColor(row.log_status)"
                  effect="dark"
                  round
                >
                  {{ getRepaymentLogStatusText(row.log_status) }}
                </el-tag>
              </template>

              <template #actions="{ row }">
                <el-button
                  type="primary"
                  size="small"
                  @click="handleViewLogDetail(row)"
                >
                  查看详情
                </el-button>
              </template>
            </PureTable>
          </div>
        </el-tab-pane>
      </el-tabs>
    </el-card>

    <!-- SelectItemDialog 组件 -->
    <SelectItemDialog
      v-if="repaymentDetail.id"
      ref="selectOrderListRef"
      :url-list="'/api/sales_order/list'"
      :contract-id="repaymentDetail.contract_id"
      @handle-select-item="handleSelectOrders"
    />

    <!-- 还款计划编辑抽屉 -->
    <RepaymentEdit
      ref="repaymentEditRef"
      @success="handleRepaymentEditSuccess"
    />

    <!-- 还款记录添加抽屉 -->
    <RepaymentLog ref="repaymentLogRef" @success="handleRepaymentLogSuccess" />

    <!-- 还款记录详情抽屉 -->
    <RepaymentLogDetail
      ref="repaymentLogDetailRef"
      @review-completed="handleReviewCompleted"
    />

    <!-- 审核确认对话框 -->
    <el-dialog
      v-model="showReviewDialog"
      title="审核还款计划"
      width="500px"
      :close-on-click-modal="false"
    >
      <div class="space-y-4">
        <p class="text-gray-600">
          确认要审核还款计划 "{{ repaymentDetail.serial }}" 吗？
        </p>

        <div>
          <div class="text-sm font-medium mb-2">审核结果：</div>
          <el-radio-group v-model="reviewOption">
            <el-radio value="approve">审核通过</el-radio>
            <el-radio value="reject">审核拒绝</el-radio>
          </el-radio-group>
        </div>

        <div>
          <el-input
            v-model="reviewRemark"
            type="textarea"
            placeholder="请输入审核意见..."
            :rows="3"
          />
        </div>
      </div>

      <template #footer>
        <div class="flex justify-end gap-2">
          <el-button @click="handleCancelReview">取消</el-button>
          <el-button
            :type="reviewOption === 'approve' ? 'success' : 'danger'"
            @click="handleConfirmReview"
          >
            确认
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { getContract } from "@/api/contract";
import {
  calcPeriodReverseDict,
  getRepayment,
  getRepaymentStatusColor,
  getRepaymentStatusText,
  relateOrdersToRepayment,
  reviewRepayment,
  unrelateOrderFromRepayment,
  type RepaymentInfo
} from "@/api/repayment";
import {
  getRepaymentLogList,
  getRepaymentLogStatusColor,
  getRepaymentLogStatusText,
  type RepaymentLogInfo
} from "@/api/repaymentLog";
import { getSalesOrderList, type SalesOrderInfo } from "@/api/salesOrder";
import { SelectItemDialog } from "@/components/SelectOrderList";
import { formatDateTime } from "@/utils/formatTime";
import { PureTable } from "@pureadmin/table";
import { ElMessage, ElMessageBox } from "element-plus";
import { computed, onMounted, ref } from "vue";
import { useRoute, useRouter } from "vue-router";
import RepaymentEdit from "./modules/RepaymentEdit.vue";
import RepaymentLog from "./modules/RepaymentLog.vue";
import RepaymentLogDetail from "./modules/RepaymentLogDetail.vue";

defineOptions({
  name: "RepaymentDetail"
});

// Props
const route = useRoute();
const router = useRouter();

// 响应式数据
const repaymentDetail = ref<RepaymentInfo>({
  id: "",
  serial: "",
  contract_id: "",
  status: "draft"
});

const activeTab = ref("orders");
const loading = ref(false);

// 关联订单相关
const relatedOrders = ref<SalesOrderInfo[]>([]);
const orderLoading = ref(false);
const orderPagination = ref({
  page: 1,
  size: 10,
  total: 0
});

// 还款记录相关
const repaymentLogs = ref<RepaymentLogInfo[]>([]);
const logLoading = ref(false);
const logPagination = ref({
  page: 1,
  size: 10,
  total: 0
});

// 审核相关
const showReviewDialog = ref(false);
const reviewOption = ref("approve");
const reviewRemark = ref("");

// 表格引用
const orderTableRef = ref();
const logTableRef = ref();

// 组件引用
const repaymentEditRef = ref();
const repaymentLogRef = ref();
const repaymentLogDetailRef = ref();

// SelectOrderList 组件引用
const selectOrderListRef = ref();

// 工具函数
const formatAmountWithDecimals = (amount: number | string) => {
  if (!amount) return "0.00";
  return Number(amount).toLocaleString("zh-CN", {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  });
};

const formatPercentage = (value: number | string, decimals = 2) => {
  if (!value) return "0.00";
  return (Number(value) * 100).toFixed(decimals);
};

const getCalcPeriodLabel = (period?: string) => {
  if (!period) return "未设置";
  return calcPeriodReverseDict[period] || period;
};

// 表格列定义
const orderColumns = computed(() => [
  {
    label: "订单编号",
    prop: "platform_order_serial",
    minWidth: 150
  },
  {
    label: "订单金额",
    prop: "total_payment",
    minWidth: 120,
    cellRenderer: ({ row }) =>
      row.total_payment
        ? `¥${formatAmountWithDecimals(row.total_payment)}`
        : "-"
  },
  {
    label: "平台费用",
    prop: "platform_fee_total",
    minWidth: 120,
    cellRenderer: ({ row }) =>
      row.platform_fee_total
        ? `¥${formatAmountWithDecimals(row.platform_fee_total)}`
        : "-"
  },
  {
    label: "发货时间",
    prop: "delivery_time",
    minWidth: 120
  },
  {
    label: "签收时间",
    prop: "sign_time",
    minWidth: 120
  },
  {
    label: "操作",
    fixed: "right",
    width: 100,
    slot: "actions"
  }
]);

const logColumns = computed(() => [
  {
    label: "还款金额",
    prop: "log_value",
    minWidth: 120,
    cellRenderer: ({ row }) =>
      row.log_value ? `¥${formatAmountWithDecimals(row.log_value)}` : "-"
  },
  {
    label: "还款日期",
    prop: "log_date",
    minWidth: 120
  },
  {
    label: "还款方式",
    prop: "payment_method",
    minWidth: 100
  },
  {
    label: "状态",
    prop: "log_status",
    minWidth: 100,
    slot: "log_status"
  },
  {
    label: "创建时间",
    prop: "created_at",
    minWidth: 160,
    cellRenderer: ({ row }) =>
      row.created_at ? formatDateTime(row.created_at) : "-"
  },
  {
    label: "操作",
    fixed: "right",
    width: 120,
    slot: "actions"
  }
]);

// 数据加载方法
const loadRepaymentDetail = async () => {
  const repaymentId = route.query.id as string;
  if (!repaymentId) {
    ElMessage.error("还款计划ID不能为空");
    return;
  }

  loading.value = true;
  try {
    const { data } = await getRepayment(repaymentId);
    if (data) {
      repaymentDetail.value = data;

      // 同步获取合同信息
      if (data.contract_id) {
        try {
          const contractResponse = await getContract(data.contract_id);
          if (contractResponse.data) {
            repaymentDetail.value.contract = contractResponse.data;
          }
        } catch (contractError) {
          console.error("获取合同信息失败:", contractError);
          // 合同信息获取失败不影响主流程，只记录错误
        }
      }
    }
  } catch (error) {
    console.error("加载还款计划详情失败:", error);
    ElMessage.error("加载还款计划详情失败");
  } finally {
    loading.value = false;
  }
};

const loadRelatedOrders = async () => {
  if (!repaymentDetail.value.id) return;

  orderLoading.value = true;
  try {
    const params = {
      page: {
        page: orderPagination.value.page,
        limit: orderPagination.value.size
      },
      options: {
        order_by: "created_at",
        desc: true
      },
      params: [
        {
          var: "repayment_id",
          val: repaymentDetail.value.id
        }
      ]
    };

    const { data } = await getSalesOrderList(params);
    if (data) {
      relatedOrders.value = data.data || [];
      orderPagination.value.total = data.total || 0;
    }
  } catch (error) {
    console.error("加载关联订单失败:", error);
    ElMessage.error("加载关联订单失败");
  } finally {
    orderLoading.value = false;
  }
};

const loadRepaymentLogs = async () => {
  if (!repaymentDetail.value.id) return;

  logLoading.value = true;
  try {
    const params = {
      page: {
        page: logPagination.value.page,
        limit: logPagination.value.size
      },
      options: {
        order_by: "created_at",
        desc: true
      },
      params: [
        {
          var: "raw",
          val: `parent_id = "${repaymentDetail.value.id}"`
        },
        {
          var: "log_type",
          val: "REPAY"
        }
      ]
    };

    const { data } = await getRepaymentLogList(params);
    if (data) {
      repaymentLogs.value = data.data || [];
      logPagination.value.total = data.total || 0;
    }
  } catch (error) {
    console.error("加载还款记录失败:", error);
    ElMessage.error("加载还款记录失败");
  } finally {
    logLoading.value = false;
  }
};

// 事件处理方法
const handleEditRepayment = () => {
  if (repaymentEditRef.value) {
    repaymentEditRef.value.show(repaymentDetail.value);
  }
};

const handleRepaymentEditSuccess = async () => {
  ElMessage.success("还款计划更新成功");
  await loadRepaymentDetail();
};

const handleReviewRepayment = () => {
  reviewOption.value = "approve";
  reviewRemark.value = "";
  showReviewDialog.value = true;
};

const handleCancelReview = () => {
  showReviewDialog.value = false;
  reviewOption.value = "approve";
  reviewRemark.value = "";
};

const handleConfirmReview = async () => {
  try {
    const requestData = {
      confirm: reviewOption.value === "approve",
      remark: reviewRemark.value || ""
    };

    const response = await reviewRepayment(
      repaymentDetail.value.id,
      requestData
    );
    if (response.code === 200) {
      ElMessage.success(
        `还款计划${reviewOption.value === "approve" ? "审核通过" : "审核拒绝"}成功`
      );
      showReviewDialog.value = false;
      await loadRepaymentDetail();
    } else {
      ElMessage.error(response.msg || "审核操作失败");
    }
  } catch (error) {
    console.error("审核还款计划失败:", error);
    ElMessage.error("审核操作失败，请重试");
  }
};

const handleAddRepayment = () => {
  if (repaymentLogRef.value) {
    repaymentLogRef.value.show(repaymentDetail.value);
  }
};

const handleRepaymentLogSuccess = async () => {
  ElMessage.success("还款记录添加成功");
  await Promise.all([loadRepaymentDetail(), loadRepaymentLogs()]);
};

const handleRelateOrders = () => {
  if (selectOrderListRef.value) {
    selectOrderListRef.value.show();
  }
};

// 处理订单选择完成事件
const handleSelectOrders = async (selectedOrders: SalesOrderInfo[]) => {
  try {
    if (!selectedOrders || selectedOrders.length === 0) {
      ElMessage.warning("请选择要关联的订单");
      return;
    }

    const orderIds = selectedOrders.map(order => order.id);
    const response = await relateOrdersToRepayment({
      id: repaymentDetail.value.id,
      ids: orderIds
    });

    if (response.code === 200) {
      ElMessage.success(`成功关联 ${selectedOrders.length} 个订单`);
      await Promise.all([loadRelatedOrders(), loadRepaymentDetail()]);
    } else {
      ElMessage.error(response.msg || "关联订单失败");
    }
  } catch (error) {
    console.error("关联订单失败:", error);
    ElMessage.error("关联订单失败");
  }
};

const handleUnrelateOrder = async (order: SalesOrderInfo) => {
  // 二次确认对话框
  const confirmResult = await ElMessageBox.confirm(
    `确定要移除订单 "${order.platform_order_serial || order.serial || order.id}" 吗？`,
    "移除订单确认",
    {
      confirmButtonText: "确定移除",
      cancelButtonText: "取消",
      type: "warning",
      confirmButtonClass: "el-button--danger",
      beforeClose: (action, instance, done) => {
        if (action === "confirm") {
          instance.confirmButtonLoading = true;
          instance.confirmButtonText = "移除中...";
          done();
        } else {
          done();
        }
      }
    }
  ).catch(() => {
    // 用户取消操作
    return "cancel";
  });

  if (confirmResult === "cancel") {
    return;
  }

  try {
    const response = await unrelateOrderFromRepayment({
      id: order.id
    });

    if (response.code === 200) {
      ElMessage.success("移除订单成功");
      await loadRelatedOrders();
      await loadRepaymentDetail(); // 刷新统计信息
    } else {
      ElMessage.error(response.msg || "移除订单失败");
    }
  } catch (error) {
    console.error("移除订单失败:", error);
    ElMessage.error("移除订单失败");
  }
};

const handleViewLogDetail = (log: RepaymentLogInfo) => {
  if (repaymentLogDetailRef.value) {
    repaymentLogDetailRef.value.show(log, repaymentDetail.value);
  }
};

const handleReviewCompleted = async () => {
  ElMessage.success("审核操作完成");
  await Promise.all([loadRepaymentDetail(), loadRepaymentLogs()]);
};

const handleGoBack = () => {
  router.back();
};

// 分页处理方法
const handleOrderPageSizeChange = (size: number) => {
  orderPagination.value.size = size;
  loadRelatedOrders();
};

const handleOrderPageCurrentChange = (page: number) => {
  orderPagination.value.page = page;
  loadRelatedOrders();
};

const handleLogPageSizeChange = (size: number) => {
  logPagination.value.size = size;
  loadRepaymentLogs();
};

const handleLogPageCurrentChange = (page: number) => {
  logPagination.value.page = page;
  loadRepaymentLogs();
};

// 生命周期
onMounted(async () => {
  await loadRepaymentDetail();
  if (repaymentDetail.value.id) {
    await Promise.all([loadRelatedOrders(), loadRepaymentLogs()]);
  }
});
</script>

<style lang="scss" scoped>
.repayment-detail {
  padding: 20px;
}

.info-item {
  .info-label {
    font-size: 14px;
    color: #666;
    margin-bottom: 4px;
  }

  .info-value {
    font-size: 16px;
    font-weight: 500;
    color: #333;

    .info-desc {
      font-size: 12px;
      color: #999;
      font-weight: normal;
      margin-top: 2px;
    }
  }
}
</style>
