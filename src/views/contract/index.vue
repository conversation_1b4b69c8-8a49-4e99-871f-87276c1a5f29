<template>
  <div class="main">
    <!-- 搜索表单 -->
    <el-form
      ref="formRef"
      :inline="true"
      :model="searchForm"
      class="search-form bg-bg_color w-full pl-8 pt-[12px] overflow-auto"
    >
      <el-form-item label="合同编号：" prop="serial">
        <el-input
          v-model="searchForm.serial"
          placeholder="请输入合同编号"
          clearable
          class="w-[180px]!"
        />
      </el-form-item>
      <el-form-item label="合同名称：" prop="name">
        <el-input
          v-model="searchForm.name"
          placeholder="请输入合同名称"
          clearable
          class="w-[180px]!"
        />
      </el-form-item>
      <el-form-item label="合同状态：" prop="status">
        <el-select
          v-model="searchForm.status"
          placeholder="请选择状态"
          clearable
          class="w-[180px]!"
        >
          <el-option
            v-for="item in contractStatusDict"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          :icon="useRenderIcon('ep:search')"
          :loading="loading"
          @click="onSearch"
        >
          搜索
        </el-button>
        <el-button
          :icon="useRenderIcon('ep:refresh')"
          @click="resetForm(formRef)"
        >
          重置
        </el-button>
      </el-form-item>
    </el-form>

    <!-- 表格 -->
    <el-card class="box-card" shadow="never" style="margin-top: 16px">
      <template #header>
        <div class="flex justify-between items-center">
          <span class="font-medium">合约列表</span>
          <el-button
            type="primary"
            :icon="useRenderIcon('ri:add-circle-line')"
            @click="handleAdd"
          >
            新增合约
          </el-button>
        </div>
      </template>

      <pure-table
        ref="tableRef"
        row-key="id"
        align-whole="center"
        showOverflowTooltip
        :loading="loading"
        :data="dataList"
        :columns="columns"
        :pagination="pagination"
        @page-size-change="onSizeChange"
        @page-current-change="onCurrentChange"
      >
        <template #status="{ row }">
          <el-tag
            :color="getContractStatusColor(row.status)"
            effect="dark"
            round
          >
            {{ getContractStatusText(row.status) }}
          </el-tag>
        </template>
        <template #operation="{ row }">
          <el-dropdown trigger="click">
            <el-button type="primary" size="small">
              操作<el-icon class="el-icon--right"><ArrowDown /></el-icon>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item :icon="View" @click="handleView(row)">
                  查看
                </el-dropdown-item>
                <el-dropdown-item :icon="Edit" @click="handleEdit(row)">
                  编辑
                </el-dropdown-item>
                <el-dropdown-item :icon="Delete" @click="confirmDelete(row)">
                  删除
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </template>
      </pure-table>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import {
  contractStatusDict,
  deleteContract,
  getContractList,
  getContractStatusColor,
  getContractStatusText,
  type ContractInfo,
  type ContractListParams
} from "@/api/contract";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import { formatDateTime } from "@/utils/formatTime";
import { ArrowDown, Delete, Edit, View } from "@element-plus/icons-vue";
import { PureTable } from "@pureadmin/table";
import "@pureadmin/table/dist/style.css";
import { ElMessage, ElMessageBox, type FormInstance } from "element-plus";
import { computed, onMounted, reactive, ref } from "vue";
import { useRouter } from "vue-router";

defineOptions({
  name: "ContractList"
});

const router = useRouter();

// 响应式数据
const loading = ref(false);
const dataList = ref<ContractInfo[]>([]);
const tableRef = ref();
const formRef = ref<FormInstance>();

// 搜索表单
const searchForm = reactive({
  serial: "",
  name: "",
  status: ""
});

// 分页配置
const pagination = reactive({
  total: 0,
  pageSize: 10,
  currentPage: 1,
  background: true,
  pageSizes: [10, 20, 50, 100]
});

// 表格列配置
const columns = computed(() => [
  {
    label: "合同名称",
    prop: "name",
    minWidth: 150
  },
  {
    label: "合同编号",
    prop: "serial",
    minWidth: 180
  },
  {
    label: "申请人",
    prop: "applier",
    minWidth: 100
  },
  {
    label: "开始时间",
    prop: "begin_time",
    minWidth: 120,
    formatter: ({ begin_time }) => formatDateTime(begin_time)
  },
  {
    label: "结束时间",
    prop: "end_time",
    minWidth: 120,
    formatter: ({ end_time }) => formatDateTime(end_time)
  },
  {
    label: "资金方",
    prop: "funder",
    minWidth: 100
  },
  {
    label: "合同状态",
    prop: "status",
    minWidth: 100,
    slot: "status"
  },
  {
    label: "操作",
    fixed: "right",
    width: 100,
    slot: "operation"
  }
]);

// 获取合约列表
const getContractListData = async () => {
  loading.value = true;
  try {
    const params: ContractListParams = {
      options: {
        order_by: "created_at",
        desc: true
      },
      page: {
        page: pagination.currentPage,
        limit: pagination.pageSize
      }
    };

    // 添加搜索参数
    if (searchForm.serial || searchForm.name || searchForm.status) {
      params.params = [];
      if (searchForm.serial) {
        params.params.push({
          var: "serial",
          val: searchForm.serial
        });
      }
      if (searchForm.name) {
        params.params.push({
          var: "name",
          val: searchForm.name
        });
      }
      if (searchForm.status) {
        params.params.push({
          var: "status",
          val: searchForm.status
        });
      }
    }

    const response = await getContractList(params);
    if (response.code === 200) {
      dataList.value = response.data.data;
      pagination.total = response.data.total;
    } else {
      ElMessage.error(response.msg || "获取合约列表失败");
    }
  } catch (error) {
    console.error("获取合约列表失败:", error);
    ElMessage.error("获取合约列表失败");
  } finally {
    loading.value = false;
  }
};

// 搜索
const onSearch = () => {
  pagination.currentPage = 1;
  getContractListData();
};

// 重置表单
const resetForm = (formEl: FormInstance | undefined) => {
  if (!formEl) return;
  formEl.resetFields();
  onSearch();
};

// 分页相关
const onSizeChange = (size: number) => {
  pagination.pageSize = size;
  getContractListData();
};

const onCurrentChange = (current: number) => {
  pagination.currentPage = current;
  getContractListData();
};

// 操作方法
const handleAdd = () => {
  router.push("/contract/create");
};

const handleView = (row: ContractInfo) => {
  router.push({
    path: "/contract/detail",
    query: { id: row.id }
  });
};

const handleEdit = (row: ContractInfo) => {
  router.push({
    path: "/contract/edit",
    query: { id: row.id }
  });
};

const confirmDelete = (row: ContractInfo) => {
  ElMessageBox.confirm(
    `是否确认删除合约编号为${row.serial}的这条数据？`,
    "删除确认",
    {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning"
    }
  )
    .then(() => {
      handleDelete(row);
    })
    .catch(() => {
      // 用户取消删除
    });
};

const handleDelete = async (row: ContractInfo) => {
  try {
    const response = await deleteContract(row.id);
    if (response.code === 200) {
      ElMessage.success("删除成功");
      getContractListData();
    } else {
      ElMessage.error(response.msg || "删除失败");
    }
  } catch (error) {
    console.error("删除合约失败:", error);
    ElMessage.error("删除失败");
  }
};

// 初始化
onMounted(() => {
  getContractListData();
});
</script>

<style lang="scss" scoped>
.main {
  margin: 20px;
}

.search-form {
  :deep(.el-form-item) {
    margin-bottom: 12px;
  }
}
</style>
