<template>
  <div class="main">
    <el-card shadow="never">
      <template #header>
        <div class="card-header">
          <span class="font-medium">{{
            isEdit ? "编辑合约" : "创建合约"
          }}</span>
          <div class="header-actions">
            <el-button
              v-if="!isEdit"
              type="success"
              size="small"
              @click="createDemoData"
            >
              生成示例数据
            </el-button>
            <el-button @click="goBack">返回列表</el-button>
          </div>
        </div>
      </template>

      <el-form
        ref="formRef"
        v-loading="loading"
        :model="form"
        :rules="rules"
        label-width="120px"
      >
        <!-- 基本信息 -->
        <el-divider content-position="left">基本信息</el-divider>
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="合作项目名称" prop="name">
              <el-input
                v-model="form.name"
                placeholder="请输入合作项目名称"
                maxlength="250"
                show-word-limit
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="合作项目编号" prop="serial">
              <el-input
                v-model="form.serial"
                placeholder="可以不输入，系统会自动生成"
                maxlength="250"
                show-word-limit
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="合作项目类型" prop="contract_type">
              <el-input
                v-model="form.contract_type"
                placeholder="请输入合作项目类型"
                maxlength="250"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="项目状态" prop="status">
              <el-radio-group v-model="form.status">
                <el-radio
                  v-for="item in statusDict"
                  :key="item.value"
                  :value="item.value"
                >
                  {{ item.label }}
                </el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 时间信息 -->
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="签订时间" prop="sign_time">
              <el-date-picker
                v-model="form.sign_time"
                type="date"
                placeholder="选择签订时间"
                style="width: 100%"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="项目开始时间" prop="begin_time">
              <el-date-picker
                v-model="form.begin_time"
                type="date"
                placeholder="选择项目开始时间"
                style="width: 100%"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="项目结束时间" prop="end_time">
              <el-date-picker
                v-model="form.end_time"
                type="date"
                placeholder="选择项目结束时间"
                style="width: 100%"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 企业信息 -->
        <el-divider content-position="left">企业信息</el-divider>
        <el-row :gutter="20">
          <!-- 申请企业信息 -->
          <el-col :span="8">
            <el-card class="info-card" shadow="never">
              <template #header>
                <span class="card-title">申请企业信息</span>
              </template>
              <el-radio-group v-model="selectSysApplier" class="mb-3">
                <el-radio :value="true">系统选择</el-radio>
                <el-radio :value="false">手动输入</el-radio>
              </el-radio-group>
              <div v-if="selectSysApplier">
                <SelectSerialItem
                  v-model:selectItem="selectApplier"
                  label="选择申请企业"
                  :url="API_URLS.company_list"
                  :can-edit="true"
                />
              </div>
              <el-input
                v-else
                v-model="form.applier"
                placeholder="手动输入申请企业名称"
                maxlength="250"
              />
            </el-card>
          </el-col>

          <!-- 资金提供企业信息 -->
          <el-col :span="8">
            <el-card class="info-card" shadow="never">
              <template #header>
                <span class="card-title">资金提供企业信息</span>
              </template>
              <el-radio-group v-model="selectSysFunder" class="mb-3">
                <el-radio :value="true">系统选择</el-radio>
                <el-radio :value="false">手动输入</el-radio>
              </el-radio-group>
              <div v-if="selectSysFunder">
                <SelectSerialItem
                  v-model:selectItem="selectFunder"
                  label="选择资金提供企业"
                  :url="API_URLS.company_list"
                  :can-edit="true"
                />
              </div>
              <el-input
                v-else
                v-model="form.funder"
                placeholder="手动输入资金提供企业名称"
                maxlength="250"
              />
            </el-card>
          </el-col>

          <!-- 业务运营企业信息 -->
          <el-col :span="8">
            <el-card class="info-card" shadow="never">
              <template #header>
                <span class="card-title">业务运营企业信息</span>
              </template>
              <el-radio-group v-model="selectSysScfCompany" class="mb-3">
                <el-radio :value="true">系统选择</el-radio>
                <el-radio :value="false">手动输入</el-radio>
              </el-radio-group>
              <div v-if="selectSysScfCompany">
                <SelectSerialItem
                  v-model:selectItem="selectScfCompany"
                  label="选择业务运营企业"
                  :url="API_URLS.company_list"
                  :can-edit="true"
                />
              </div>
              <el-input
                v-else
                v-model="form.scf_company"
                placeholder="手动输入业务运营企业名称"
                maxlength="250"
              />
            </el-card>
          </el-col>
        </el-row>

        <el-row :gutter="20" class="mt-4">
          <!-- 供应商信息 -->
          <el-col :span="12">
            <el-card class="info-card" shadow="never">
              <template #header>
                <span class="card-title">供应商信息</span>
              </template>
              <el-radio-group v-model="selectSysSupplier" class="mb-3">
                <el-radio :value="true">系统选择</el-radio>
                <el-radio :value="false">手动输入</el-radio>
              </el-radio-group>
              <div v-if="selectSysSupplier">
                <SelectSerialItem
                  v-model:selectItem="selectSupplier"
                  label="选择供应商"
                  :url="API_URLS.supplier_list"
                  :can-edit="true"
                />
              </div>
              <el-input
                v-else
                v-model="form.supplier_name"
                placeholder="手动输入供应商名称"
                maxlength="250"
              />
            </el-card>
          </el-col>

          <!-- 物流/仓储方信息 -->
          <el-col :span="12">
            <el-card class="info-card" shadow="never">
              <template #header>
                <span class="card-title">物流/仓储方信息</span>
              </template>
              <el-radio-group v-model="selectSysWarehouse" class="mb-3">
                <el-radio :value="true">系统选择</el-radio>
                <el-radio :value="false">手动输入</el-radio>
              </el-radio-group>
              <div v-if="selectSysWarehouse">
                <SelectSerialItem
                  v-model:selectItem="selectWarehouse"
                  label="选择物流/仓储方"
                  :url="API_URLS.warehouse_list"
                  :can-edit="true"
                />
              </div>
              <el-input
                v-else
                v-model="form.warehouse_name"
                placeholder="手动输入物流/仓储方名称"
                maxlength="250"
              />
            </el-card>
          </el-col>
        </el-row>
        <el-row :gutter="20" class="mt-4">
          <!-- 中标方信息 -->
          <el-col :span="12">
            <el-card class="info-card" shadow="never">
              <template #header>
                <span class="card-title">中标方信息</span>
              </template>
              <el-radio-group v-model="selectSysBidWinner" class="mb-3">
                <el-radio :value="true">系统选择</el-radio>
                <el-radio :value="false">手动输入</el-radio>
              </el-radio-group>
              <div v-if="selectSysBidWinner">
                <SelectSerialItem
                  v-model:selectItem="selectBidWinner"
                  label="选择中标方"
                  :url="API_URLS.company_list"
                  :can-edit="true"
                />
              </div>
              <el-input
                v-else
                v-model="form.bid_winner_name"
                placeholder="手动输入中标方名称"
                maxlength="250"
              />
            </el-card>
          </el-col>
          <el-col :span="12">
            <el-card class="info-card" shadow="never">
              <template #header>
                <span class="card-title">招标方信息</span>
              </template>
              <el-radio-group v-model="selectSysBidOwner" class="mb-3">
                <el-radio :value="true">系统选择</el-radio>
                <el-radio :value="false">手动输入</el-radio>
              </el-radio-group>
              <div v-if="selectSysBidOwner">
                <SelectSerialItem
                  v-model:selectItem="selectBidOwner"
                  label="选择招标方"
                  :url="API_URLS.company_list"
                  :can-edit="true"
                />
              </div>
              <el-input
                v-else
                v-model="form.bid_owner_name"
                placeholder="手动输入招标方名称"
                maxlength="250"
              />
            </el-card>
          </el-col>
        </el-row>

        <!-- 资金信息 -->
        <el-divider content-position="left">资金信息</el-divider>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="申请资金额度" prop="application_quota">
              <el-input-number
                v-model="form.application_quota"
                :min="0"
                :step="1.0"
                :precision="2"
                style="width: 100%"
                placeholder="请输入申请资金额度"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="确认资金额度" prop="confirm_quota">
              <el-input-number
                v-model="form.confirm_quota"
                :min="0"
                :step="1.0"
                :precision="2"
                style="width: 100%"
                placeholder="请输入确认资金额度"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 费用信息 -->
        <el-divider content-position="left">费用信息</el-divider>
        <el-row :gutter="20">
          <!-- 服务费用信息 -->
          <el-col :span="12">
            <el-card class="info-card" shadow="never">
              <template #header>
                <span class="card-title">服务费用信息</span>
              </template>
              <el-form-item label="计算周期" prop="profit_calc_period">
                <el-radio-group v-model="form.profit_calc_period">
                  <el-radio
                    v-for="item in calcPeriodDict"
                    :key="item.value"
                    :value="item.value"
                  >
                    {{ item.label }}
                  </el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item label="费用费率(%)" prop="profitCalcFeePercent">
                <el-input-number
                  v-model="profitCalcFeePercent"
                  :min="0"
                  :max="100"
                  :precision="0"
                  style="width: 100%"
                  placeholder="输入费用费率（0-100整数，如输入7表示7%即0.07）"
                />
              </el-form-item>
            </el-card>
          </el-col>

          <!-- 违约金信息 -->
          <el-col :span="12">
            <el-card class="info-card" shadow="never">
              <template #header>
                <span class="card-title">违约金信息</span>
              </template>
              <el-form-item label="计算周期" prop="penalty_calc_period">
                <el-radio-group v-model="form.penalty_calc_period">
                  <el-radio
                    v-for="item in calcPeriodDict"
                    :key="item.value"
                    :value="item.value"
                  >
                    {{ item.label }}
                  </el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item label="违约金费率(%)" prop="penaltyCalcFeePercent">
                <el-input-number
                  v-model="penaltyCalcFeePercent"
                  :min="0"
                  :max="100"
                  :precision="0"
                  style="width: 100%"
                  placeholder="输入违约金费率（0-100整数，如输入7表示7%即0.07）"
                />
              </el-form-item>
            </el-card>
          </el-col>
        </el-row>

        <!-- 产品目录选择 -->
        <el-divider content-position="left">产品目录选择</el-divider>
        <el-row :gutter="20">
          <el-col :span="24">
            <el-card class="info-card" shadow="never">
              <template #header>
                <div class="flex justify-between items-center">
                  <span class="card-title">产品目录选择</span>
                  <el-button
                    type="primary"
                    size="small"
                    @click="showSelectCategory"
                  >
                    选择产品目录
                  </el-button>
                </div>
              </template>
              <div
                v-if="selectedCategories && selectedCategories.length > 0"
                class="category-tags"
              >
                <el-tag
                  v-for="category in selectedCategories"
                  :key="category.id"
                  closable
                  type="primary"
                  class="mr-2 mb-2"
                  @close="removeCategory(category)"
                >
                  {{ category.name }}
                </el-tag>
              </div>
              <el-empty
                v-else
                description="暂未选择产品目录"
                :image-size="60"
              />
            </el-card>
          </el-col>
        </el-row>

        <!-- 补充信息 -->
        <el-divider content-position="left">补充信息</el-divider>
        <el-form-item label="合作项目补充信息" prop="desc">
          <el-input
            v-model="form.desc"
            type="textarea"
            :rows="4"
            placeholder="请输入合作项目补充信息"
            maxlength="250"
            show-word-limit
          />
        </el-form-item>

        <!-- 操作按钮 -->
        <el-form-item>
          <el-button type="primary" :loading="submitting" @click="handleSubmit">
            {{ isEdit ? "保存修改" : "创建合约" }}
          </el-button>
          <el-button @click="handleReset">重置</el-button>
          <el-button @click="goBack">取消</el-button>
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import {
  createContract,
  getContract,
  updateContract,
  type ContractCreateRequest,
  type ContractUpdateRequest
} from "@/api/contract";
import SelectSerialItem from "@/components/SelectSerialItem/index.vue";
import { closeCurrentTagAndGo } from "@/store/modules/multiTags";
import { ElMessage, type FormInstance } from "element-plus";
import { computed, onMounted, reactive, ref } from "vue";
import { useRoute, useRouter } from "vue-router";

defineOptions({
  name: "ContractForm"
});

// API地址常量
const API_URLS = {
  warehouse_list: "/api/warehouse/list",
  supplier_list: "/api/supplier/list",
  company_list: "/api/company/list"
};

const router = useRouter();
const route = useRoute();
const formRef = ref<FormInstance>();

// 响应式数据
const loading = ref(false);
const submitting = ref(false);

// 判断是否为编辑模式
const isEdit = computed(() => !!route.query.id);
const contractId = computed(() => route.query.id as string);

// 表单数据
const form = reactive({
  name: "",
  serial: "",
  begin_time: "",
  end_time: "",
  applier: "",
  applier_id: "",
  funder: "",
  funder_id: "",
  scf_company: "",
  scf_company_id: "",
  contract_type: "",
  status: "draft",
  application_quota: 0.0,
  confirm_quota: 0.0,
  used_quota: 0.0,
  desc: "",
  sign_time: "",
  supplier_id: "",
  supplier_name: "",
  bid_winner_id: "",
  bid_winner_name: "",
  bid_owner_id: "",
  bid_owner_name: "",
  warehouse_id: "",
  warehouse_name: "",
  profit_calc_fee: 0.0,
  penalty_calc_fee: 0.0,
  profit_calc_period: "ONCE",
  penalty_calc_period: "ONCE",
  product_category: [] as string[]
});

// 选择的企业对象
const selectSupplier = ref<any>({});
const selectWarehouse = ref<any>({});
const selectBidWinner = ref<any>({});
const selectBidOwner = ref<any>({});
const selectApplier = ref<any>({});
const selectFunder = ref<any>({});
const selectScfCompany = ref<any>({});

// 产品目录选择相关
const selectedCategories = ref<any[]>([]);

// 控制显示选择组件还是输入框的布尔字段
const selectSysSupplier = ref(true);
const selectSysWarehouse = ref(true);
const selectSysBidWinner = ref(true);
const selectSysBidOwner = ref(true);
const selectSysApplier = ref(true);
const selectSysFunder = ref(true);
const selectSysScfCompany = ref(true);

// 项目状态字典
const statusDict = [
  { label: "草稿", value: "draft" },
  { label: "待审核", value: "new" },
  { label: "已审核", value: "processing" },
  { label: "已完成", value: "done" },
  { label: "已过期", value: "expired" }
];

// 计算周期字典
const calcPeriodDict = [
  { label: "单次", value: "ONCE" },
  { label: "按天", value: "DAY" },
  { label: "按月", value: "MONTH" },
  { label: "按年", value: "YEAR" },
  { label: "按季度", value: "QUARTER" }
];

// 计算属性：将0-100的整数转换为两位小数
const profitCalcFeePercent = computed({
  get: () => {
    // 将小数转换为百分比整数显示（如0.07显示为7）
    return Math.round((form.profit_calc_fee || 0) * 100);
  },
  set: value => {
    // 将百分比整数转换为小数存储（如7转换为0.07）
    form.profit_calc_fee = (value || 0) / 100;
  }
});

const penaltyCalcFeePercent = computed({
  get: () => {
    // 将小数转换为百分比整数显示（如0.07显示为7）
    return Math.round((form.penalty_calc_fee || 0) * 100);
  },
  set: value => {
    // 将百分比整数转换为小数存储（如7转换为0.07）
    form.penalty_calc_fee = (value || 0) / 100;
  }
});

// 表单验证规则
const rules = {
  name: [{ required: true, message: "请输入合作项目名称", trigger: "blur" }],
  application_quota: [
    { required: true, message: "请输入申请资金额度", trigger: "blur" },
    {
      validator: (_rule: any, value: any, callback: any) => {
        if (value < 0) {
          callback(new Error("申请资金额度必须大于等于0"));
        } else {
          callback();
        }
      },
      trigger: "blur"
    }
  ]
  // profitCalcFeePercent: [
  //   { required: true, message: "请输入费用费率", trigger: "blur" },
  //   {
  //     validator: (_rule: any, value: any, callback: any) => {
  //       if (value < 0 || value > 100) {
  //         callback(new Error("费用费率必须在0-100之间"));
  //       } else {
  //         callback();
  //       }
  //     },
  //     trigger: "blur"
  //   }
  // ],
  // penaltyCalcFeePercent: [
  //   { required: true, message: "请输入违约金费率", trigger: "blur" },
  //   {
  //     validator: (_rule: any, value: any, callback: any) => {
  //       if (value < 0 || value > 100) {
  //         callback(new Error("违约金费率必须在0-100之间"));
  //       } else {
  //         callback();
  //       }
  //     },
  //     trigger: "blur"
  //   }
  // ]
};

// 返回列表
const goBack = () => {
  // 使用公共方法：删除当前标签页并返回列表页
  closeCurrentTagAndGo(router, "/contract/list");
};

// 生成示例数据
const createDemoData = () => {
  Object.assign(form, {
    name: "示例合作项目",
    contract_type: "供应链金融合作",
    status: "draft",
    sign_time: "2024-01-01",
    begin_time: "2024-01-01",
    end_time: "2024-12-31",
    application_quota: 1000000.0,
    confirm_quota: 800000.0,
    profit_calc_period: "MONTH",
    penalty_calc_period: "DAY",
    desc: "这是一个示例合作项目，用于演示系统功能。"
  });

  // 设置费率
  profitCalcFeePercent.value = 7; // 7%
  penaltyCalcFeePercent.value = 5; // 5%

  // 设置手动输入模式
  selectSysApplier.value = false;
  selectSysFunder.value = false;
  selectSysScfCompany.value = false;
  selectSysSupplier.value = false;
  selectSysWarehouse.value = false;
  selectSysBidWinner.value = false;
  selectSysBidOwner.value = false;

  // 设置企业名称
  form.applier = "示例申请企业有限公司";
  form.funder = "示例资金提供企业有限公司";
  form.scf_company = "示例业务运营企业有限公司";
  form.supplier_name = "示例供应商有限公司";
  form.warehouse_name = "示例物流仓储有限公司";
  form.bid_winner_name = "示例中标方有限公司";
  form.bid_owner_name = "示例招标方有限公司";

  ElMessage.success("示例数据已生成");
};

// 选择企业相关方法已由SelectSerialItem组件替代

// 产品目录选择相关方法
const showSelectCategory = () => {
  ElMessage.info("选择产品目录功能开发中...");
  // TODO: 实现选择产品目录组件
};

const removeCategory = (category: any) => {
  const index = selectedCategories.value.findIndex(
    item => item.id === category.id
  );
  if (index > -1) {
    selectedCategories.value.splice(index, 1);
    // 同时从表单数据中移除
    const categoryIndex = form.product_category.findIndex(
      id => id === category.id
    );
    if (categoryIndex > -1) {
      form.product_category.splice(categoryIndex, 1);
    }
  }
};

// 加载合约数据（编辑模式）
const loadContractData = async () => {
  if (!isEdit.value) return;

  loading.value = true;
  try {
    const response = await getContract(contractId.value);
    if (response.code === 200) {
      const data = response.data;
      Object.assign(form, {
        name: data.name || "",
        serial: data.serial || "",
        begin_time: data.begin_time || "",
        end_time: data.end_time || "",
        applier: data.applier || "",
        funder: data.funder || "",
        contract_type: data.contract_type || "",
        status: data.status || "draft",
        application_quota: data.application_quota || 0.0,
        confirm_quota: data.confirm_quota || 0.0,
        used_quota: data.used_quota || 0.0,
        desc: data.desc || "",
        sign_time: data.sign_time || "",
        supplier_name: data.supplier_name || "",
        bid_winner_name: data.bid_winner_name || "",
        bid_owner_name: data.bid_owner_name || "",
        warehouse_name: data.warehouse_name || "",
        scf_company: data.scf_company || "",
        profit_calc_fee: data.profit_calc_fee || 0.0,
        penalty_calc_fee: data.penalty_calc_fee || 0.0,
        profit_calc_period: data.profit_calc_period || "ONCE",
        penalty_calc_period: data.penalty_calc_period || "ONCE",
        product_category: data.product_category || []
      });
    } else {
      ElMessage.error(response.msg || "获取合约信息失败");
      goBack();
    }
  } catch (error) {
    console.error("获取合约信息失败:", error);
    ElMessage.error("获取合约信息失败");
    goBack();
  } finally {
    loading.value = false;
  }
};

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return;

  await formRef.value.validate(async valid => {
    if (!valid) return;

    submitting.value = true;
    try {
      // 准备提交数据，根据选择模式设置对应的ID字段
      const submitData = { ...form };

      // 设置企业ID字段
      if (selectSysApplier.value && selectApplier.value.id) {
        submitData.applier_id = selectApplier.value.id;
        submitData.applier = selectApplier.value.name;
      }
      if (selectSysFunder.value && selectFunder.value.id) {
        submitData.funder_id = selectFunder.value.id;
        submitData.funder = selectFunder.value.name;
      }
      if (selectSysScfCompany.value && selectScfCompany.value.id) {
        submitData.scf_company_id = selectScfCompany.value.id;
        submitData.scf_company = selectScfCompany.value.name;
      }
      if (selectSysSupplier.value && selectSupplier.value.id) {
        submitData.supplier_id = selectSupplier.value.id;
        submitData.supplier_name = selectSupplier.value.name;
      }
      if (selectSysWarehouse.value && selectWarehouse.value.id) {
        submitData.warehouse_id = selectWarehouse.value.id;
        submitData.warehouse_name = selectWarehouse.value.name;
      }
      if (selectSysBidWinner.value && selectBidWinner.value.id) {
        submitData.bid_winner_id = selectBidWinner.value.id;
        submitData.bid_winner_name = selectBidWinner.value.name;
      }
      if (selectSysBidOwner.value && selectBidOwner.value.id) {
        submitData.bid_owner_id = selectBidOwner.value.id;
        submitData.bid_owner_name = selectBidOwner.value.name;
      }

      if (isEdit.value) {
        // 编辑模式
        const updateData: ContractUpdateRequest = {
          id: contractId.value,
          ...submitData
        };
        const response = await updateContract(updateData);
        if (response.code === 200) {
          ElMessage.success("合约修改成功");
          goBack();
        } else {
          ElMessage.error(response.msg || "修改失败");
        }
      } else {
        // 创建模式
        const createData: ContractCreateRequest = submitData;
        const response = await createContract(createData);
        if (response.code === 200) {
          ElMessage.success("合约创建成功");
          goBack();
        } else {
          ElMessage.error(response.msg || "创建失败");
        }
      }
    } catch (error) {
      console.error("提交失败:", error);
      ElMessage.error("操作失败");
    } finally {
      submitting.value = false;
    }
  });
};

// 重置表单
const handleReset = () => {
  if (isEdit.value) {
    loadContractData();
  } else {
    if (formRef.value) {
      formRef.value.resetFields();
    }
    // 重置选择的企业对象
    selectSupplier.value = {};
    selectWarehouse.value = {};
    selectBidWinner.value = {};
    selectBidOwner.value = {};
    selectApplier.value = {};
    selectFunder.value = {};
    selectScfCompany.value = {};
    selectedCategories.value = [];

    // 重置选择模式为系统选择
    selectSysSupplier.value = true;
    selectSysWarehouse.value = true;
    selectSysBidWinner.value = true;
    selectSysBidOwner.value = true;
    selectSysApplier.value = true;
    selectSysFunder.value = true;
    selectSysScfCompany.value = true;
  }
};

// 初始化
onMounted(() => {
  if (isEdit.value) {
    loadContractData();
  }
});
</script>

<style lang="scss" scoped>
.main {
  margin: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;

  .header-actions {
    display: flex;
    gap: 8px;
  }
}

.info-card {
  border: 1px solid var(--el-border-color-light);

  :deep(.el-card__header) {
    padding: 12px 16px;
    background-color: var(--el-bg-color-page);
    border-bottom: 1px solid var(--el-border-color-light);
  }

  :deep(.el-card__body) {
    padding: 16px;
  }

  .card-title {
    font-weight: 600;
    color: var(--el-text-color-primary);
  }
}

.category-tags {
  min-height: 40px;
  padding: 8px;
  border: 1px dashed var(--el-border-color);
  border-radius: 4px;
  background-color: var(--el-bg-color-page);
}

.mr-2 {
  margin-right: 8px;
}

.mb-2 {
  margin-bottom: 8px;
}

.mb-3 {
  margin-bottom: 12px;
}

.mt-2 {
  margin-top: 8px;
}

.mt-4 {
  margin-top: 16px;
}
</style>
