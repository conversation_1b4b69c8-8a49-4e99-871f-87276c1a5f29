<template>
  <div v-if="contractDetail?.id" class="contract-summary">
    <!-- 合约标题和操作按钮 -->
    <div class="flex justify-between items-center mb-4">
      <div class="flex items-center gap-3">
        <h2 class="text-xl font-semibold">{{ contractDetail.name }}</h2>
        <el-tag
          v-if="contractDetail.status"
          :color="getContractStatusColor(contractDetail.status)"
          effect="dark"
          round
        >
          {{ getContractStatusText(contractDetail.status) }}
        </el-tag>
      </div>
      <div class="flex gap-2">
        <el-button type="primary" @click="handleReviewContract">
          审核合同
        </el-button>
        <el-button type="warning" @click="handleShowAttachment">
          查看附件
        </el-button>
        <el-button @click="handlePrintContract"> 打印合同 </el-button>
      </div>
    </div>

    <!-- 财务信息和资金使用汇总 -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-4 mb-4">
      <!-- 财务信息 -->
      <el-card shadow="never">
        <template #header>
          <span class="font-medium text-blue-600">财务信息</span>
        </template>

        <div class="space-y-4">
          <div class="grid grid-cols-2 gap-4">
            <div>
              <div class="text-sm text-gray-500">确认额度</div>
              <div class="text-lg font-medium">
                ¥{{ formatAmountWithDecimals(contractDetail.confirm_quota) }}
              </div>
            </div>
            <div>
              <div class="text-sm text-gray-500">已用额度</div>
              <div class="text-lg font-medium">
                ¥{{ formatAmountWithDecimals(contractDetail.used_quota) }}
              </div>
            </div>
          </div>

          <div class="grid grid-cols-2 gap-4">
            <div>
              <div class="text-sm text-gray-500">服务费率</div>
              <div class="text-lg font-medium">
                {{ formatPercentage(contractDetail.profit_calc_fee) }}%
              </div>
            </div>
            <div>
              <div class="text-sm text-gray-500">计算周期</div>
              <div class="text-lg font-medium">
                {{ contractDetail.profit_calc_period || "-" }}
              </div>
            </div>
          </div>

          <div class="grid grid-cols-2 gap-4">
            <div>
              <div class="text-sm text-gray-500">违约金费率</div>
              <div class="text-lg font-medium">
                {{ formatPercentage(contractDetail.penalty_calc_fee) }}%
              </div>
            </div>
            <div>
              <div class="text-sm text-gray-500">违约金计算周期</div>
              <div class="text-lg font-medium">
                {{ contractDetail.penalty_calc_period || "-" }}
              </div>
            </div>
          </div>
        </div>
      </el-card>

      <!-- 资金使用汇总情况 -->
      <el-card shadow="never">
        <template #header>
          <span class="font-medium text-blue-600">资金使用汇总情况</span>
        </template>

        <div class="space-y-4">
          <div class="flex justify-between items-center">
            <span class="text-sm text-gray-500">合同订单总金额</span>
            <span class="text-lg font-medium text-orange-600">
              {{ formatAmountWithDecimals(paymentTotal.total_amount) }}（元）
            </span>
          </div>
          <div class="flex justify-between items-center">
            <span class="text-sm text-gray-500">已绑定金额</span>
            <span class="text-lg font-medium text-orange-600">
              {{ formatAmountWithDecimals(paymentTotal.paid_amount) }}（元）
            </span>
          </div>
          <div class="flex justify-between items-center">
            <span class="text-sm text-gray-500">未绑定金额</span>
            <span class="text-lg font-medium text-orange-600">
              {{ formatAmountWithDecimals(paymentTotal.unpay_amount) }}（元）
            </span>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 资金使用历史情况 -->
    <el-card shadow="never">
      <template #header>
        <div class="flex justify-between items-center">
          <span class="font-medium text-blue-600">资金使用历史情况</span>
          <el-button type="primary" @click="handleCreateRepayment">
            创建还款计划
          </el-button>
        </div>
      </template>

      <PureTable
        ref="tableRef"
        :key="`repayment-table-${contractId}`"
        :data="repaymentTableData"
        :columns="repaymentColumns"
        :pagination="repaymentPagination"
        :loading="repaymentLoading"
        :show-overflow-tooltip="false"
        height="400"
        @page-size-change="handleRepaymentPageSizeChange"
        @page-current-change="handleRepaymentPageCurrentChange"
      >
        <template #status="{ row }">
          <el-tag
            :color="getRepaymentStatusColor(row.status)"
            effect="dark"
            round
          >
            {{ getRepaymentStatusText(row.status) }}
          </el-tag>
        </template>

        <template #actions="{ row }">
          <el-button
            type="primary"
            size="small"
            @click="handleShowRepaymentDetail(row)"
          >
            查看明细
          </el-button>
        </template>
      </PureTable>
    </el-card>

    <!-- 审核合同弹窗 -->
    <ReviewDialog
      v-model="showReviewDialog"
      :contract-id="contractId"
      :contract-detail="contractDetail"
      @success="handleReviewSuccess"
    />

    <!-- 附件管理抽屉 -->
    <AttachmentDrawer
      v-model="showAttachmentDrawer"
      :contract-detail="contractDetail"
    />
  </div>
</template>

<script setup lang="ts">
import {
  getContractStatusColor,
  getContractStatusText,
  type ContractInfo
} from "@/api/contract";
import {
  calcPeriodDict,
  createRepayment,
  getRepaymentList,
  getRepaymentStatusColor,
  getRepaymentStatusText,
  type RepaymentInfo
} from "@/api/repayment";
import { countOrderPayment, type PaymentCountResponse } from "@/api/salesOrder";
import { formatDateTime } from "@/utils/formatTime";
import { PureTable } from "@pureadmin/table";
import { ElMessage } from "element-plus";
import { computed, onMounted, ref } from "vue";
import { useRouter } from "vue-router";
import AttachmentDrawer from "./modules/AttachmentDrawer.vue";
import ReviewDialog from "./modules/ReviewDialog.vue";

defineOptions({
  name: "ContractSummary"
});

// Props
interface Props {
  contractId: string;
  contractDetail: ContractInfo;
}

const props = defineProps<Props>();
const router = useRouter();

// 响应式数据
const paymentTotal = ref<PaymentCountResponse>({
  total_amount: 0,
  paid_amount: 0,
  unpay_amount: 0
});

// 还款表格相关
const repaymentTableData = ref<RepaymentInfo[]>([]);
const repaymentLoading = ref(false);
const repaymentPagination = ref({
  page: 1,
  size: 10,
  total: 0
});

// 审核弹窗相关
const showReviewDialog = ref(false);

// 附件抽屉相关
const showAttachmentDrawer = ref(false);

// 表格引用
const tableRef = ref();

// 还款表格列定义
const repaymentColumns = computed(() => {
  if (!repaymentTableData.value) return [];

  return [
    {
      label: "还款计划编号",
      prop: "serial",
      minWidth: 150
    },
    {
      label: "预计利润",
      prop: "profit_amount",
      minWidth: 120,
      cellRenderer: ({ row }) =>
        row.profit_amount
          ? `¥${formatAmountWithDecimals(row.profit_amount)}`
          : "-"
    },
    {
      label: "预计本金",
      prop: "principal_amount",
      minWidth: 120,
      cellRenderer: ({ row }) =>
        row.principal_amount
          ? `¥${formatAmountWithDecimals(row.principal_amount)}`
          : "-"
    },
    {
      label: "预计总额",
      prop: "total_amount",
      minWidth: 120,
      cellRenderer: ({ row }) =>
        row.total_amount
          ? `¥${formatAmountWithDecimals(row.total_amount)}`
          : "-"
    },
    {
      label: "剩余利润",
      prop: "profit_remain",
      minWidth: 120,
      cellRenderer: ({ row }) =>
        row.profit_remain
          ? `¥${formatAmountWithDecimals(row.profit_remain)}`
          : "-"
    },
    {
      label: "剩余本金",
      prop: "principal_remain",
      minWidth: 120,
      cellRenderer: ({ row }) =>
        row.principal_remain
          ? `¥${formatAmountWithDecimals(row.principal_remain)}`
          : "-"
    },
    {
      label: "剩余总额",
      prop: "total_remain",
      minWidth: 120,
      cellRenderer: ({ row }) =>
        row.total_remain
          ? `¥${formatAmountWithDecimals(row.total_remain)}`
          : "-"
    },
    {
      label: "开始日期",
      prop: "begin_date",
      minWidth: 120
    },
    {
      label: "结束日期",
      prop: "end_date",
      minWidth: 120
    },
    {
      label: "计划状态",
      prop: "status",
      minWidth: 100,
      slot: "status"
    },
    {
      label: "创建时间",
      prop: "created_at",
      minWidth: 160,
      cellRenderer: ({ row }) =>
        row.created_at ? formatDateTime(row.created_at) : "-"
    },
    {
      label: "操作",
      fixed: "right",
      width: 120,
      slot: "actions"
    }
  ];
});

// 工具函数
const formatAmountWithDecimals = (amount: number | string) => {
  if (!amount) return "0.00";
  return Number(amount).toLocaleString("zh-CN", {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  });
};

const formatPercentage = (value: number | string) => {
  if (!value) return "0.00";
  return (Number(value) * 100).toFixed(2);
};

// 获取还款计划数据
const getRepaymentData = async () => {
  if (!props.contractId) return;

  repaymentLoading.value = true;
  try {
    const params = {
      page: {
        page: repaymentPagination.value.page,
        limit: repaymentPagination.value.size
      },
      options: {
        order_by: "created_at",
        desc: true
      },
      params: [
        {
          var: "contract_id",
          val: props.contractId
        }
      ]
    };

    const { data } = await getRepaymentList(params);
    if (data) {
      repaymentTableData.value = data.data || [];
      repaymentPagination.value.total = data.total || 0;
    }
  } catch (error) {
    console.error("获取还款数据失败:", error);
    ElMessage.error("获取还款数据失败");
  } finally {
    repaymentLoading.value = false;
  }
};

// 获取支付统计数据
const getPaymentTotal = async () => {
  if (!props.contractId) return;

  try {
    const { data } = await countOrderPayment({ id: props.contractId });
    if (data) {
      paymentTotal.value = data;
    }
  } catch (error) {
    console.error("获取支付统计失败:", error);
    ElMessage.error("获取支付统计失败");
  }
};

// 事件处理函数
const handleReviewContract = () => {
  showReviewDialog.value = true;
};

const handleReviewSuccess = () => {
  // 审核成功后刷新页面数据
  window.location.reload();
};

const handleShowAttachment = () => {
  showAttachmentDrawer.value = true;
};

const handlePrintContract = () => {
  window.print();
};

const handleCreateRepayment = async () => {
  try {
    const repaymentData = {
      contract_id: props.contractId,
      profit_calc_fee: props.contractDetail.profit_calc_fee,
      penalty_calc_fee: props.contractDetail.penalty_calc_fee,
      profit_calc_period:
        calcPeriodDict[props.contractDetail.profit_calc_period || ""] || "DAY",
      status: "draft"
    };

    const response = await createRepayment(repaymentData);
    if (response.code === 200) {
      router.push({
        path: "/repayment/detail",
        query: { id: response.data }
      });
    } else {
      ElMessage.error("创建还款计划失败，请重试");
    }
  } catch (error) {
    console.error("创建还款计划失败:", error);
    ElMessage.error("创建还款计划失败，请重试");
  }
};

const handleShowRepaymentDetail = (row: RepaymentInfo) => {
  router.push({
    path: "/repayment/detail",
    query: { id: row.id }
  });
};

const handleRepaymentPageSizeChange = (size: number) => {
  repaymentPagination.value.size = size;
  getRepaymentData();
};

const handleRepaymentPageCurrentChange = (page: number) => {
  repaymentPagination.value.page = page;
  getRepaymentData();
};

// 生命周期
onMounted(async () => {
  if (props.contractId) {
    await Promise.all([getRepaymentData(), getPaymentTotal()]);
  }
});
</script>

<style lang="scss" scoped>
.contract-summary {
  padding: 0;
}
</style>
