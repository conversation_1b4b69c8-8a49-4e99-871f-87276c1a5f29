<template>
  <div class="main">
    <el-card shadow="never">
      <template #header>
        <div class="card-header">
          <span class="font-medium">菜单管理</span>
        </div>
      </template>

      <div class="mb-4">
        <el-button type="primary" @click="handleAdd"> 新增根菜单 </el-button>
        <el-button @click="handleExpandAll">展开全部</el-button>
        <el-button @click="handleCollapseAll">收起全部</el-button>
        <el-button :loading="loading" @click="handleRefresh">
          <template #icon>
            <IconifyIconOffline icon="ep:refresh" />
          </template>
          刷新
        </el-button>
      </div>

      <el-table
        ref="tableRef"
        v-loading="loading"
        :data="tableData"
        style="width: 100%"
        row-key="id"
        :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
        :default-expand-all="false"
        border
        stripe
      >
        <!-- 固定左侧：菜单名称列 -->
        <el-table-column
          prop="title"
          label="菜单名称"
          width="200"
          fixed="left"
          :show-overflow-tooltip="false"
        />

        <!-- 中间可滚动列 -->
        <el-table-column
          prop="name"
          label="路由名称"
          width="150"
          :show-overflow-tooltip="false"
        />
        <el-table-column
          prop="path"
          label="路由路径"
          width="200"
          :show-overflow-tooltip="false"
        />
        <el-table-column
          prop="component"
          label="组件路径"
          width="200"
          :show-overflow-tooltip="false"
        />
        <el-table-column prop="icon" label="图标" width="80" align="center">
          <template #default="{ row }">
            <div class="flex items-center justify-center">
              <IconifyIconOnline v-if="row.icon" :icon="row.icon" />
              <span v-else> - </span>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="order" label="排序" width="80" align="center" />
        <el-table-column
          prop="hidden"
          label="是否隐藏"
          width="100"
          align="center"
        >
          <template #default="{ row }">
            <el-tag
              :type="row.hidden === 'yes' ? 'danger' : 'success'"
              size="small"
            >
              {{ row.hidden === "yes" ? "隐藏" : "显示" }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="active" label="状态" width="100" align="center">
          <template #default="{ row }">
            <el-tag
              :type="row.active === 'yes' ? 'success' : 'danger'"
              size="small"
            >
              {{ row.active === "yes" ? "启用" : "禁用" }}
            </el-tag>
          </template>
        </el-table-column>

        <!-- 固定右侧：操作列 -->
        <el-table-column label="操作" width="280" fixed="right" align="center">
          <template #default="{ row }">
            <el-button-group>
              <el-button
                size="small"
                type="success"
                :icon="Plus"
                @click="handleAddChild(row)"
              >
                子菜单
              </el-button>
              <el-button
                size="small"
                type="primary"
                :icon="Edit"
                @click="handleEdit(row)"
              >
                编辑
              </el-button>
              <el-button
                size="small"
                type="danger"
                :icon="Delete"
                @click="handleDelete(row)"
              >
                删除
              </el-button>
            </el-button-group>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import type { MenuInfo } from "@/api/menu";
import { deleteMenu, getMenuList } from "@/api/menu";
import { IconifyIconOffline, IconifyIconOnline } from "@/components/ReIcon";
import { message } from "@/utils/message";
import { Delete, Edit, Plus } from "@element-plus/icons-vue";
import { ElMessageBox, type ElTable } from "element-plus";
import { onMounted, ref } from "vue";
import {
  openAddChildMenuDrawer,
  openAddRootMenuDrawer,
  openEditMenuDrawer
} from "./modules/useMenuFormDrawer";

defineOptions({
  name: "MenuList"
});

const tableRef = ref<InstanceType<typeof ElTable>>();
const tableData = ref<MenuInfo[]>([]);
const loading = ref(false);

// 获取菜单列表
const getTableData = async () => {
  try {
    loading.value = true;
    const params = {
      options: {
        order_by: "order",
        desc: false
      },
      page: {
        page: 1,
        limit: 1000 // 获取所有数据，因为是树形结构
      }
    };

    const response = await getMenuList(params);
    if (response.code === 200) {
      // 后端返回的是 { data: { data: MenuInfo[], total, page, size }, ... }
      // 我们需要的是 data.data 中的菜单数组
      tableData.value = response.data.data;
    } else {
      message(response.msg || "获取菜单列表失败", { type: "error" });
    }
  } catch (error) {
    console.error("获取菜单列表失败:", error);
    message("获取菜单列表失败", { type: "error" });
  } finally {
    loading.value = false;
  }
};

// 新增根菜单
const handleAdd = () => {
  openAddRootMenuDrawer({
    onSuccess: () => {
      getTableData();
    }
  });
};

// 添加子菜单
const handleAddChild = (row: MenuInfo) => {
  openAddChildMenuDrawer(row.name, {
    onSuccess: () => {
      getTableData();
    }
  });
};

// 编辑菜单
const handleEdit = (row: MenuInfo) => {
  openEditMenuDrawer(row, {
    onSuccess: () => {
      getTableData();
    }
  });
};

// 删除菜单
const handleDelete = async (row: MenuInfo) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除菜单 "${row.title}" 吗？`,
      "删除确认",
      {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }
    );

    const response = await deleteMenu(row.id);
    if (response.code === 200) {
      message("删除成功", { type: "success" });
      getTableData();
    } else {
      message(response.msg || "删除失败", { type: "error" });
    }
  } catch (error) {
    if (error !== "cancel") {
      console.error("删除菜单失败:", error);
      message("删除失败", { type: "error" });
    }
  }
};

// 展开全部
const handleExpandAll = () => {
  tableData.value.forEach(row => {
    tableRef.value?.toggleRowExpansion(row, true);
  });
};

// 收起全部
const handleCollapseAll = () => {
  tableData.value.forEach(row => {
    tableRef.value?.toggleRowExpansion(row, false);
  });
};

// 刷新数据
const handleRefresh = () => {
  getTableData();
};

// 组件挂载时获取数据
onMounted(() => {
  getTableData();
});
</script>

<style scoped>
.main {
  margin: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* 表格样式增强 */
:deep(.el-table) {
  border-radius: 8px;
  overflow: hidden;
}

:deep(.el-table__header-wrapper) {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

:deep(.el-table__header-wrapper .el-table__header th) {
  background: white;
  color: black;
  font-weight: 600;
  border-bottom: none;
}

:deep(.el-table__fixed-header-wrapper .el-table__header th) {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

:deep(.el-table__row:hover > td) {
  background-color: #f5f7fa !important;
}

:deep(.el-table__fixed-right) {
  box-shadow: -2px 0 8px rgba(0, 0, 0, 0.1);
}

:deep(.el-table__fixed-left) {
  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.1);
}

/* 按钮组样式 */
/* 只对不在按钮组内的按钮添加间距 */
:deep(.el-button + .el-button:not(.el-button-group .el-button)) {
  margin-left: 8px;
}

/* 确保按钮组内的按钮没有额外间距 */
:deep(.el-button-group .el-button + .el-button) {
  margin-left: 0;
}

/* 按钮组样式增强 */
:deep(.el-button-group) {
  display: inline-flex;
  border-radius: 6px;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

:deep(.el-button-group .el-button) {
  border-radius: 0;
  border-right: 1px solid rgba(255, 255, 255, 0.2);
}

:deep(.el-button-group .el-button:first-child) {
  border-top-left-radius: 6px;
  border-bottom-left-radius: 6px;
}

:deep(.el-button-group .el-button:last-child) {
  border-top-right-radius: 6px;
  border-bottom-right-radius: 6px;
  border-right: none;
}

/* 标签样式 */
:deep(.el-tag) {
  border-radius: 12px;
  font-weight: 500;
}

/* 树形表格缩进样式 */
:deep(.el-table__indent) {
  padding-left: 20px;
}

:deep(.el-table__expand-icon) {
  color: #409eff;
}
</style>
