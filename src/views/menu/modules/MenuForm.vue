<script setup lang="ts">
import { IconSelect } from "@/components/ReIcon";
import { computed, ref } from "vue";

// 声明 props 类型
export interface MenuFormProps {
  formData?: {
    id?: string;
    name: string;
    order: number;
    path?: string;
    component?: string;
    redirect?: string;
    active?: string;
    title?: string;
    icon?: string;
    keep_alive?: string;
    hidden?: string;
    is_link?: string;
    parent?: string;
    remark?: string;
  };
  isParent?: boolean;
  parentName?: string;
}

// 声明 props 默认值
const props = withDefaults(defineProps<MenuFormProps>(), {
  formData: () => ({
    name: "",
    order: 1,
    path: "",
    component: "",
    redirect: "",
    active: "yes",
    title: "",
    icon: "",
    keep_alive: "no",
    hidden: "no",
    is_link: "no",
    parent: "",
    remark: ""
  }),
  isParent: true,
  parentName: ""
});

// vue 规定所有的 prop 都遵循着单向绑定原则，直接修改 prop 时，Vue 会抛出警告。
// 此处的写法仅仅是为了消除警告。因为对一个 reactive 对象执行 ref，
// 返回 Ref 对象的 value 值仍为传入的 reactive 对象
const newFormData = ref(props.formData);

// 如果不是父菜单，设置parent值
if (!props.isParent && props.parentName) {
  newFormData.value.parent = props.parentName;
}

// 选项数据
const yesNoOptions = [
  { label: "是", value: "yes" },
  { label: "否", value: "no" }
];

// 计算属性：是否显示parent字段
const showParentField = computed(() => {
  return !props.isParent;
});
</script>

<template>
  <el-form :model="newFormData" label-width="120px" label-position="top">
    <div class="grid grid-cols-2 gap-4">
      <el-form-item label="菜单名称" prop="title">
        <el-input
          v-model="newFormData.title"
          placeholder="请输入菜单名称"
          clearable
        />
      </el-form-item>

      <el-form-item label="路由名称" prop="name">
        <el-input
          v-model="newFormData.name"
          placeholder="请输入路由名称"
          clearable
        />
      </el-form-item>
    </div>

    <div class="grid grid-cols-2 gap-4">
      <el-form-item label="路由路径" prop="path">
        <el-input
          v-model="newFormData.path"
          placeholder="请输入路由路径"
          clearable
        />
      </el-form-item>

      <el-form-item label="组件路径" prop="component">
        <el-input
          v-model="newFormData.component"
          placeholder="请输入组件路径"
          clearable
        />
      </el-form-item>
    </div>

    <div class="grid grid-cols-2 gap-4">
      <el-form-item label="重定向路径" prop="redirect">
        <el-input
          v-model="newFormData.redirect"
          placeholder="请输入重定向路径"
          clearable
        />
      </el-form-item>

      <el-form-item label="图标" prop="icon">
        <IconSelect v-model="newFormData.icon" class="w-full" />
      </el-form-item>
    </div>

    <div class="grid grid-cols-2 gap-4">
      <el-form-item label="排序" prop="order">
        <el-input-number
          v-model="newFormData.order"
          :min="1"
          :max="999"
          placeholder="请输入排序"
        />
      </el-form-item>
      <el-form-item label="父级菜单" prop="parent">
        <el-input
          v-model="newFormData.parent"
          placeholder="请输入父级菜单名称"
          clearable
        />
      </el-form-item>
    </div>

    <div class="grid grid-cols-2 gap-4">
      <el-form-item label="是否隐藏">
        <el-radio-group v-model="newFormData.hidden">
          <el-radio
            v-for="option in yesNoOptions"
            :key="option.value"
            :value="option.value"
          >
            {{ option.label }}
          </el-radio>
        </el-radio-group>
      </el-form-item>

      <el-form-item label="是否启用">
        <el-radio-group v-model="newFormData.active">
          <el-radio
            v-for="option in yesNoOptions"
            :key="option.value"
            :value="option.value"
          >
            {{ option.label }}
          </el-radio>
        </el-radio-group>
      </el-form-item>
    </div>

    <div class="grid grid-cols-2 gap-4">
      <el-form-item label="是否缓存">
        <el-radio-group v-model="newFormData.keep_alive">
          <el-radio
            v-for="option in yesNoOptions"
            :key="option.value"
            :value="option.value"
          >
            {{ option.label }}
          </el-radio>
        </el-radio-group>
      </el-form-item>

      <el-form-item label="是否外链">
        <el-radio-group v-model="newFormData.is_link">
          <el-radio
            v-for="option in yesNoOptions"
            :key="option.value"
            :value="option.value"
          >
            {{ option.label }}
          </el-radio>
        </el-radio-group>
      </el-form-item>
    </div>

    <el-form-item label="备注" prop="remark">
      <el-input
        v-model="newFormData.remark"
        type="textarea"
        :rows="4"
        placeholder="请输入备注信息"
        maxlength="200"
        show-word-limit
      />
    </el-form-item>
  </el-form>
</template>
