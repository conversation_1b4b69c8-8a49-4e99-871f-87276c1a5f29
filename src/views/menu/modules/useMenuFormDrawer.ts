import type {
  MenuCreateRequest,
  MenuInfo,
  MenuUpdateRequest
} from "@/api/menu";
import { createMenu, updateMenu } from "@/api/menu";
import { addDrawer } from "@/components/ReDrawer";
import { message } from "@/utils/message";
import { cloneDeep } from "@pureadmin/utils";
import MenuForm, { type MenuFormProps } from "./MenuForm.vue";

export interface UseMenuFormDrawerOptions {
  onSuccess?: () => void;
}

/**
 * 打开菜单表单抽屉
 * @param menuData 菜单数据（编辑时传入）
 * @param options 配置选项
 */
export function openMenuFormDrawer(
  menuData?: Partial<MenuInfo>,
  options: UseMenuFormDrawerOptions & {
    isParent?: boolean;
    parentName?: string;
  } = {}
) {
  const { onSuccess, isParent = true, parentName = "" } = options;
  const isEdit = !!menuData?.id;

  // 准备表单数据
  const formData = {
    id: menuData?.id || "",
    name: menuData?.name || "",
    order: menuData?.order || 1,
    path: menuData?.path || "",
    component: menuData?.component || "",
    redirect: menuData?.redirect || "",
    active: menuData?.active || "yes",
    title: menuData?.title || "",
    icon: menuData?.icon || "",
    keep_alive: menuData?.keep_alive || "no",
    hidden: menuData?.hidden || "no",
    is_link: menuData?.is_link || "no",
    parent: menuData?.parent || (isParent ? "" : parentName),
    remark: menuData?.remark || ""
  };

  addDrawer({
    title: isEdit ? "编辑菜单" : isParent ? "新增根菜单" : "新增子菜单",
    size: "600px",
    closeOnClickModal: false,
    contentRenderer: () => MenuForm,
    props: {
      formData: cloneDeep(formData),
      isParent,
      parentName
    },
    beforeSure: async (done, { options }) => {
      const { formData: currentFormData } = options.props as MenuFormProps;

      try {
        // 确保数字类型
        const baseData = {
          ...currentFormData,
          order: Number(currentFormData.order)
        };

        if (isEdit) {
          // 编辑时需要包含id字段
          if (!baseData.id) {
            throw new Error("编辑菜单时缺少ID");
          }
          const updateData: MenuUpdateRequest = {
            id: baseData.id,
            name: baseData.name,
            order: baseData.order,
            path: baseData.path,
            component: baseData.component,
            redirect: baseData.redirect,
            active: baseData.active,
            title: baseData.title,
            icon: baseData.icon,
            keep_alive: baseData.keep_alive,
            hidden: baseData.hidden,
            is_link: baseData.is_link,
            parent: baseData.parent,
            remark: baseData.remark
          };
          await updateMenu(updateData);
          message("菜单更新成功", { type: "success" });
        } else {
          // 创建时不需要id字段
          const createData: MenuCreateRequest = {
            name: baseData.name,
            order: baseData.order,
            path: baseData.path,
            component: baseData.component,
            redirect: baseData.redirect,
            active: baseData.active,
            title: baseData.title,
            icon: baseData.icon,
            keep_alive: baseData.keep_alive,
            hidden: baseData.hidden,
            is_link: baseData.is_link,
            parent: baseData.parent,
            remark: baseData.remark
          };
          await createMenu(createData);
          message("菜单创建成功", { type: "success" });
        }

        onSuccess?.();
        done(); // 关闭抽屉
      } catch (error) {
        console.error("提交失败:", error);
        message("操作失败，请重试", { type: "error" });
        // 不调用 done()，保持抽屉打开
      }
    }
  });
}

/**
 * 打开新增根菜单抽屉
 */
export function openAddRootMenuDrawer(options: UseMenuFormDrawerOptions = {}) {
  return openMenuFormDrawer(undefined, { ...options, isParent: true });
}

/**
 * 打开新增子菜单抽屉
 */
export function openAddChildMenuDrawer(
  parentName: string,
  options: UseMenuFormDrawerOptions = {}
) {
  return openMenuFormDrawer(undefined, {
    ...options,
    isParent: false,
    parentName
  });
}

/**
 * 打开编辑菜单抽屉
 */
export function openEditMenuDrawer(
  menuData: MenuInfo,
  options: UseMenuFormDrawerOptions = {}
) {
  const isParent = !menuData.parent || menuData.parent === "";
  return openMenuFormDrawer(menuData, {
    ...options,
    isParent,
    parentName: menuData.parent || ""
  });
}
