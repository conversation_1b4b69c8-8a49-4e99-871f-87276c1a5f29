<script setup lang="ts">
import { useRouter } from "vue-router";

defineOptions({
  name: "Welcome"
});

const router = useRouter();

const viewContract = () => {
  router.push({ name: "contractList" });
};

const viewOrder = () => {
  router.push({ name: "orderList" });
};

const importOrder = () => {
  router.push({ name: "importRecord" });
};
</script>

<template>
  <div class="p-6">
    <h1 class="text-2xl font-bold mb-6">资金管理系统 - 快速导航栏</h1>

    <div class="flex flex-wrap gap-4">
      <el-button
        type="primary"
        size="large"
        class="px-8 py-4"
        @click="viewContract"
      >
        查看合同列表
      </el-button>

      <el-button
        type="success"
        size="large"
        class="px-8 py-4"
        @click="viewOrder"
      >
        查看订单列表
      </el-button>

      <el-button
        type="warning"
        size="large"
        class="px-8 py-4"
        @click="importOrder"
      >
        导入订单
      </el-button>
    </div>
  </div>
</template>
