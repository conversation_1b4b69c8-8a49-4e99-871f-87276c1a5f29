<script setup lang="ts">
import { useRouter } from "vue-router";
import { onMounted } from "vue";

defineOptions({
  name: "Welcome"
});

const router = useRouter();

onMounted(() => {
  console.log("Welcome 页面已挂载");
});

const viewContract = () => {
  router.push({ name: "contractList" });
};

const viewOrder = () => {
  router.push({ name: "orderList" });
};

const importOrder = () => {
  router.push({ name: "importRecord" });
};
</script>

<template>
  <div style="padding: 24px; background-color: #f5f5f5; min-height: 500px">
    <h1 style="color: #333; font-size: 24px; margin-bottom: 20px">
      🎉 欢迎使用资金管理系统！
    </h1>

    <div
      style="
        background: white;
        padding: 20px;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      "
    >
      <p style="color: #666; font-size: 16px; margin-bottom: 20px">
        系统已成功加载，您可以开始使用以下功能：
      </p>

      <div style="display: flex; gap: 16px; flex-wrap: wrap">
        <button
          style="
            background: #409eff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 4px;
            cursor: pointer;
          "
          @click="viewContract"
        >
          查看合同列表
        </button>

        <button
          style="
            background: #67c23a;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 4px;
            cursor: pointer;
          "
          @click="viewOrder"
        >
          查看订单列表
        </button>

        <button
          style="
            background: #e6a23c;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 4px;
            cursor: pointer;
          "
          @click="importOrder"
        >
          导入订单
        </button>
      </div>
    </div>

    <div style="margin-top: 20px; color: #999; font-size: 14px">
      当前时间: {{ new Date().toLocaleString() }}
    </div>
  </div>
</template>
