import type { RoleInfo } from "@/api/role";
import { addDrawer } from "@/components/ReDrawer";
import RoleUserBindDrawer from "./RoleUserBindDrawer.vue";

export interface UseRoleUserBindDrawerOptions {
  onSuccess?: () => void;
}

/**
 * 打开角色用户绑定抽屉
 * @param roleData 角色数据
 * @param options 配置选项
 */
export function openRoleUserBindDrawer(
  roleData: RoleInfo,
  options: UseRoleUserBindDrawerOptions = {}
) {
  const { onSuccess } = options;

  addDrawer({
    title: "角色用户绑定管理",
    size: "1000px",
    closeOnClickModal: true,
    hideFooter: true, // 隐藏底部按钮，因为这个drawer主要用于展示和管理
    contentRenderer: () => RoleUserBindDrawer,
    props: {
      roleData
    },
    closeCallBack: () => {
      // 关闭时的回调
      onSuccess?.();
    }
  });
}
