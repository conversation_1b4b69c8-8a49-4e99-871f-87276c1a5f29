import type { RoleInfo } from "@/api/role";
import { roleRelateMenu, roleRelatePermission } from "@/api/role";
import { addDrawer } from "@/components/ReDrawer";
import { message } from "@/utils/message";
import RolePermissionDrawer from "./RolePermissionDrawer.vue";

export interface UseRolePermissionDrawerOptions {
  onSuccess?: () => void;
}

/**
 * 打开角色权限配置抽屉
 * @param roleData 角色数据
 * @param permissionType 权限类型
 * @param options 配置选项
 */
export function openRolePermissionDrawer(
  roleData: RoleInfo,
  permissionType: "menu" | "button",
  options: UseRolePermissionDrawerOptions = {}
) {
  const { onSuccess } = options;

  addDrawer({
    title: `配置${permissionType === "menu" ? "菜单" : "按钮"}权限`,
    size: "600px",
    closeOnClickModal: false,
    contentRenderer: () => RolePermissionDrawer,
    props: {
      roleData,
      permissionType
    },
    beforeSure: async (done, { options }) => {
      try {
        // 这里需要通过ref获取组件实例，暂时使用模拟数据
        const selectedPermissions: string[] = [];

        if (permissionType === "menu") {
          // 绑定菜单权限
          const response = await roleRelateMenu({
            role_id: roleData.id,
            menu_ids: selectedPermissions
          });
          if (response.code === 200) {
            message("菜单权限配置成功", { type: "success" });
          } else {
            message(response.msg || "菜单权限配置失败", { type: "error" });
            return; // 不关闭抽屉
          }
        } else {
          // 绑定按钮权限
          const response = await roleRelatePermission({
            role_id: roleData.id,
            permission_ids: selectedPermissions
          });
          if (response.code === 200) {
            message("按钮权限配置成功", { type: "success" });
          } else {
            message(response.msg || "按钮权限配置失败", { type: "error" });
            return; // 不关闭抽屉
          }
        }

        onSuccess?.();
        done(); // 关闭抽屉
      } catch (error) {
        console.error("权限配置失败:", error);
        message("权限配置失败，请重试", { type: "error" });
        // 不调用 done()，保持抽屉打开
      }
    }
  });
}

/**
 * 打开菜单权限配置抽屉
 */
export function openMenuPermissionDrawer(
  roleData: RoleInfo,
  options: UseRolePermissionDrawerOptions = {}
) {
  return openRolePermissionDrawer(roleData, "menu", options);
}

/**
 * 打开按钮权限配置抽屉
 */
export function openButtonPermissionDrawer(
  roleData: RoleInfo,
  options: UseRolePermissionDrawerOptions = {}
) {
  return openRolePermissionDrawer(roleData, "button", options);
}
