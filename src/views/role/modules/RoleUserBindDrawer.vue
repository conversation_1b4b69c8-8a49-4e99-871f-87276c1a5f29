<script setup lang="ts">
import type { RoleInfo } from "@/api/role";
import type { FlexibleParams, UpdateFields } from "@/api/types";
import type { UserInfo } from "@/api/user";
import { getUserByRole, updateUserField } from "@/api/user";
import { addDrawer } from "@/components/ReDrawer";
import { IconifyIconOnline } from "@/components/ReIcon";
import SelectUser from "@/components/SelectUser/index.vue";
import { message } from "@/utils/message";
import { onMounted, ref } from "vue";

// 声明 props 类型
export interface RoleUserBindProps {
  roleData: RoleInfo;
}

// 声明 props
const props = defineProps<RoleUserBindProps>();

// 响应式数据
const loading = ref(false);
const userList = ref<UserInfo[]>([]);

// 移除未使用的columns变量

// 获取绑定的用户列表
const getUserList = async () => {
  loading.value = true;
  try {
    const params: FlexibleParams = {
      id: { id: props.roleData.id },
      page: {
        page: 1,
        limit: 100 // 暂时获取所有数据，后续可以添加分页
      },
      options: {
        order_by: "created_at",
        desc: true
      }
    };

    const response = await getUserByRole(params);
    if (response.code === 200) {
      userList.value = response.data.data;
    } else {
      message(response.msg || "获取用户列表失败", { type: "error" });
    }
  } catch (error) {
    console.error("获取用户列表失败:", error);
    message("获取用户列表失败", { type: "error" });
  } finally {
    loading.value = false;
  }
};

// 绑定用户到角色
const handleBindUser = async (user: UserInfo) => {
  try {
    const updateFields: UpdateFields = {
      id: user.id,
      update_fields: [{ field: "role_id", value: props.roleData.id }]
    };

    const response = await updateUserField(user.id, updateFields);
    if (response.code === 200) {
      message(`用户 ${user.username} 绑定成功`, { type: "success" });
      // 更新本地数据
      const userIndex = userList.value.findIndex(u => u.id === user.id);
      if (userIndex !== -1) {
        userList.value[userIndex].role_id = props.roleData.id;
      }
      getUserList(); // 刷新列表
    } else {
      message(response.msg || "绑定失败", { type: "error" });
    }
  } catch (error) {
    console.error("绑定用户失败:", error);
    message("绑定用户失败", { type: "error" });
  }
};

// 解绑用户角色
const handleUnbindUser = async (user: UserInfo) => {
  try {
    const updateFields: UpdateFields = {
      id: user.id,
      update_fields: [{ field: "role_id", value: "None" }]
    };

    const response = await updateUserField(user.id, updateFields);
    if (response.code === 200) {
      message(`用户 ${user.username} 解绑成功`, { type: "success" });
      // 更新本地数据
      const userIndex = userList.value.findIndex(u => u.id === user.id);
      if (userIndex !== -1) {
        userList.value[userIndex].role_id = "";
      }
      getUserList(); // 刷新列表
    } else {
      message(response.msg || "解绑失败", { type: "error" });
    }
  } catch (error) {
    console.error("解绑用户失败:", error);
    message("解绑用户失败", { type: "error" });
  }
};

// 移除用户绑定（解绑的别名）
const handleRemoveUser = (user: UserInfo) => {
  handleUnbindUser(user);
};

// 添加用户绑定
const handleAddUser = () => {
  // 打开选择用户的drawer
  openSelectUserDrawer();
};

// 处理用户选择
const handleUserSelected = (user: UserInfo) => {
  // 绑定选中的用户
  handleBindUser(user);
};

// 打开选择用户drawer
const openSelectUserDrawer = () => {
  // 构建过滤参数，排除已绑定当前角色的用户
  const filterParams = [
    {
      var: "role_id",
      val: props.roleData.id,
      operator: "!="
    }
  ];

  addDrawer({
    title: "选择用户进行绑定",
    size: "1000px",
    closeOnClickModal: true,
    hideFooter: true,
    contentRenderer: () => SelectUser,
    props: {
      filterParams: filterParams,
      onUserSelected: handleUserSelected
    },
    closeCallBack: () => {
      // 关闭时刷新用户列表
      getUserList();
    }
  });
};

// 组件挂载时获取数据
onMounted(() => {
  getUserList();
});
</script>

<template>
  <div class="role-user-bind-drawer">
    <div class="header mb-4">
      <div class="title">
        <span class="font-medium">角色 "{{ roleData.name }}" 的用户绑定</span>
      </div>
      <el-button type="primary" @click="handleAddUser">
        <template #icon>
          <IconifyIconOnline icon="ep:plus" />
        </template>
        添加用户
      </el-button>
    </div>

    <el-table
      :data="userList"
      :loading="loading"
      style="width: 100%"
      border
      stripe
    >
      <el-table-column prop="login_name" label="用户名" min-width="120" />
      <el-table-column prop="username" label="真实姓名" min-width="120" />
      <el-table-column
        prop="is_admin"
        label="是否管理员"
        width="100"
        align="center"
      >
        <template #default="{ row }">
          <el-tag :type="row.is_admin ? 'success' : 'info'">
            {{ row.is_admin ? "是" : "否" }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="is_active" label="状态" width="100" align="center">
        <template #default="{ row }">
          <el-tag :type="row.is_active ? 'success' : 'danger'">
            {{ row.is_active ? "启用" : "禁用" }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="created_at" label="创建时间" width="160" />
      <el-table-column label="操作" width="100" fixed="right" align="center">
        <template #default="{ row }">
          <el-button
            link
            type="danger"
            size="small"
            @click="handleRemoveUser(row)"
          >
            移除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <div v-if="userList.length === 0 && !loading" class="empty-state">
      <el-empty description="暂无绑定用户" />
    </div>
  </div>
</template>

<style scoped>
.role-user-bind-drawer {
  padding: 20px;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.title {
  font-size: 16px;
}

.font-medium {
  font-weight: 500;
}

.mb-4 {
  margin-bottom: 16px;
}

.empty-state {
  margin-top: 40px;
  text-align: center;
}
</style>
