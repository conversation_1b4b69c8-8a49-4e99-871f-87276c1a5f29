<script setup lang="ts">
import { ref } from "vue";

// 声明 props 类型
export interface RoleFormProps {
  formData?: {
    id?: string;
    name: string;
    code: string;
    order: number;
    stable?: string;
    desc?: string;
    remark?: string;
  };
}

// 声明 props 默认值
const props = withDefaults(defineProps<RoleFormProps>(), {
  formData: () => ({
    name: "",
    code: "",
    order: 1,
    stable: "yes",
    desc: "",
    remark: ""
  })
});

// vue 规定所有的 prop 都遵循着单向绑定原则，直接修改 prop 时，Vue 会抛出警告。
// 此处的写法仅仅是为了消除警告。因为对一个 reactive 对象执行 ref，
// 返回 Ref 对象的 value 值仍为传入的 reactive 对象
const newFormData = ref(props.formData);

// 选项数据
const yesNoOptions = [
  { label: "是", value: "yes" },
  { label: "否", value: "no" }
];
</script>

<template>
  <el-form :model="newFormData" label-width="120px" label-position="top">
    <div class="grid grid-cols-2 gap-4">
      <el-form-item label="角色名称" prop="name">
        <el-input
          v-model="newFormData.name"
          placeholder="请输入角色名称"
          clearable
        />
      </el-form-item>

      <el-form-item label="角色编码" prop="code">
        <el-input
          v-model="newFormData.code"
          placeholder="请输入角色编码"
          clearable
        />
      </el-form-item>
    </div>

    <div class="grid grid-cols-2 gap-4">
      <el-form-item label="排序" prop="order">
        <el-input-number
          v-model="newFormData.order"
          :min="1"
          :max="999"
          placeholder="请输入排序"
        />
      </el-form-item>

      <el-form-item label="是否稳定">
        <el-radio-group v-model="newFormData.stable">
          <el-radio
            v-for="option in yesNoOptions"
            :key="option.value"
            :value="option.value"
          >
            {{ option.label }}
          </el-radio>
        </el-radio-group>
      </el-form-item>
    </div>

    <el-form-item label="角色描述" prop="desc">
      <el-input
        v-model="newFormData.desc"
        placeholder="请输入角色描述"
        clearable
      />
    </el-form-item>

    <el-form-item label="备注" prop="remark">
      <el-input
        v-model="newFormData.remark"
        type="textarea"
        :rows="4"
        placeholder="请输入备注信息"
        maxlength="200"
        show-word-limit
      />
    </el-form-item>
  </el-form>
</template>
