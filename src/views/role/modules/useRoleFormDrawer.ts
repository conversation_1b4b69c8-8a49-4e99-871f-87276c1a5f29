import type { RoleInfo } from "@/api/role";
import { createRole, updateRole } from "@/api/role";
import { addDrawer } from "@/components/ReDrawer";
import { message } from "@/utils/message";
import { cloneDeep } from "@pureadmin/utils";
import RoleForm, { type RoleFormProps } from "./RoleForm.vue";

export interface UseRoleFormDrawerOptions {
  onSuccess?: () => void;
}

/**
 * 打开角色表单抽屉
 * @param roleData 角色数据（编辑时传入）
 * @param options 配置选项
 */
export function openRoleFormDrawer(
  roleData?: Partial<RoleInfo>,
  options: UseRoleFormDrawerOptions = {}
) {
  const { onSuccess } = options;
  const isEdit = !!roleData?.id;

  // 准备表单数据
  const formData = {
    id: roleData?.id || "",
    name: roleData?.name || "",
    code: roleData?.code || "",
    order: roleData?.order || 1,
    stable: roleData?.stable || "yes",
    desc: roleData?.desc || "",
    remark: roleData?.remark || ""
  };

  addDrawer({
    title: isEdit ? "编辑角色" : "新增角色",
    size: "500px",
    closeOnClickModal: false,
    contentRenderer: () => RoleForm,
    props: {
      formData: cloneDeep(formData)
    },
    beforeSure: async (done, { options }) => {
      const { formData: currentFormData } = options.props as RoleFormProps;

      try {
        const submitData = { ...currentFormData };

        if (isEdit) {
          // 编辑模式下确保 id 存在
          if (!submitData.id) {
            message("缺少角色ID，无法更新", { type: "error" });
            return;
          }

          // 类型断言：编辑模式下 id 必定存在
          const updateData = submitData as typeof submitData & { id: string };
          const response = await updateRole(updateData);
          if (response.code === 200) {
            message("角色更新成功", { type: "success" });
          } else {
            message(response.msg || "角色更新失败", { type: "error" });
            return; // 不关闭抽屉
          }
        } else {
          const response = await createRole(submitData);
          if (response.code === 200) {
            message("角色创建成功", { type: "success" });
          } else {
            message(response.msg || "角色创建失败", { type: "error" });
            return; // 不关闭抽屉
          }
        }

        onSuccess?.();
        done(); // 关闭抽屉
      } catch (error) {
        console.error("提交失败:", error);
        message("操作失败，请重试", { type: "error" });
        // 不调用 done()，保持抽屉打开
      }
    }
  });
}

/**
 * 打开新增角色抽屉
 */
export function openAddRoleDrawer(options: UseRoleFormDrawerOptions = {}) {
  return openRoleFormDrawer(undefined, options);
}

/**
 * 打开编辑角色抽屉
 */
export function openEditRoleDrawer(
  roleData: RoleInfo,
  options: UseRoleFormDrawerOptions = {}
) {
  return openRoleFormDrawer(roleData, options);
}
