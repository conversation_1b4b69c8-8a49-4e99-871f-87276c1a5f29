<script setup lang="ts">
import { ref } from "vue";
import type { RoleInfo } from "@/api/role";

// 声明 props 类型
export interface RolePermissionProps {
  roleData: RoleInfo;
  permissionType: "menu" | "button";
}

// 声明 props 默认值
const props = withDefaults(defineProps<RolePermissionProps>(), {
  permissionType: "menu"
});

// 菜单权限数据
const menuPermissions = ref([
  {
    id: "1",
    title: "用户管理",
    children: [
      { id: "1-1", title: "用户列表" },
      { id: "1-2", title: "用户详情" }
    ]
  },
  {
    id: "2",
    title: "合约管理",
    children: [
      { id: "2-1", title: "合约列表" },
      { id: "2-2", title: "合约详情" },
      { id: "2-3", title: "新增合约" }
    ]
  },
  {
    id: "3",
    title: "系统管理",
    children: [
      { id: "3-1", title: "角色管理" },
      { id: "3-2", title: "权限管理" },
      { id: "3-3", title: "菜单管理" }
    ]
  }
]);

// 按钮权限数据
const buttonPermissions = ref([
  { id: "user:create", name: "新增用户" },
  { id: "user:edit", name: "编辑用户" },
  { id: "user:delete", name: "删除用户" },
  { id: "contract:create", name: "新增合约" },
  { id: "contract:edit", name: "编辑合约" },
  { id: "contract:delete", name: "删除合约" }
]);

// 选中的权限
const checkedMenus = ref<string[]>([]);
const checkedButtons = ref<string[]>([]);

// 树形组件引用
const menuTreeRef = ref();

// 获取选中的权限数据
const getSelectedPermissions = () => {
  if (props.permissionType === "menu") {
    return menuTreeRef.value?.getCheckedKeys() || [];
  } else {
    return checkedButtons.value;
  }
};

// 设置选中的权限数据
const setSelectedPermissions = (permissions: string[]) => {
  if (props.permissionType === "menu") {
    checkedMenus.value = permissions;
  } else {
    checkedButtons.value = permissions;
  }
};

// 暴露方法给父组件
defineExpose({
  getSelectedPermissions,
  setSelectedPermissions
});
</script>

<template>
  <div class="role-permission-drawer">
    <div class="mb-4">
      <span class="font-medium">
        为角色 "{{ roleData.name }}" 配置{{ permissionType === "menu" ? "菜单" : "按钮" }}权限
      </span>
    </div>
    
    <!-- 菜单权限配置 -->
    <div v-if="permissionType === 'menu'">
      <el-tree
        ref="menuTreeRef"
        :data="menuPermissions"
        :props="{ children: 'children', label: 'title' }"
        show-checkbox
        node-key="id"
        :default-checked-keys="checkedMenus"
        class="permission-tree"
      />
    </div>
    
    <!-- 按钮权限配置 -->
    <div v-else>
      <el-checkbox-group v-model="checkedButtons">
        <el-row :gutter="20">
          <el-col :span="8" v-for="permission in buttonPermissions" :key="permission.id">
            <el-checkbox :label="permission.id" class="mb-2">
              {{ permission.name }}
            </el-checkbox>
          </el-col>
        </el-row>
      </el-checkbox-group>
    </div>
  </div>
</template>

<style scoped>
.role-permission-drawer {
  padding: 20px;
}

.permission-tree {
  max-height: 400px;
  overflow-y: auto;
}

.mb-2 {
  margin-bottom: 8px;
}

.mb-4 {
  margin-bottom: 16px;
}

.font-medium {
  font-weight: 500;
}
</style>
