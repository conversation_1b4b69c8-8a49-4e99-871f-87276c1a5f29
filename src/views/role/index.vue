<template>
  <div class="main">
    <!-- 搜索表单 -->
    <el-card class="box-card" shadow="never">
      <template #header>
        <div class="card-header">
          <span>角色管理</span>
        </div>
      </template>

      <el-form :inline="true" :model="searchForm" class="demo-form-inline">
        <el-form-item label="角色名称">
          <el-input
            v-model="searchForm.name"
            placeholder="请输入角色名称"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="角色编码">
          <el-input
            v-model="searchForm.code"
            placeholder="请输入角色编码"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button @click="resetSearch">重置</el-button>
          <el-button type="primary" @click="handleAdd">
            <template #icon>
              <IconifyIconOnline icon="ep:plus" />
            </template>
            新增角色
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 表格 -->
    <el-card class="box-card" shadow="never" style="margin-top: 16px">
      <pure-table
        ref="tableRef"
        row-key="id"
        align-whole="center"
        showOverflowTooltip
        :loading="loading"
        :data="dataList"
        :columns="columns"
        :pagination="pagination"
        @page-size-change="onSizeChange"
        @page-current-change="onCurrentChange"
      >
        <template #operation="{ row }">
          <el-button-group>
            <el-button
              link
              type="primary"
              size="small"
              @click="handleEdit(row)"
            >
              编辑
            </el-button>
            <el-button
              link
              type="warning"
              size="small"
              @click="handleMenuPermission(row)"
            >
              菜单权限
            </el-button>
            <el-button
              link
              type="info"
              size="small"
              @click="handleButtonPermission(row)"
            >
              按钮权限
            </el-button>
            <el-button
              link
              type="success"
              size="small"
              @click="handleBindUser(row)"
            >
              绑定用户
            </el-button>
            <el-button
              link
              type="danger"
              size="small"
              @click="handleDelete(row)"
            >
              删除
            </el-button>
          </el-button-group>
        </template>
      </pure-table>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { type RoleInfo, deleteRole, getRoleList } from "@/api/role";
import { IconifyIconOnline } from "@/components/ReIcon";
import { message } from "@/utils/message";
import type { PaginationProps } from "@pureadmin/table";
import { onMounted, reactive, ref } from "vue";
import { openRoleFormDrawer } from "./modules/useRoleFormDrawer";
import {
  openButtonPermissionDrawer,
  openMenuPermissionDrawer
} from "./modules/useRolePermissionDrawer";
import { openRoleUserBindDrawer } from "./modules/useRoleUserBindDrawer";

defineOptions({
  name: "RoleList"
});

const loading = ref(false);
const tableRef = ref();

const searchForm = ref({
  name: "",
  code: ""
});

const dataList = ref<RoleInfo[]>([]);

/** 分页配置 */
const pagination = reactive<PaginationProps>({
  pageSize: 10,
  currentPage: 1,
  layout: "prev, pager, next, jumper, ->, total, sizes",
  total: 0,
  align: "center",
  background: true,
  pageSizes: [10, 20, 50, 100]
});

/** 表格列配置 */
const columns: TableColumnList = [
  {
    label: "排序",
    prop: "order",
    width: 80
  },
  {
    label: "角色编码",
    prop: "code",
    width: 120
  },
  {
    label: "角色名称",
    prop: "name",
    minWidth: 120
  },
  {
    label: "状态",
    prop: "stable",
    width: 100
  },
  {
    label: "描述",
    prop: "desc",
    minWidth: 200,
    showOverflowTooltip: true
  },
  {
    label: "操作",
    fixed: "right",
    width: 280,
    slot: "operation"
  }
];

/** 获取角色列表数据 */
const getRoleListData = async () => {
  loading.value = true;
  try {
    const params = {
      options: {
        order_by: "order",
        desc: false
      },
      page: {
        page: pagination.currentPage,
        limit: pagination.pageSize
      },
      params: []
    };

    // 添加搜索条件
    if (searchForm.value.name) {
      params.params.push({
        var: "name",
        val: searchForm.value.name
      });
    }
    if (searchForm.value.code) {
      params.params.push({
        var: "code",
        val: searchForm.value.code
      });
    }

    const response = await getRoleList(params);
    if (response.code === 200) {
      dataList.value = response.data.data;
      pagination.total = response.data.total;
    } else {
      message(response.msg || "获取角色列表失败", { type: "error" });
    }
  } catch (error) {
    console.error("获取角色列表失败:", error);
    message("获取角色列表失败", { type: "error" });
  } finally {
    loading.value = false;
  }
};

/** 搜索 */
const handleSearch = () => {
  pagination.currentPage = 1;
  getRoleListData();
};

/** 重置搜索 */
const resetSearch = () => {
  searchForm.value = {
    name: "",
    code: ""
  };
  pagination.currentPage = 1;
  getRoleListData();
};

/** 新增角色 */
const handleAdd = () => {
  openRoleFormDrawer(undefined, {
    onSuccess: () => {
      getRoleListData();
    }
  });
};

/** 编辑角色 */
const handleEdit = (row: RoleInfo) => {
  openRoleFormDrawer(row, {
    onSuccess: () => {
      getRoleListData();
    }
  });
};

/** 删除角色 */
const handleDelete = async (row: RoleInfo) => {
  try {
    const response = await deleteRole(row.id);
    if (response.code === 200) {
      message("删除成功", { type: "success" });
      getRoleListData();
    } else {
      message(response.msg || "删除失败", { type: "error" });
    }
  } catch (error) {
    console.error("删除失败:", error);
    message("删除失败", { type: "error" });
  }
};

/** 菜单权限配置 */
const handleMenuPermission = (row: RoleInfo) => {
  openMenuPermissionDrawer(row, {
    onSuccess: () => {
      message("菜单权限配置成功", { type: "success" });
    }
  });
};

/** 按钮权限配置 */
const handleButtonPermission = (row: RoleInfo) => {
  openButtonPermissionDrawer(row, {
    onSuccess: () => {
      message("按钮权限配置成功", { type: "success" });
    }
  });
};

/** 绑定用户管理 */
const handleBindUser = (row: RoleInfo) => {
  openRoleUserBindDrawer(row, {
    onSuccess: () => {
      // 可以在这里刷新数据或执行其他操作
      console.log("角色用户绑定drawer已关闭");
    }
  });
};

/** 分页大小改变 */
const onSizeChange = (size: number) => {
  pagination.pageSize = size;
  getRoleListData();
};

/** 当前页改变 */
const onCurrentChange = (page: number) => {
  pagination.currentPage = page;
  getRoleListData();
};

onMounted(() => {
  getRoleListData();
});
</script>

<style scoped>
.main {
  margin: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>
