<template>
  <div class="main">
    <!-- 搜索表单 -->
    <el-form
      ref="formRef"
      :inline="true"
      :model="searchForm"
      class="search-form bg-bg_color w-full pl-8 pt-[12px] overflow-auto"
    >
      <el-form-item label="平台订单编号：" prop="platform_order_serial">
        <el-input
          v-model="searchForm.platform_order_serial"
          placeholder="请输入平台订单编号"
          clearable
          class="w-[180px]!"
          @keyup.enter="onSearch"
        />
      </el-form-item>
      <el-form-item label="订单编号：" prop="serial">
        <el-input
          v-model="searchForm.serial"
          placeholder="请输入订单编号"
          clearable
          class="w-[180px]!"
          @keyup.enter="onSearch"
        />
      </el-form-item>
      <el-form-item label="销售平台：" prop="platform_name">
        <el-input
          v-model="searchForm.platform_name"
          placeholder="请输入销售平台"
          clearable
          class="w-[180px]!"
          @keyup.enter="onSearch"
        />
      </el-form-item>
      <el-form-item label="客户姓名：" prop="customer">
        <el-input
          v-model="searchForm.customer"
          placeholder="请输入客户姓名"
          clearable
          class="w-[180px]!"
          @keyup.enter="onSearch"
        />
      </el-form-item>
      <el-form-item label="订单状态：" prop="status">
        <el-select
          v-model="searchForm.status"
          placeholder="请选择状态"
          clearable
          class="w-[180px]!"
        >
          <el-option
            v-for="item in orderStatusDict"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          :icon="useRenderIcon('ep:search')"
          :loading="loading"
          @click="onSearch"
        >
          搜索
        </el-button>
        <el-button
          :icon="useRenderIcon('ep:refresh')"
          @click="resetForm(formRef)"
        >
          重置
        </el-button>
      </el-form-item>
    </el-form>

    <!-- 表格 -->
    <el-card class="box-card" shadow="never" style="margin-top: 16px">
      <template #header>
        <div class="flex justify-between items-center">
          <span class="font-medium">销售订单列表</span>
        </div>
      </template>

      <pure-table
        ref="tableRef"
        row-key="id"
        align-whole="center"
        showOverflowTooltip
        :loading="loading"
        :data="dataList"
        :columns="columns"
        :pagination="pagination"
        @page-size-change="onSizeChange"
        @page-current-change="onCurrentChange"
      >
        <template #status="{ row }">
          <el-tag :color="getOrderStatusColor(row.status)" effect="dark" round>
            {{ getOrderStatusText(row.status) }}
          </el-tag>
        </template>
        <template #amount="{ row }">
          <span>¥{{ formatAmount(row.amount) }}</span>
        </template>
        <template #total_payment="{ row }">
          <span>¥{{ formatAmount(row.total_payment) }}</span>
        </template>
        <template #purchase_time="{ row }">
          <span>{{ formatDateTime(row.purchase_time) }}</span>
        </template>
        <template #pay_time="{ row }">
          <span>{{ formatDateTime(row.pay_time) }}</span>
        </template>
        <template #delivery_time="{ row }">
          <span>{{ formatDateTime(row.delivery_time) }}</span>
        </template>
        <template #sign_time="{ row }">
          <span>{{ formatDateTime(row.sign_time) }}</span>
        </template>
        <template #complete_time="{ row }">
          <span>{{ formatDateTime(row.complete_time) }}</span>
        </template>
        <template #operation="{ row }">
          <el-button link type="primary" size="small" @click="handleView(row)">
            查看
          </el-button>
        </template>
      </pure-table>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import {
  deleteSalesOrder,
  getOrderStatusColor,
  getOrderStatusText,
  getSalesOrderList,
  orderStatusDict,
  type SalesOrderInfo,
  type SalesOrderListParams
} from "@/api/salesOrder";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import { formatDateTime } from "@/utils/formatTime";
import { PureTable } from "@pureadmin/table";
import "@pureadmin/table/dist/style.css";
import { ElMessage, type FormInstance } from "element-plus";
import { computed, onMounted, reactive, ref } from "vue";
import { useRouter } from "vue-router";

defineOptions({
  name: "OrderList"
});

const router = useRouter();

// 响应式数据
const loading = ref(false);
const dataList = ref<SalesOrderInfo[]>([]);
const tableRef = ref();
const formRef = ref<FormInstance>();

// 搜索表单
const searchForm = reactive({
  platform_order_serial: "",
  serial: "",
  platform_name: "",
  customer: "",
  status: ""
});

// 分页配置
const pagination = reactive({
  total: 0,
  pageSize: 10,
  currentPage: 1,
  background: true,
  pageSizes: [10, 20, 50, 100]
});

// 表格列配置
const columns = computed(() => [
  {
    label: "平台订单编号",
    prop: "platform_order_serial",
    minWidth: 150
  },
  {
    label: "销售平台",
    prop: "platform_name",
    minWidth: 120
  },
  {
    label: "订单编号",
    prop: "serial",
    minWidth: 150
  },
  {
    label: "客户姓名",
    prop: "customer",
    minWidth: 120
  },
  {
    label: "订单金额",
    prop: "amount",
    minWidth: 120,
    slot: "amount"
  },
  {
    label: "总支付金额",
    prop: "total_payment",
    minWidth: 120,
    slot: "total_payment"
  },
  {
    label: "下单时间",
    prop: "purchase_time",
    minWidth: 150,
    slot: "purchase_time"
  },
  {
    label: "支付时间",
    prop: "pay_time",
    minWidth: 150,
    slot: "pay_time"
  },
  {
    label: "发货时间",
    prop: "delivery_time",
    minWidth: 150,
    slot: "delivery_time"
  },
  {
    label: "签收时间",
    prop: "sign_time",
    minWidth: 150,
    slot: "sign_time"
  },
  {
    label: "完成时间",
    prop: "complete_time",
    minWidth: 150,
    slot: "complete_time"
  },
  {
    label: "订单状态",
    prop: "status",
    minWidth: 100,
    slot: "status"
  },
  {
    label: "操作",
    fixed: "right",
    width: 80,
    slot: "operation"
  }
]);

// 格式化金额
const formatAmount = (amount: number | string) => {
  if (!amount) return "0.00";
  return Number(amount).toLocaleString("zh-CN", {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  });
};

// 获取销售订单列表
const getSalesOrderListData = async () => {
  loading.value = true;
  try {
    const params: SalesOrderListParams = {
      options: {
        order_by: "created_at",
        desc: true
      },
      page: {
        page: pagination.currentPage,
        limit: pagination.pageSize
      }
    };

    // 添加搜索参数
    if (
      searchForm.platform_order_serial ||
      searchForm.serial ||
      searchForm.platform_name ||
      searchForm.customer ||
      searchForm.status
    ) {
      params.params = [];
      if (searchForm.platform_order_serial) {
        params.params.push({
          var: "platform_order_serial",
          val: searchForm.platform_order_serial
        });
      }
      if (searchForm.serial) {
        params.params.push({
          var: "serial",
          val: searchForm.serial
        });
      }
      if (searchForm.platform_name) {
        params.params.push({
          var: "platform_name",
          val: searchForm.platform_name
        });
      }
      if (searchForm.customer) {
        params.params.push({
          var: "customer",
          val: searchForm.customer
        });
      }
      if (searchForm.status) {
        params.params.push({
          var: "status",
          val: searchForm.status
        });
      }
    }

    const response = await getSalesOrderList(params);
    if (response.code === 200) {
      dataList.value = response.data.data;
      pagination.total = response.data.total;
    } else {
      ElMessage.error(response.msg || "获取销售订单列表失败");
    }
  } catch (error) {
    console.error("获取销售订单列表失败:", error);
    ElMessage.error("获取销售订单列表失败");
  } finally {
    loading.value = false;
  }
};

// 搜索
const onSearch = () => {
  pagination.currentPage = 1;
  getSalesOrderListData();
};

// 重置表单
const resetForm = (formEl: FormInstance | undefined) => {
  if (!formEl) return;
  formEl.resetFields();
  onSearch();
};

// 分页相关
const onSizeChange = (size: number) => {
  pagination.pageSize = size;
  getSalesOrderListData();
};

const onCurrentChange = (current: number) => {
  pagination.currentPage = current;
  getSalesOrderListData();
};

// 操作方法
const handleView = (row: SalesOrderInfo) => {
  console.log(row);
  router.push({
    path: "/order/detail",
    query: { id: row.id }
  });
};

// 初始化
onMounted(() => {
  getSalesOrderListData();
});
</script>

<style lang="scss" scoped>
.main {
  margin: 20px;
}

.search-form {
  :deep(.el-form-item) {
    margin-bottom: 12px;
  }
}
</style>
