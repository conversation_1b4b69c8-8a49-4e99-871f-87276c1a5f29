<template>
  <div class="main">
    <div v-if="itemDetail?.id" class="order-detail">
      <!-- 订单标题 -->
      <div class="order-header mb-6">
        <div class="flex justify-between items-center">
          <div class="flex items-center">
            <h2 class="text-2xl font-bold mr-4">
              订单详情 - {{ itemDetail.serial }}
            </h2>
            <el-tag
              :color="getOrderStatusColor(itemDetail.status)"
              effect="dark"
              round
              size="large"
            >
              {{ getOrderStatusText(itemDetail.status) }}
            </el-tag>
          </div>
          <el-button :icon="useRenderIcon('ep:back')" @click="goBack">
            返回列表
          </el-button>
        </div>
      </div>

      <!-- 基本信息 -->
      <el-card class="mb-6" shadow="never">
        <template #header>
          <h3 class="card-title">基本信息</h3>
        </template>
        <el-descriptions :column="4" border>
          <el-descriptions-item label="订单编号">
            {{ itemDetail.serial }}
          </el-descriptions-item>
          <el-descriptions-item label="平台名称">
            {{ itemDetail.platform_name || "-" }}
          </el-descriptions-item>
          <el-descriptions-item label="平台订单号">
            {{ itemDetail.platform_order_serial || "-" }}
          </el-descriptions-item>
          <el-descriptions-item label="平台编号">
            {{ itemDetail.platform_serial || "-" }}
          </el-descriptions-item>
          <el-descriptions-item label="导入批次">
            {{ itemDetail.import_record || "-" }}
          </el-descriptions-item>
          <el-descriptions-item label="合同ID">
            {{ itemDetail.contract_id || "-" }}
          </el-descriptions-item>
          <el-descriptions-item label="还款ID">
            {{ itemDetail.repayment_id || "-" }}
          </el-descriptions-item>
          <el-descriptions-item label="创建时间">
            {{ formatDateTime(itemDetail.created_at) }}
          </el-descriptions-item>
        </el-descriptions>
      </el-card>

      <!-- 客户信息 -->
      <div class="flex gap-6 mb-6">
        <el-card class="flex-1" shadow="never">
          <template #header>
            <h3 class="card-title">客户信息</h3>
          </template>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="客户姓名">
              {{ itemDetail.customer || "-" }}
            </el-descriptions-item>
            <el-descriptions-item label="客户电话">
              {{ itemDetail.customer_phone || "-" }}
            </el-descriptions-item>
            <el-descriptions-item label="收货电话">
              {{ itemDetail.receive_phone || "-" }}
            </el-descriptions-item>
            <el-descriptions-item label="收货地址">
              {{ itemDetail.address || "-" }}
            </el-descriptions-item>
          </el-descriptions>
        </el-card>

        <!-- 金额信息 -->
        <el-card class="flex-1" shadow="never">
          <template #header>
            <h3 class="card-title">金额信息</h3>
          </template>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="订单金额">
              ¥{{ formatAmount(itemDetail.amount) }}
            </el-descriptions-item>
            <el-descriptions-item label="快递费用">
              ¥{{ formatAmount(itemDetail.express_fee) }}
            </el-descriptions-item>
            <el-descriptions-item label="平台费用">
              ¥{{ formatAmount(itemDetail.platform_fee_total) }}
            </el-descriptions-item>
            <el-descriptions-item
              label="总支付金额"
              class="font-bold text-primary"
            >
              ¥{{ formatAmount(itemDetail.total_payment) }}
            </el-descriptions-item>
          </el-descriptions>
        </el-card>
      </div>

      <!-- 支付和物流信息 -->
      <div class="flex gap-6 mb-6">
        <el-card class="flex-1" shadow="never">
          <template #header>
            <h3 class="card-title">支付信息</h3>
          </template>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="支付方式">
              {{ itemDetail.pay_type || "-" }}
            </el-descriptions-item>
            <el-descriptions-item label="支付时间">
              {{ formatDateTime(itemDetail.pay_time) }}
            </el-descriptions-item>
            <el-descriptions-item label="支付信息" :span="2">
              {{ itemDetail.pay_info || "-" }}
            </el-descriptions-item>
          </el-descriptions>
        </el-card>

        <!-- 物流信息 -->
        <el-card class="flex-1" shadow="never">
          <template #header>
            <h3 class="card-title">物流信息</h3>
          </template>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="快递类型">
              {{ itemDetail.express_type || "-" }}
            </el-descriptions-item>
            <el-descriptions-item label="快递公司">
              {{ itemDetail.express_company || "-" }}
            </el-descriptions-item>
            <el-descriptions-item label="快递单号">
              {{ itemDetail.express_order || "-" }}
            </el-descriptions-item>
            <el-descriptions-item label="发货时间">
              {{ formatDateTime(itemDetail.delivery_time) }}
            </el-descriptions-item>
            <el-descriptions-item label="签收时间">
              {{ formatDateTime(itemDetail.sign_time) }}
            </el-descriptions-item>
            <el-descriptions-item label="完成时间">
              {{ formatDateTime(itemDetail.complete_time) }}
            </el-descriptions-item>
          </el-descriptions>
        </el-card>
      </div>

      <!-- 订单商品明细 -->
      <el-card shadow="never">
        <template #header>
          <h3 class="card-title">订单商品明细</h3>
        </template>
        <el-table
          :data="infoList"
          style="width: 100%"
          border
          stripe
          :loading="loading"
          max-height="500"
        >
          <el-table-column
            prop="product_serial"
            label="商品编号"
            min-width="120"
          />
          <el-table-column
            prop="product_name"
            label="商品名称"
            min-width="150"
          />
          <el-table-column
            prop="product_model"
            label="商品型号"
            min-width="120"
          />
          <el-table-column
            prop="product_type"
            label="商品类型"
            min-width="100"
          />
          <el-table-column
            prop="quantity"
            label="数量"
            width="80"
            align="center"
          />
          <el-table-column
            prop="sales_price"
            label="销售单价"
            width="100"
            align="right"
          >
            <template #default="{ row }">
              ¥{{ formatAmount(row.sales_price) }}
            </template>
          </el-table-column>
          <el-table-column
            prop="cost_price"
            label="成本单价"
            width="100"
            align="right"
          >
            <template #default="{ row }">
              ¥{{ formatAmount(row.cost_price) }}
            </template>
          </el-table-column>
          <el-table-column
            prop="total_sales_price"
            label="销售总价"
            width="120"
            align="right"
          >
            <template #default="{ row }">
              ¥{{ formatAmount(row.total_sales_price) }}
            </template>
          </el-table-column>
          <el-table-column
            prop="total_cost_price"
            label="成本总价"
            width="120"
            align="right"
          >
            <template #default="{ row }">
              ¥{{ formatAmount(row.total_cost_price) }}
            </template>
          </el-table-column>
          <el-table-column
            prop="platform_fee"
            label="平台费用"
            width="100"
            align="right"
          >
            <template #default="{ row }">
              ¥{{ formatAmount(row.platform_fee) }}
            </template>
          </el-table-column>
          <el-table-column
            prop="discount"
            label="折扣"
            width="80"
            align="right"
          >
            <template #default="{ row }">
              ¥{{ formatAmount(row.discount) }}
            </template>
          </el-table-column>
          <el-table-column
            prop="express_company"
            label="快递公司"
            min-width="100"
          />
          <el-table-column
            prop="express_order"
            label="快递单号"
            min-width="120"
          />
          <el-table-column
            prop="system_remark"
            label="系统备注"
            min-width="150"
          />
        </el-table>
      </el-card>
    </div>
    <el-empty v-else description="订单信息不存在" />
  </div>
</template>

<script setup lang="ts">
import {
  getOrderStatusColor,
  getOrderStatusText,
  getSalesOrder,
  type SalesOrderInfo
} from "@/api/salesOrder";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import { formatDateTime } from "@/utils/formatTime";
import { ElMessage } from "element-plus";
import { onMounted, ref } from "vue";
import { useRoute, useRouter } from "vue-router";

defineOptions({
  name: "OrderDetail"
});

const router = useRouter();
const route = useRoute();

const itemDetail = ref<SalesOrderInfo>({} as SalesOrderInfo);
const infoList = ref([]);
const loading = ref(false);

// 格式化金额
const formatAmount = (amount: number | string) => {
  if (!amount) return "0.00";
  return Number(amount).toLocaleString("zh-CN", {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  });
};

// 返回列表
const goBack = () => {
  router.push({ path: "/order/list" });
};

// 获取销售订单详情
const handleDetail = async () => {
  try {
    loading.value = true;
    const response = await getSalesOrder(route.query.id as string);
    if (response.code === 200) {
      itemDetail.value = response.data;
      ElMessage.success("订单信息加载成功");
      // 获取订单商品明细
      await getInfoList();
    } else {
      ElMessage.warning(response.msg || "订单信息查询失败，请重试");
    }
  } catch (error) {
    console.error("获取订单详情失败:", error);
    ElMessage.error("订单信息加载失败");
  } finally {
    loading.value = false;
  }
};

// 获取销售订单商品明细
const getInfoList = async () => {
  try {
    // 这里需要实现获取订单商品明细的API
    // 暂时使用模拟数据
    infoList.value = [
      {
        id: "1",
        product_serial: "P001",
        product_name: "笔记本电脑",
        product_model: "ThinkPad X1",
        product_type: "电子产品",
        quantity: 1,
        sales_price: 8999,
        cost_price: 7500,
        total_sales_price: 8999,
        total_cost_price: 7500,
        platform_fee: 200,
        discount: 0,
        express_company: "顺丰速运",
        express_order: "SF1234567890",
        system_remark: ""
      },
      {
        id: "2",
        product_serial: "P002",
        product_name: "无线鼠标",
        product_model: "罗技 MX Master",
        product_type: "电子配件",
        quantity: 1,
        sales_price: 599,
        cost_price: 450,
        total_sales_price: 599,
        total_cost_price: 450,
        platform_fee: 20,
        discount: 0,
        express_company: "顺丰速运",
        express_order: "SF1234567890",
        system_remark: ""
      }
    ];

    // 实际实现应该是调用API
    /*
    const params = {
      params: [{ var: "order_serial", val: itemDetail.value.platform_order_serial }],
      page: {
        page: 1,
        limit: 0,
      },
    };
    const response = await getSalesOrderInfoList(params);
    if (response.code === 200) {
      infoList.value = response.data.data || [];
    } else {
      infoList.value = [];
      ElMessage.warning("商品明细查询失败");
    }
    */
  } catch (error) {
    console.error("获取商品明细失败:", error);
    infoList.value = [];
  }
};

// 初始化
onMounted(async () => {
  console.log(route.query.id);
  if (route.query.id) {
    handleDetail();
  } else {
    ElMessage.error("缺少订单ID参数");
    // goBack();
  }
});
</script>

<style lang="scss" scoped>
.main {
  margin: 20px;
}

.order-detail {
  .card-title {
    font-size: 16px;
    font-weight: 600;
    margin: 0;
  }

  .mb-6 {
    margin-bottom: 24px;
  }

  .text-primary {
    color: var(--el-color-primary);
  }

  .font-bold {
    font-weight: bold;
  }
}
</style>

<style lang="scss" scoped>
.main {
  margin: 20px;
}

.test-content {
  .mb-4 {
    margin-bottom: 16px;
  }

  .mt-6 {
    margin-top: 24px;
  }
}
</style>
