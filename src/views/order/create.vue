<template>
  <div class="main">
    <el-card shadow="never">
      <template #header>
        <div class="flex justify-between items-center">
          <h2 class="text-xl font-bold">创建订单</h2>
          <el-button :icon="useRenderIcon('ep:back')" @click="goBack">
            返回列表
          </el-button>
        </div>
      </template>
      
      <div class="create-content">
        <el-alert
          title="订单创建功能"
          type="info"
          :closable="false"
          show-icon
          class="mb-4"
        >
          <template #default>
            <p>这里是订单创建页面，您可以在这里实现订单创建的业务逻辑。</p>
          </template>
        </el-alert>

        <el-form :model="form" label-width="120px">
          <el-form-item label="订单编号">
            <el-input v-model="form.serial" placeholder="请输入订单编号" />
          </el-form-item>
          <el-form-item label="客户姓名">
            <el-input v-model="form.customer" placeholder="请输入客户姓名" />
          </el-form-item>
          <el-form-item label="订单金额">
            <el-input v-model="form.amount" placeholder="请输入订单金额" />
          </el-form-item>
          <el-form-item>
            <el-button type="primary">创建订单</el-button>
            <el-button @click="goBack">取消</el-button>
          </el-form-item>
        </el-form>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import { reactive } from "vue";
import { useRouter } from "vue-router";

defineOptions({
  name: "OrderCreate"
});

const router = useRouter();

const form = reactive({
  serial: "",
  customer: "",
  amount: ""
});

// 返回列表
const goBack = () => {
  router.push({ path: "/order/list" });
};
</script>

<style lang="scss" scoped>
.main {
  margin: 20px;
}

.create-content {
  .mb-4 {
    margin-bottom: 16px;
  }
}
</style>
