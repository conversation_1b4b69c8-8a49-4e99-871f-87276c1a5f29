<template>
  <div class="main">
    <el-card shadow="never">
      <template #header>
        <div class="flex justify-between items-center">
          <h2 class="text-xl font-bold">编辑产品SKU</h2>
          <el-button :icon="useRenderIcon('ep:back')" @click="goBack">
            返回列表
          </el-button>
        </div>
      </template>

      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="120px"
        label-position="left"
        class="max-w-4xl"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="产品编号" prop="serial">
              <el-input
                v-model="formData.serial"
                placeholder="请输入产品编号"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="产品名称" prop="name">
              <el-input
                v-model="formData.name"
                placeholder="请输入产品名称"
                clearable
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="产品系列编号" prop="product_serial">
              <el-input
                v-model="formData.product_serial"
                placeholder="请输入产品系列编号"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="外部编号" prop="external_serial">
              <el-input
                v-model="formData.external_serial"
                placeholder="请输入外部编号"
                clearable
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="产品类型" prop="product_type">
              <el-input
                v-model="formData.product_type"
                placeholder="请输入产品类型"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="品牌" prop="brand">
              <el-input
                v-model="formData.brand"
                placeholder="请输入品牌"
                clearable
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="销售价格" prop="sales_price">
              <el-input-number
                v-model="formData.sales_price"
                :min="0"
                :precision="2"
                placeholder="请输入销售价格"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="市场价格" prop="market_price">
              <el-input-number
                v-model="formData.market_price"
                :min="0"
                :precision="2"
                placeholder="请输入市场价格"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="成本价格" prop="cost_price">
              <el-input-number
                v-model="formData.cost_price"
                :min="0"
                :precision="2"
                placeholder="请输入成本价格"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="条形码" prop="barcode">
              <el-input
                v-model="formData.barcode"
                placeholder="请输入条形码"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="型号" prop="model">
              <el-input
                v-model="formData.model"
                placeholder="请输入型号"
                clearable
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="规格" prop="specification">
              <el-input
                v-model="formData.specification"
                placeholder="请输入规格"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="尺寸" prop="size">
              <el-input
                v-model="formData.size"
                placeholder="请输入尺寸"
                clearable
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="包装" prop="package">
              <el-input
                v-model="formData.package"
                placeholder="请输入包装信息"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="单位" prop="unit">
              <el-input
                v-model="formData.unit"
                placeholder="请输入单位"
                clearable
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="产地" prop="source_area">
              <el-input
                v-model="formData.source_area"
                placeholder="请输入产地"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="状态" prop="status">
              <el-select
                v-model="formData.status"
                placeholder="请选择状态"
                style="width: 100%"
              >
                <el-option label="上架" value="active" />
                <el-option label="下架" value="inactive" />
                <el-option label="草稿" value="draft" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="排序" prop="sort">
              <el-input-number
                v-model="formData.sort"
                :min="0"
                placeholder="请输入排序"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="供应商ID" prop="supplier_id">
              <el-input
                v-model="formData.supplier_id"
                placeholder="请输入供应商ID"
                clearable
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="产品描述" prop="desc">
          <el-input
            v-model="formData.desc"
            type="textarea"
            :rows="3"
            placeholder="请输入产品描述"
          />
        </el-form-item>

        <el-form-item label="产品内容" prop="content">
          <el-input
            v-model="formData.content"
            type="textarea"
            :rows="5"
            placeholder="请输入产品详细内容"
          />
        </el-form-item>

        <el-form-item>
          <el-button type="primary" :loading="loading" @click="handleSubmit">
            更新产品SKU
          </el-button>
          <el-button @click="handleReset">重置</el-button>
          <el-button @click="goBack">取消</el-button>
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import {
  getProductSku,
  updateProductSku,
  type ProductSkuUpdateRequest
} from "@/api/productSku";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import { ElMessage, type FormInstance, type FormRules } from "element-plus";
import { onMounted, reactive, ref } from "vue";
import { useRoute, useRouter } from "vue-router";

defineOptions({
  name: "ProductSkuEdit"
});

const router = useRouter();
const route = useRoute();
const formRef = ref<FormInstance>();
const loading = ref(false);

// 表单数据
const formData = reactive<ProductSkuUpdateRequest>({
  id: "",
  sort: 0,
  status: "draft",
  serial: "",
  product_serial: "",
  external_serial: "",
  product_type: "",
  sales_price: 0,
  market_price: 0,
  cost_price: 0,
  name: "",
  brand: "",
  barcode: "",
  model: "",
  specification: "",
  size: "",
  package: "",
  unit: "",
  source_area: "",
  desc: "",
  content: "",
  supplier_id: ""
});

// 表单验证规则
const formRules: FormRules = {
  serial: [{ required: true, message: "请输入产品编号", trigger: "blur" }],
  name: [{ required: true, message: "请输入产品名称", trigger: "blur" }],
  status: [{ required: true, message: "请选择状态", trigger: "change" }],
  sales_price: [{ required: true, message: "请输入销售价格", trigger: "blur" }],
  market_price: [{ required: true, message: "请输入市场价格", trigger: "blur" }],
  cost_price: [{ required: true, message: "请输入成本价格", trigger: "blur" }]
};

// 返回列表
const goBack = () => {
  router.push({ path: "/productSku/list" });
};

// 重置表单
const handleReset = () => {
  getProductSkuDetail();
};

// 获取产品SKU详情
const getProductSkuDetail = async () => {
  const id = route.query.id as string;
  if (!id) {
    ElMessage.error("缺少产品SKU ID参数");
    goBack();
    return;
  }

  try {
    loading.value = true;
    const response = await getProductSku(id);
    if (response.code === 200) {
      const data = response.data;
      Object.assign(formData, {
        id: data.id,
        sort: data.sort || 0,
        status: data.status || "draft",
        serial: data.serial || "",
        product_serial: data.product_serial || "",
        external_serial: data.external_serial || "",
        product_type: data.product_type || "",
        sales_price: data.sales_price || 0,
        market_price: data.market_price || 0,
        cost_price: data.cost_price || 0,
        name: data.name || "",
        brand: data.brand || "",
        barcode: data.barcode || "",
        model: data.model || "",
        specification: data.specification || "",
        size: data.size || "",
        package: data.package || "",
        unit: data.unit || "",
        source_area: data.source_area || "",
        desc: data.desc || "",
        content: data.content || "",
        supplier_id: data.supplier_id || ""
      });
    } else {
      ElMessage.error(response.msg || "获取产品SKU信息失败");
    }
  } catch (error) {
    console.error("获取产品SKU详情失败:", error);
    ElMessage.error("获取产品SKU信息失败");
  } finally {
    loading.value = false;
  }
};

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return;

  try {
    const valid = await formRef.value.validate();
    if (!valid) return;

    loading.value = true;
    const response = await updateProductSku(formData);
    if (response.code === 200) {
      ElMessage.success("更新成功");
      goBack();
    } else {
      ElMessage.error(response.msg || "更新失败");
    }
  } catch (error) {
    console.error("更新产品SKU失败:", error);
    ElMessage.error("更新失败");
  } finally {
    loading.value = false;
  }
};

// 初始化
onMounted(() => {
  getProductSkuDetail();
});
</script>

<style lang="scss" scoped>
.main {
  margin: 20px;
}

.max-w-4xl {
  max-width: 1024px;
}
</style>
