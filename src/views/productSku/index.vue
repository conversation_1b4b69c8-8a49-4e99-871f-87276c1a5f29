<template>
  <div class="main">
    <el-card shadow="never">
      <template #header>
        <div class="card-header">
          <span class="font-medium">产品SKU列表</span>
        </div>
      </template>

      <div class="mb-4">
        <el-button
          type="primary"
          :icon="useRenderIcon('ep:plus')"
          @click="handleCreate"
        >
          添加产品SKU
        </el-button>
      </div>

      <pure-table
        ref="tableRef"
        row-key="id"
        align-whole="center"
        showOverflowTooltip
        :loading="loading"
        :data="dataList"
        :columns="columns"
        :pagination="pagination"
        @page-size-change="onSizeChange"
        @page-current-change="onCurrentChange"
      >
        <template #status="{ row }">
          <el-tag :type="getStatusTagType(row.status)">
            {{ getStatusText(row.status) }}
          </el-tag>
        </template>
        <template #created_at="{ row }">
          <span>{{ formatDateTime(row.created_at) }}</span>
        </template>
        <template #operation="{ row }">
          <el-dropdown trigger="click">
            <el-button type="primary" size="small">
              操作<el-icon class="el-icon--right"><ArrowDown /></el-icon>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item :icon="View" @click="handleView(row)">
                  查看
                </el-dropdown-item>
                <el-dropdown-item :icon="Edit" @click="handleEdit(row)">
                  编辑
                </el-dropdown-item>
                <el-dropdown-item :icon="Delete" @click="confirmDelete(row)">
                  删除
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </template>
      </pure-table>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import {
  deleteProductSku,
  getProductSkuList,
  type ProductSkuInfo
} from "@/api/productSku";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import { formatDateTime } from "@/utils/formatTime";
import { ArrowDown, Delete, Edit, View } from "@element-plus/icons-vue";
import { PureTable } from "@pureadmin/table";
import { ElMessage, ElMessageBox } from "element-plus";
import { computed, onMounted, reactive, ref } from "vue";
import { useRouter } from "vue-router";

defineOptions({
  name: "ProductSkuList"
});

const router = useRouter();
const tableRef = ref();

// 响应式数据
const loading = ref(false);
const dataList = ref<ProductSkuInfo[]>([]);

// 分页配置
const pagination = reactive({
  total: 0,
  pageSize: 10,
  currentPage: 1,
  background: true,
  pageSizes: [10, 20, 50, 100]
});

// 表格列配置
const columns = computed(() => [
  {
    label: "产品编号",
    prop: "serial",
    minWidth: 150
  },
  {
    label: "产品名称",
    prop: "name",
    minWidth: 150
  },
  {
    label: "品牌",
    prop: "brand",
    minWidth: 120
  },
  {
    label: "规格",
    prop: "specification",
    minWidth: 150
  },
  {
    label: "销售价格",
    prop: "sales_price",
    minWidth: 120,
    formatter: (row: ProductSkuInfo) => {
      return `¥${row.sales_price.toFixed(2)}`;
    }
  },
  {
    label: "成本价格",
    prop: "cost_price",
    minWidth: 120,
    formatter: (row: ProductSkuInfo) => {
      return `¥${row.cost_price.toFixed(2)}`;
    }
  },
  {
    label: "状态",
    prop: "status",
    minWidth: 100,
    slot: "status"
  },
  {
    label: "创建时间",
    prop: "created_at",
    minWidth: 160,
    slot: "created_at"
  },
  {
    label: "操作",
    fixed: "right",
    width: 100,
    slot: "operation"
  }
]);

// 获取状态文本
const getStatusText = (status: string) => {
  switch (status) {
    case "active":
      return "上架";
    case "inactive":
      return "下架";
    case "draft":
      return "草稿";
    default:
      return status;
  }
};

// 获取状态标签类型
const getStatusTagType = (status: string) => {
  switch (status) {
    case "active":
      return "success";
    case "inactive":
      return "danger";
    case "draft":
      return "info";
    default:
      return "info";
  }
};

// 获取产品SKU列表数据
const getProductSkuListData = async () => {
  try {
    loading.value = true;
    const params = {
      options: {
        order_by: "created_at",
        desc: true
      },
      page: {
        page: pagination.currentPage,
        limit: pagination.pageSize
      },
      params: []
    };

    const response = await getProductSkuList(params);
    if (response.code === 200) {
      dataList.value = response.data.data || [];
      pagination.total = response.data.total || 0;
    } else {
      ElMessage.error(response.msg || "获取产品SKU列表失败");
    }
  } catch (error) {
    console.error("获取产品SKU列表失败:", error);
    ElMessage.error("获取产品SKU列表失败");
  } finally {
    loading.value = false;
  }
};

// 分页事件处理
const onSizeChange = (size: number) => {
  pagination.pageSize = size;
  getProductSkuListData();
};

const onCurrentChange = (page: number) => {
  pagination.currentPage = page;
  getProductSkuListData();
};

// 操作方法
const handleCreate = () => {
  router.push({ path: "/productSku/create" });
};

const handleView = (row: ProductSkuInfo) => {
  router.push({
    path: "/productSku/detail",
    query: { id: row.id }
  });
};

const handleEdit = (row: ProductSkuInfo) => {
  router.push({
    path: "/productSku/edit",
    query: { id: row.id }
  });
};

const confirmDelete = (row: ProductSkuInfo) => {
  ElMessageBox.confirm(
    `是否确认删除产品SKU "${row.name || row.serial}"？`,
    "删除确认",
    {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning"
    }
  )
    .then(() => {
      handleDelete(row);
    })
    .catch(() => {
      // 用户取消删除
    });
};

const handleDelete = async (row: ProductSkuInfo) => {
  try {
    const response = await deleteProductSku(row.id);
    if (response.code === 200) {
      ElMessage.success("删除成功");
      getProductSkuListData();
    } else {
      ElMessage.error(response.msg || "删除失败");
    }
  } catch (error) {
    console.error("删除产品SKU失败:", error);
    ElMessage.error("删除失败");
  }
};

// 初始化
onMounted(() => {
  getProductSkuListData();
});
</script>

<style scoped>
.main {
  margin: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>
