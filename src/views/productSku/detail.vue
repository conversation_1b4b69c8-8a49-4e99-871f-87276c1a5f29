<template>
  <div class="main">
    <el-card shadow="never">
      <template #header>
        <div class="flex justify-between items-center">
          <h2 class="text-xl font-bold">产品SKU详情</h2>
          <el-button :icon="useRenderIcon('ep:back')" @click="goBack">
            返回列表
          </el-button>
        </div>
      </template>

      <div v-if="productSkuDetail?.id" class="product-sku-detail">
        <el-row :gutter="20">
          <el-col :span="8">
            <!-- 产品图片 -->
            <div class="product-images">
              <el-image
                :src="getMainImage()"
                :preview-src-list="getImageList()"
                fit="cover"
                style="width: 100%; height: 300px"
              />
              <div class="image-list mt-4">
                <el-image
                  v-for="(image, index) in getImageList()"
                  :key="index"
                  :src="image"
                  fit="cover"
                  style="
                    width: 60px;
                    height: 60px;
                    margin-right: 8px;
                    cursor: pointer;
                  "
                />
              </div>
            </div>
          </el-col>

          <el-col :span="16">
            <!-- 产品基本信息 -->
            <el-descriptions title="基本信息" :column="2" border class="mb-6">
              <el-descriptions-item label="产品名称">
                {{ productSkuDetail.name || "-" }}
              </el-descriptions-item>
              <el-descriptions-item label="产品编号">
                {{ productSkuDetail.serial }}
              </el-descriptions-item>
              <el-descriptions-item label="产品系列编号">
                {{ productSkuDetail.product_serial || "-" }}
              </el-descriptions-item>
              <el-descriptions-item label="外部编号">
                {{ productSkuDetail.external_serial || "-" }}
              </el-descriptions-item>
              <el-descriptions-item label="产品类型">
                {{ productSkuDetail.product_type || "-" }}
              </el-descriptions-item>
              <el-descriptions-item label="品牌">
                {{ productSkuDetail.brand || "-" }}
              </el-descriptions-item>
              <el-descriptions-item label="规格">
                {{ productSkuDetail.specification || "-" }}
              </el-descriptions-item>
              <el-descriptions-item label="型号">
                {{ productSkuDetail.model || "-" }}
              </el-descriptions-item>
              <el-descriptions-item label="尺寸">
                {{ productSkuDetail.size || "-" }}
              </el-descriptions-item>
              <el-descriptions-item label="单位">
                {{ productSkuDetail.unit || "-" }}
              </el-descriptions-item>
              <el-descriptions-item label="产地">
                {{ productSkuDetail.source_area || "-" }}
              </el-descriptions-item>
              <el-descriptions-item label="状态">
                <el-tag :type="getStatusTagType(productSkuDetail.status)">
                  {{ getStatusText(productSkuDetail.status) }}
                </el-tag>
              </el-descriptions-item>
            </el-descriptions>
          </el-col>
        </el-row>

        <!-- 价格信息 -->
        <el-descriptions title="价格信息" :column="3" border class="mb-6">
          <el-descriptions-item label="销售价格">
            ¥{{ productSkuDetail.sales_price?.toFixed(2) || "0.00" }}
          </el-descriptions-item>
          <el-descriptions-item label="市场价格">
            ¥{{ productSkuDetail.market_price?.toFixed(2) || "0.00" }}
          </el-descriptions-item>
          <el-descriptions-item label="成本价格">
            ¥{{ productSkuDetail.cost_price?.toFixed(2) || "0.00" }}
          </el-descriptions-item>
        </el-descriptions>

        <!-- 其他信息 -->
        <el-descriptions title="其他信息" :column="2" border class="mb-6">
          <el-descriptions-item label="条形码">
            {{ productSkuDetail.barcode || "-" }}
          </el-descriptions-item>
          <el-descriptions-item label="包装">
            {{ productSkuDetail.package || "-" }}
          </el-descriptions-item>
          <el-descriptions-item label="供应商ID">
            {{ productSkuDetail.supplier_id || "-" }}
          </el-descriptions-item>
          <el-descriptions-item label="排序">
            {{ productSkuDetail.sort || 0 }}
          </el-descriptions-item>
          <el-descriptions-item label="创建时间">
            {{ formatDateTime(productSkuDetail.created_at) }}
          </el-descriptions-item>
          <el-descriptions-item label="更新时间">
            {{ formatDateTime(productSkuDetail.updated_at) }}
          </el-descriptions-item>
        </el-descriptions>

        <!-- 产品描述 -->
        <el-descriptions title="产品描述" :column="1" border class="mb-6">
          <el-descriptions-item label="描述内容">
            <div class="desc-content">
              {{ productSkuDetail.desc || "暂无描述" }}
            </div>
          </el-descriptions-item>
        </el-descriptions>

        <!-- 产品内容 -->
        <el-descriptions title="产品内容" :column="1" border class="mb-6">
          <el-descriptions-item label="详细内容">
            <div class="content-detail">
              {{ productSkuDetail.content || "暂无详细内容" }}
            </div>
          </el-descriptions-item>
        </el-descriptions>

        <!-- 附件信息 -->
        <div
          v-if="
            productSkuDetail.attachment &&
            productSkuDetail.attachment.length > 0
          "
          class="mt-6"
        >
          <h3>附件信息</h3>
          <el-table :data="productSkuDetail.attachment" class="mt-4">
            <el-table-column prop="filename" label="文件名" />
            <el-table-column prop="size" label="文件大小" width="120">
              <template #default="{ row }">
                {{ formatFileSize(row.size) }}
              </template>
            </el-table-column>
            <el-table-column prop="mime_type" label="文件类型" width="150" />
            <el-table-column label="操作" width="100">
              <template #default="{ row }">
                <el-button size="small" @click="downloadFile(row)"
                  >下载</el-button
                >
              </template>
            </el-table-column>
          </el-table>
        </div>

        <div class="mt-6">
          <el-button type="primary" @click="handleEdit">编辑产品SKU</el-button>
          <el-button @click="goBack">返回列表</el-button>
        </div>
      </div>

      <el-empty v-else description="产品SKU信息不存在" />
    </el-card>
  </div>
</template>

<script setup lang="ts">
import {
  getProductSku,
  type ProductSkuInfo,
  type UploadFileResponse
} from "@/api/productSku";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import { formatDateTime } from "@/utils/formatTime";
import { ElMessage } from "element-plus";
import { onMounted, ref } from "vue";
import { useRoute, useRouter } from "vue-router";

defineOptions({
  name: "ProductSkuDetail"
});

const router = useRouter();
const route = useRoute();

const productSkuDetail = ref<ProductSkuInfo>({} as ProductSkuInfo);
const loading = ref(false);

// 获取状态文本
const getStatusText = (status: string) => {
  switch (status) {
    case "active":
      return "上架";
    case "inactive":
      return "下架";
    case "draft":
      return "草稿";
    default:
      return status;
  }
};

// 获取状态标签类型
const getStatusTagType = (status: string) => {
  switch (status) {
    case "active":
      return "success";
    case "inactive":
      return "danger";
    case "draft":
      return "info";
    default:
      return "info";
  }
};

// 获取主图片
const getMainImage = () => {
  if (productSkuDetail.value.image && productSkuDetail.value.image.length > 0) {
    return productSkuDetail.value.image[0].url;
  }
  return "https://via.placeholder.com/300x300";
};

// 获取图片列表
const getImageList = () => {
  if (productSkuDetail.value.image && productSkuDetail.value.image.length > 0) {
    return productSkuDetail.value.image.map(img => img.url);
  }
  return ["https://via.placeholder.com/300x300"];
};

// 格式化文件大小
const formatFileSize = (size: number) => {
  if (size < 1024) return `${size} B`;
  if (size < 1024 * 1024) return `${(size / 1024).toFixed(2)} KB`;
  if (size < 1024 * 1024 * 1024)
    return `${(size / (1024 * 1024)).toFixed(2)} MB`;
  return `${(size / (1024 * 1024 * 1024)).toFixed(2)} GB`;
};

// 下载文件
const downloadFile = (file: UploadFileResponse) => {
  window.open(file.url, "_blank");
};

// 返回列表
const goBack = () => {
  router.push({ path: "/productSku/list" });
};

// 编辑产品SKU
const handleEdit = () => {
  router.push({
    path: "/productSku/edit",
    query: { id: productSkuDetail.value.id }
  });
};

// 获取产品SKU详情
const getProductSkuDetail = async () => {
  const id = route.query.id as string;
  if (!id) {
    ElMessage.error("缺少产品SKU ID参数");
    goBack();
    return;
  }

  try {
    loading.value = true;
    const response = await getProductSku(id);
    if (response.code === 200) {
      productSkuDetail.value = response.data;
    } else {
      ElMessage.error(response.msg || "获取产品SKU详情失败");
    }
  } catch (error) {
    console.error("获取产品SKU详情失败:", error);
    ElMessage.error("获取产品SKU详情失败");
  } finally {
    loading.value = false;
  }
};

// 初始化
onMounted(() => {
  getProductSkuDetail();
});
</script>

<style lang="scss" scoped>
.main {
  margin: 20px;
}

.product-sku-detail {
  .mb-6 {
    margin-bottom: 24px;
  }

  .desc-content,
  .content-detail {
    white-space: pre-wrap;
    word-break: break-word;
    line-height: 1.6;
  }
}

.product-images {
  text-align: center;
}

.image-list {
  display: flex;
  flex-wrap: wrap;
}
</style>
