import type { UserInfo } from "@/api/user";
import { createUser, updateUser } from "@/api/user";
import { addDrawer } from "@/components/ReDrawer";
import { message } from "@/utils/message";
import { cloneDeep } from "@pureadmin/utils";
import UserForm, { type UserFormProps } from "./UserForm.vue";

export interface UseUserFormDrawerOptions {
  onSuccess?: () => void;
}

/**
 * 打开用户表单抽屉
 * @param userData 用户数据（编辑时传入）
 * @param options 配置选项
 */
export function openUserFormDrawer(
  userData?: Partial<UserInfo>,
  options: UseUserFormDrawerOptions = {}
) {
  const { onSuccess } = options;
  const isEdit = !!userData?.id;

  // 准备表单数据
  const formData = {
    id: userData?.id || "",
    login_name: userData?.login_name || "",
    username: userData?.username || "",
    password: "",
    is_admin: userData?.is_admin || false,
    is_active: userData?.is_active !== undefined ? userData.is_active : true,
    remark: userData?.remark || ""
  };

  addDrawer({
    title: isEdit ? "编辑用户" : "新增用户",
    size: "500px",
    closeOnClickModal: false,
    contentRenderer: () => UserForm,
    props: {
      formData: cloneDeep(formData)
    },
    beforeSure: async (done, { options }) => {
      const { formData: currentFormData } = options.props as UserFormProps;

      try {
        const submitData = { ...currentFormData };

        // 如果是编辑模式且密码为空，则不提交密码字段
        if (isEdit && !submitData.password) {
          delete submitData.password;
        }

        if (isEdit) {
          await updateUser(submitData);
          message("用户更新成功", { type: "success" });
        } else {
          await createUser(submitData);
          message("用户创建成功", { type: "success" });
        }

        onSuccess?.();
        done(); // 关闭抽屉
      } catch (error) {
        console.error("提交失败:", error);
        message("操作失败，请重试", { type: "error" });
        // 不调用 done()，保持抽屉打开
      }
    }
  });
}

/**
 * 打开新增用户抽屉
 */
export function openAddUserDrawer(options: UseUserFormDrawerOptions = {}) {
  return openUserFormDrawer(undefined, options);
}

/**
 * 打开编辑用户抽屉
 */
export function openEditUserDrawer(
  userData: UserInfo,
  options: UseUserFormDrawerOptions = {}
) {
  return openUserFormDrawer(userData, options);
}
