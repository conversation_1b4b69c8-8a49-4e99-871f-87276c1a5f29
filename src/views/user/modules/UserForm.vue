<script setup lang="ts">
import { ref } from "vue";

// 声明 props 类型
export interface UserFormProps {
  formData?: {
    id?: string;
    login_name: string;
    username: string;
    login_pwd?: string;
    is_admin: boolean;
    is_active: boolean;
    stable: boolean;
    remark: string;
  };
}

// 声明 props 默认值
const props = withDefaults(defineProps<UserFormProps>(), {
  formData: () => ({
    login_name: "",
    username: "",
    login_pwd: "",
    is_admin: false,
    is_active: true,
    stable: false,
    remark: ""
  })
});

// vue 规定所有的 prop 都遵循着单向绑定原则，直接修改 prop 时，Vue 会抛出警告。
// 此处的写法仅仅是为了消除警告。因为对一个 reactive 对象执行 ref，
// 返回 Ref 对象的 value 值仍为传入的 reactive 对象
const newFormData = ref(props.formData);
</script>

<template>
  <el-form :model="newFormData" label-width="100px" label-position="top">
    <el-form-item label="用户名" prop="login_name">
      <el-input
        v-model="newFormData.login_name"
        placeholder="请输入用户名"
        clearable
      />
    </el-form-item>

    <el-form-item label="真实姓名" prop="username">
      <el-input
        v-model="newFormData.username"
        placeholder="请输入真实姓名"
        clearable
      />
    </el-form-item>

    <el-form-item label="密码" prop="login_pwd">
      <el-input
        v-model="newFormData.login_pwd"
        type="password"
        placeholder="请输入密码"
        show-password
        clearable
      />
    </el-form-item>

    <el-form-item label="管理员权限">
      <el-switch
        v-model="newFormData.is_admin"
        active-text="是"
        inactive-text="否"
      />
    </el-form-item>

    <el-form-item label="账户状态">
      <el-switch
        v-model="newFormData.is_active"
        active-text="启用"
        inactive-text="禁用"
      />
    </el-form-item>

    <el-form-item label="备注" prop="remark">
      <el-input
        v-model="newFormData.remark"
        type="textarea"
        :rows="4"
        placeholder="请输入备注信息"
        maxlength="200"
        show-word-limit
      />
    </el-form-item>
  </el-form>
</template>
