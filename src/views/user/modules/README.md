# 用户表单抽屉组件

基于 vue-pure-admin 的 ReDrawer 实现的用户表单抽屉组件。

## 🎉 重构完成

已从原来的 ReDialog + 自定义样式方式重构为标准的 ReDrawer 实现，更符合 vue-pure-admin 的最佳实践。

## 组件结构

```
src/views/user/modules/
├── UserForm.vue              # 纯表单组件（无业务逻辑）
├── useUserFormDrawer.ts      # 抽屉调用工具函数（包含业务逻辑）
└── README.md                 # 使用说明
```

## 特性

- ✅ 使用标准的 ReDrawer 实现
- ✅ 右侧抽屉弹出，500px 宽度
- ✅ 支持新增和编辑两种模式
- ✅ 自动字段映射（login_name ↔ 用户名，username ↔ 真实姓名）
- ✅ 表单验证和提交处理集成在 beforeSure 回调中
- ✅ 禁止点击遮罩关闭 (`closeOnClickModal: false`)
- ✅ 支持备注字段（最多200字符）
- ✅ 编辑时密码可选（留空不修改）

## 使用方法

```typescript
import {
  openAddUserDrawer,
  openEditUserDrawer
} from "./modules/useUserFormDrawer";

// 新增用户
const handleAdd = () => {
  openAddUserDrawer({
    onSuccess: () => {
      getUserListData(); // 刷新列表
    }
  });
};

// 编辑用户
const handleEdit = (row: UserInfo) => {
  openEditUserDrawer(row, {
    onSuccess: () => {
      getUserListData(); // 刷新列表
    }
  });
};
```

## 字段映射

| 前端显示 | 前端字段名   | 后端字段名   |
| -------- | ------------ | ------------ |
| 用户名   | `login_name` | `login_name` |
| 真实姓名 | `username`   | `username`   |

## 实现原理

1. **UserForm.vue**: 纯表单组件，使用 props 接收数据，无业务逻辑
2. **useUserFormDrawer.ts**:
   - 使用 ReDrawer 的 `addDrawer` 方法
   - 在 `beforeSure` 回调中处理表单提交和 API 调用
   - 使用 `cloneDeep` 确保数据独立性
3. **App.vue**: 已添加 `<ReDrawer />` 组件

## 与原实现的区别

| 特性       | 原实现 (ReDialog) | 新实现 (ReDrawer)   |
| ---------- | ----------------- | ------------------- |
| 组件类型   | 对话框            | 抽屉                |
| 样式实现   | 自定义 CSS 覆盖   | 原生抽屉样式        |
| 业务逻辑   | 分散在表单组件中  | 集中在调用函数中    |
| 表单验证   | 组件内部处理      | beforeSure 回调处理 |
| 代码复杂度 | 高（~300行）      | 低（~100行）        |
| 维护性     | 较难维护          | 易于维护            |
