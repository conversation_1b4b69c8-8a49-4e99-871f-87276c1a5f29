<template>
  <div class="main">
    <el-card shadow="never">
      <template #header>
        <div class="card-header">
          <span class="font-medium">用户详情</span>
          <el-button @click="goBack">返回列表</el-button>
        </div>
      </template>
      
      <el-descriptions :column="2" border>
        <el-descriptions-item label="用户ID">{{ userInfo.id }}</el-descriptions-item>
        <el-descriptions-item label="用户名">{{ userInfo.username }}</el-descriptions-item>
        <el-descriptions-item label="真实姓名">{{ userInfo.realname }}</el-descriptions-item>
        <el-descriptions-item label="邮箱">{{ userInfo.email }}</el-descriptions-item>
        <el-descriptions-item label="手机号">{{ userInfo.phone }}</el-descriptions-item>
        <el-descriptions-item label="角色">{{ userInfo.role }}</el-descriptions-item>
        <el-descriptions-item label="状态">
          <el-tag :type="userInfo.status === 'active' ? 'success' : 'danger'">
            {{ userInfo.status === 'active' ? '启用' : '禁用' }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="是否管理员">
          <el-tag :type="userInfo.isAdmin ? 'warning' : 'info'">
            {{ userInfo.isAdmin ? '是' : '否' }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="创建时间">{{ userInfo.createTime }}</el-descriptions-item>
        <el-descriptions-item label="最后登录">{{ userInfo.lastLoginTime }}</el-descriptions-item>
        <el-descriptions-item label="登录次数">{{ userInfo.loginCount }}</el-descriptions-item>
        <el-descriptions-item label="部门">{{ userInfo.department }}</el-descriptions-item>
        <el-descriptions-item label="备注" :span="2">{{ userInfo.remark }}</el-descriptions-item>
      </el-descriptions>

      <div class="mt-6">
        <h3>权限信息</h3>
        <el-row :gutter="20" class="mt-4">
          <el-col :span="12">
            <el-card>
              <template #header>
                <span>角色权限</span>
              </template>
              <el-tag v-for="permission in userInfo.permissions" :key="permission" class="mr-2 mb-2">
                {{ permission }}
              </el-tag>
            </el-card>
          </el-col>
          <el-col :span="12">
            <el-card>
              <template #header>
                <span>菜单权限</span>
              </template>
              <el-tree
                :data="userInfo.menuPermissions"
                :props="{ children: 'children', label: 'title' }"
                default-expand-all
                node-key="name"
              />
            </el-card>
          </el-col>
        </el-row>
      </div>

      <div class="mt-6">
        <h3>操作日志</h3>
        <el-table :data="operationLogs" class="mt-4">
          <el-table-column prop="time" label="操作时间" width="180" />
          <el-table-column prop="action" label="操作类型" width="120" />
          <el-table-column prop="description" label="操作描述" />
          <el-table-column prop="ip" label="IP地址" width="140" />
          <el-table-column prop="userAgent" label="浏览器" />
        </el-table>
      </div>

      <div class="mt-6">
        <el-button type="primary" @click="handleEdit">编辑用户</el-button>
        <el-button type="warning" @click="handleResetPassword">重置密码</el-button>
        <el-button type="danger" @click="handleToggleStatus">
          {{ userInfo.status === 'active' ? '禁用用户' : '启用用户' }}
        </el-button>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from "vue";
import { useRouter, useRoute } from "vue-router";
import { message } from "@/utils/message";

defineOptions({
  name: "UserDetail"
});

const router = useRouter();
const route = useRoute();

const userInfo = ref({
  id: "",
  username: "",
  realname: "",
  email: "",
  phone: "",
  role: "",
  status: "active",
  isAdmin: false,
  createTime: "",
  lastLoginTime: "",
  loginCount: 0,
  department: "",
  remark: "",
  permissions: [] as string[],
  menuPermissions: [] as any[]
});

const operationLogs = ref([
  {
    time: "2024-01-15 10:30:00",
    action: "登录",
    description: "用户登录系统",
    ip: "*************",
    userAgent: "Chrome 120.0.0.0"
  },
  {
    time: "2024-01-15 11:15:00",
    action: "查看",
    description: "查看用户列表",
    ip: "*************",
    userAgent: "Chrome 120.0.0.0"
  },
  {
    time: "2024-01-15 14:20:00",
    action: "编辑",
    description: "修改个人信息",
    ip: "*************",
    userAgent: "Chrome 120.0.0.0"
  }
]);

const goBack = () => {
  router.push("/user/list");
};

const handleEdit = () => {
  message(`编辑用户: ${userInfo.value.username}`, { type: "info" });
};

const handleResetPassword = () => {
  message(`重置用户密码: ${userInfo.value.username}`, { type: "warning" });
};

const handleToggleStatus = () => {
  const action = userInfo.value.status === 'active' ? '禁用' : '启用';
  message(`${action}用户: ${userInfo.value.username}`, { type: "warning" });
};

const loadUserDetail = async () => {
  const id = route.query.id;
  // 模拟数据
  userInfo.value = {
    id: id as string || "1",
    username: "admin",
    realname: "管理员",
    email: "<EMAIL>",
    phone: "13800138000",
    role: "管理员",
    status: "active",
    isAdmin: true,
    createTime: "2024-01-01 10:00:00",
    lastLoginTime: "2024-01-15 09:30:00",
    loginCount: 156,
    department: "技术部",
    remark: "系统管理员账户",
    permissions: ["用户管理", "角色管理", "菜单管理", "系统设置"],
    menuPermissions: [
      {
        name: "userManagement",
        title: "用户管理",
        children: [
          { name: "userList", title: "用户列表" },
          { name: "userDetail", title: "用户详情" }
        ]
      },
      {
        name: "systemManagement",
        title: "系统管理",
        children: [
          { name: "roleList", title: "角色管理" },
          { name: "menuList", title: "菜单管理" }
        ]
      }
    ]
  };
};

onMounted(() => {
  loadUserDetail();
});
</script>

<style scoped>
.main {
  margin: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.mr-2 {
  margin-right: 8px;
}

.mb-2 {
  margin-bottom: 8px;
}
</style>
