<template>
  <div class="main">
    <!-- 搜索表单 -->
    <el-card class="box-card" shadow="never">
      <template #header>
        <div class="card-header">
          <span>用户管理</span>
        </div>
      </template>

      <el-form :inline="true" :model="searchForm" class="demo-form-inline">
        <el-form-item label="用户名">
          <el-input
            v-model="searchForm.login_name"
            placeholder="请输入用户名"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="真实姓名">
          <el-input
            v-model="searchForm.username"
            placeholder="请输入真实姓名"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button @click="resetSearch">重置</el-button>
          <el-button type="primary" @click="handleAdd">新增用户</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 表格 -->
    <el-card class="box-card" shadow="never" style="margin-top: 16px">
      <pure-table
        ref="tableRef"
        row-key="id"
        align-whole="center"
        showOverflowTooltip
        :loading="loading"
        :data="dataList"
        :columns="columns"
        :pagination="pagination"
        @page-size-change="onSizeChange"
        @page-current-change="onCurrentChange"
      >
        <template #operation="{ row }">
          <el-button link type="primary" size="small" @click="handleEdit(row)">
            编辑
          </el-button>
          <el-button link type="danger" size="small" @click="handleDelete(row)">
            删除
          </el-button>
        </template>
      </pure-table>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import {
  deleteUser,
  getUserList,
  type UserInfo,
  type UserListParams
} from "@/api/user";
import { formatDateTime } from "@/utils/formatTime";
import { PureTable } from "@pureadmin/table";
import "@pureadmin/table/dist/style.css";
import { ElMessage, ElMessageBox } from "element-plus";
import { computed, onMounted, reactive, ref } from "vue";
import {
  openAddUserDrawer,
  openEditUserDrawer
} from "./modules/useUserFormDrawer";

defineOptions({
  name: "UserList"
});

// 响应式数据
const loading = ref(false);
const dataList = ref<UserInfo[]>([]);
const tableRef = ref();

// 搜索表单
const searchForm = reactive({
  login_name: "", // 对应后端的login_name字段
  username: "" // 对应后端的username字段（前端显示为真实姓名）
});

// 分页配置
const pagination = reactive({
  total: 0,
  pageSize: 10,
  currentPage: 1,
  pageSizes: [10, 20, 50, 100],
  small: false,
  background: true,
  layout: "total, sizes, prev, pager, next, jumper"
});

// 表格列定义
const columns = computed(() => [
  {
    label: "ID",
    prop: "id",
    width: 200,
    cellRenderer: ({ row }: { row: UserInfo }) => {
      // 显示ID的后8位
      return row.id.length > 8 ? row.id.slice(-8) : row.id;
    }
  },
  {
    label: "用户名",
    prop: "login_name", // 对应后端的login_name字段
    width: 150
  },
  {
    label: "真实姓名",
    prop: "username", // 对应后端的username字段
    width: 150
  },
  {
    label: "管理员",
    prop: "is_admin",
    width: 100,
    cellRenderer: ({ row }: { row: UserInfo }) => {
      return row.is_admin ? "是" : "否";
    }
  },
  {
    label: "状态",
    prop: "is_active",
    width: 100,
    cellRenderer: ({ row }: { row: UserInfo }) => {
      return row.is_active ? "启用" : "停用";
    }
  },
  {
    label: "创建时间",
    prop: "created_at",
    width: 180,
    cellRenderer: ({ row }: { row: UserInfo }) => {
      return formatDateTime(row.created_at);
    }
  },
  {
    label: "操作",
    fixed: "right",
    width: 160,
    slot: "operation"
  }
]);

// 获取用户列表
const getUserListData = async () => {
  loading.value = true;
  try {
    const params: UserListParams = {
      options: {
        order_by: "created_at",
        desc: false
      },
      page: {
        page: pagination.currentPage,
        limit: pagination.pageSize
      }
    };

    // 添加搜索参数
    if (searchForm.login_name || searchForm.username) {
      params.params = [];
      if (searchForm.login_name) {
        params.params.push({
          var: "login_name",
          val: searchForm.login_name
        });
      }
      if (searchForm.username) {
        params.params.push({
          var: "username",
          val: searchForm.username
        });
      }
    }

    const response = await getUserList(params);
    if (response.code === 200) {
      dataList.value = response.data.data;
      pagination.total = response.data.total;
    } else {
      ElMessage.error(response.msg || "获取用户列表失败");
    }
  } catch (error) {
    console.error("获取用户列表失败:", error);
    ElMessage.error("获取用户列表失败");
  } finally {
    loading.value = false;
  }
};

// 搜索
const handleSearch = () => {
  pagination.currentPage = 1;
  getUserListData();
};

// 重置搜索
const resetSearch = () => {
  searchForm.login_name = "";
  searchForm.username = "";
  pagination.currentPage = 1;
  getUserListData();
};

// 分页大小改变
const onSizeChange = (size: number) => {
  pagination.pageSize = size;
  pagination.currentPage = 1;
  getUserListData();
};

// 当前页改变
const onCurrentChange = (page: number) => {
  pagination.currentPage = page;
  getUserListData();
};

// 新增用户
const handleAdd = () => {
  openAddUserDrawer({
    onSuccess: () => {
      getUserListData();
    }
  });
};

// 编辑用户
const handleEdit = (row: UserInfo) => {
  openEditUserDrawer(row, {
    onSuccess: () => {
      getUserListData();
    }
  });
};

// 删除用户
const handleDelete = async (row: UserInfo) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除用户 "${row.username}" 吗？`,
      "确认删除",
      {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }
    );

    const response = await deleteUser(row.id);
    if (response.code === 200) {
      ElMessage.success("删除成功");
      getUserListData();
    } else {
      ElMessage.error(response.msg || "删除失败");
    }
  } catch (error) {
    if (error !== "cancel") {
      console.error("删除用户失败:", error);
      ElMessage.error("删除失败");
    }
  }
};

// 页面加载时获取数据
onMounted(() => {
  getUserListData();
});
</script>

<style scoped>
.main {
  padding: 16px;
}

.box-card {
  margin-bottom: 16px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>
