<template>
  <div class="main">
    <el-card shadow="never">
      <template #header>
        <div class="card-header">
          <span class="font-medium">仓库列表</span>
        </div>
      </template>

      <div class="mb-4">
        <el-button
          type="primary"
          :icon="useRenderIcon('ep:plus')"
          @click="handleCreate"
        >
          添加仓库
        </el-button>
      </div>

      <pure-table
        ref="tableRef"
        row-key="id"
        align-whole="center"
        showOverflowTooltip
        :loading="loading"
        :data="dataList"
        :columns="columns"
        :pagination="pagination"
        @page-size-change="onSizeChange"
        @page-current-change="onCurrentChange"
      >
        <template #created_at="{ row }">
          <span>{{ formatDateTime(row.created_at) }}</span>
        </template>
        <template #operation="{ row }">
          <el-dropdown trigger="click">
            <el-button type="primary" size="small">
              操作<el-icon class="el-icon--right"><ArrowDown /></el-icon>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item :icon="View" @click="handleView(row)">
                  查看
                </el-dropdown-item>
                <el-dropdown-item :icon="Edit" @click="handleEdit(row)">
                  编辑
                </el-dropdown-item>
                <el-dropdown-item :icon="Delete" @click="confirmDelete(row)">
                  删除
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </template>
      </pure-table>
    </el-card>

    <!-- 仓库表单抽屉 -->
    <WarehouseForm
      v-model="showForm"
      :warehouse-id="currentWarehouseId"
      @success="handleFormSuccess"
    />
  </div>
</template>

<script setup lang="ts">
import {
  deleteWarehouse,
  getWarehouseList,
  type WarehouseInfo
} from "@/api/warehouse";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import { formatDateTime } from "@/utils/formatTime";
import { ArrowDown, Delete, Edit, View } from "@element-plus/icons-vue";
import { PureTable } from "@pureadmin/table";
import { ElMessage, ElMessageBox } from "element-plus";
import { computed, onMounted, reactive, ref } from "vue";
import { useRouter } from "vue-router";
import WarehouseForm from "./modules/WarehouseForm.vue";

defineOptions({
  name: "WarehouseList"
});

const router = useRouter();
const tableRef = ref();

// 响应式数据
const loading = ref(false);
const dataList = ref<WarehouseInfo[]>([]);
const showForm = ref(false);
const currentWarehouseId = ref("");

// 分页配置
const pagination = reactive({
  total: 0,
  pageSize: 10,
  currentPage: 1,
  background: true,
  pageSizes: [10, 20, 50, 100]
});

// 表格列配置
const columns = computed(() => [
  {
    label: "仓库名称",
    prop: "name",
    minWidth: 150
  },
  {
    label: "仓库编号",
    prop: "serial",
    minWidth: 120
  },
  {
    label: "所属公司",
    prop: "company",
    minWidth: 120
  },
  {
    label: "联系人",
    prop: "contacts_person",
    minWidth: 100
  },
  {
    label: "联系电话",
    prop: "contacts_phone",
    minWidth: 120
  },
  {
    label: "联系地址",
    prop: "contact_address",
    minWidth: 150
  },
  {
    label: "仓库位置",
    prop: "location",
    minWidth: 150
  },
  {
    label: "创建时间",
    prop: "created_at",
    minWidth: 160,
    slot: "created_at"
  },
  {
    label: "操作",
    fixed: "right",
    width: 100,
    slot: "operation"
  }
]);

// 获取仓库列表数据
const getWarehouseListData = async () => {
  try {
    loading.value = true;
    const params = {
      options: {
        order_by: "created_at",
        desc: true
      },
      page: {
        page: pagination.currentPage,
        limit: pagination.pageSize
      },
      params: []
    };

    const response = await getWarehouseList(params);
    if (response.code === 200) {
      dataList.value = response.data.data || [];
      pagination.total = response.data.total || 0;
    } else {
      ElMessage.error(response.msg || "获取仓库列表失败");
    }
  } catch (error) {
    console.error("获取仓库列表失败:", error);
    ElMessage.error("获取仓库列表失败");
  } finally {
    loading.value = false;
  }
};

// 分页事件处理
const onSizeChange = (size: number) => {
  pagination.pageSize = size;
  getWarehouseListData();
};

const onCurrentChange = (page: number) => {
  pagination.currentPage = page;
  getWarehouseListData();
};

// 操作方法
const handleCreate = () => {
  currentWarehouseId.value = "";
  showForm.value = true;
};

const handleView = (row: WarehouseInfo) => {
  router.push({
    name: "warehouseDetail",
    query: { id: row.id }
  });
};

const handleEdit = (row: WarehouseInfo) => {
  currentWarehouseId.value = row.id;
  showForm.value = true;
};

const confirmDelete = (row: WarehouseInfo) => {
  ElMessageBox.confirm(`是否确认删除仓库${row.name}？`, "删除确认", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning"
  })
    .then(() => {
      handleDelete(row);
    })
    .catch(() => {
      // 用户取消删除
    });
};

const handleDelete = async (row: WarehouseInfo) => {
  try {
    const response = await deleteWarehouse(row.id);
    if (response.code === 200) {
      ElMessage.success("删除成功");
      getWarehouseListData();
    } else {
      ElMessage.error(response.msg || "删除失败");
    }
  } catch (error) {
    console.error("删除仓库失败:", error);
    ElMessage.error("删除失败");
  }
};

// 表单成功回调
const handleFormSuccess = () => {
  getWarehouseListData();
};

// 初始化
onMounted(() => {
  getWarehouseListData();
});
</script>

<style scoped>
.main {
  margin: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>
