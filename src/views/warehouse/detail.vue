<template>
  <div class="main">
    <el-card shadow="never">
      <template #header>
        <div class="flex justify-between items-center">
          <h2 class="text-xl font-bold">仓库详情</h2>
          <el-button :icon="useRenderIcon('ep:back')" @click="goBack">
            返回列表
          </el-button>
        </div>
      </template>

      <div v-if="warehouseDetail?.id" class="warehouse-detail">
        <!-- 基本信息 -->
        <el-descriptions title="基本信息" :column="3" border class="mb-6">
          <el-descriptions-item label="仓库名称">
            {{ warehouseDetail.name }}
          </el-descriptions-item>
          <el-descriptions-item label="仓库编号">
            {{ warehouseDetail.serial }}
          </el-descriptions-item>
          <el-descriptions-item label="所属公司">
            {{ warehouseDetail.company || "-" }}
          </el-descriptions-item>
          <el-descriptions-item label="仓库位置">
            {{ warehouseDetail.location || "-" }}
          </el-descriptions-item>
          <el-descriptions-item label="创建时间">
            {{ formatDateTime(warehouseDetail.created_at) }}
          </el-descriptions-item>
          <el-descriptions-item label="更新时间">
            {{ formatDateTime(warehouseDetail.updated_at) }}
          </el-descriptions-item>
        </el-descriptions>

        <!-- 联系信息 -->
        <el-descriptions title="联系信息" :column="2" border class="mb-6">
          <el-descriptions-item label="联系人">
            {{ warehouseDetail.contacts_person || "-" }}
          </el-descriptions-item>
          <el-descriptions-item label="联系电话">
            {{ warehouseDetail.contacts_phone || "-" }}
          </el-descriptions-item>
          <el-descriptions-item label="联系地址" :span="2">
            {{ warehouseDetail.contact_address || "-" }}
          </el-descriptions-item>
        </el-descriptions>

        <!-- 备注信息 -->
        <el-descriptions title="备注信息" :column="1" border>
          <el-descriptions-item label="备注内容">
            <div class="remark-content">
              {{ warehouseDetail.remark || "暂无备注" }}
            </div>
          </el-descriptions-item>
        </el-descriptions>
      </div>

      <el-empty v-else description="仓库信息不存在" />
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { getWarehouse, type WarehouseInfo } from "@/api/warehouse";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import { formatDateTime } from "@/utils/formatTime";
import { ElMessage } from "element-plus";
import { onMounted, ref } from "vue";
import { useRoute, useRouter } from "vue-router";

defineOptions({
  name: "WarehouseDetail"
});

const router = useRouter();
const route = useRoute();

const warehouseDetail = ref<WarehouseInfo>({} as WarehouseInfo);
const loading = ref(false);

// 返回列表
const goBack = () => {
  router.push({ path: "/warehouse/list" });
};

// 获取仓库详情
const getWarehouseDetail = async () => {
  try {
    loading.value = true;
    const response = await getWarehouse(route.query.id as string);
    if (response.code === 200) {
      warehouseDetail.value = response.data;
    } else {
      ElMessage.error(response.msg || "获取仓库详情失败");
    }
  } catch (error) {
    console.error("获取仓库详情失败:", error);
    ElMessage.error("获取仓库详情失败");
  } finally {
    loading.value = false;
  }
};

// 初始化
onMounted(() => {
  if (route.query.id) {
    getWarehouseDetail();
  } else {
    ElMessage.error("缺少仓库ID参数");
    goBack();
  }
});
</script>

<style lang="scss" scoped>
.main {
  margin: 20px;
}

.warehouse-detail {
  .mb-6 {
    margin-bottom: 24px;
  }

  .remark-content {
    white-space: pre-wrap;
    word-break: break-word;
    line-height: 1.6;
  }
}
</style>
