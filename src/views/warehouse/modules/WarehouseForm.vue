<template>
  <el-drawer
    v-model="visible"
    :title="isEdit ? '编辑仓库' : '添加仓库'"
    direction="rtl"
    size="600px"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @close="handleClose"
  >
    <template #default>
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="120px"
        label-position="left"
      >
        <el-form-item label="仓库编号" prop="serial">
          <el-input
            v-model="formData.serial"
            placeholder="请输入仓库编号"
            clearable
          />
        </el-form-item>

        <el-form-item label="仓库名称" prop="name">
          <el-input
            v-model="formData.name"
            placeholder="请输入仓库名称"
            clearable
          />
        </el-form-item>

        <el-form-item label="所属公司" prop="company">
          <el-input
            v-model="formData.company"
            placeholder="请输入所属公司"
            clearable
          />
        </el-form-item>

        <el-form-item label="联系人" prop="contacts_person">
          <el-input
            v-model="formData.contacts_person"
            placeholder="请输入联系人"
            clearable
          />
        </el-form-item>

        <el-form-item label="联系电话" prop="contacts_phone">
          <el-input
            v-model="formData.contacts_phone"
            placeholder="请输入联系电话"
            clearable
          />
        </el-form-item>

        <el-form-item label="联系地址" prop="contact_address">
          <el-input
            v-model="formData.contact_address"
            type="textarea"
            :rows="3"
            placeholder="请输入联系地址"
          />
        </el-form-item>

        <el-form-item label="仓库位置" prop="location">
          <el-input
            v-model="formData.location"
            placeholder="请输入仓库位置"
            clearable
          />
        </el-form-item>

        <el-form-item label="备注" prop="remark">
          <el-input
            v-model="formData.remark"
            type="textarea"
            :rows="4"
            placeholder="请输入备注信息"
          />
        </el-form-item>
      </el-form>
    </template>

    <template #footer>
      <div class="flex justify-end gap-4">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" :loading="loading" @click="handleSubmit">
          {{ isEdit ? "更新" : "创建" }}
        </el-button>
      </div>
    </template>
  </el-drawer>
</template>

<script setup lang="ts">
import {
  createWarehouse,
  getWarehouse,
  updateWarehouse,
  type WarehouseCreateRequest,
  type WarehouseUpdateRequest
} from "@/api/warehouse";
import { ElMessage, type FormInstance, type FormRules } from "element-plus";
import { reactive, ref, watch } from "vue";

// Props
interface Props {
  modelValue: boolean;
  warehouseId?: string;
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: false,
  warehouseId: ""
});

// Emits
const emit = defineEmits<{
  "update:modelValue": [value: boolean];
  success: [];
}>();

// 响应式数据
const visible = ref(false);
const loading = ref(false);
const formRef = ref<FormInstance>();
const isEdit = ref(false);

// 表单数据
const formData = reactive<WarehouseCreateRequest>({
  serial: "",
  name: "",
  company: "",
  contacts_person: "",
  contacts_phone: "",
  contact_address: "",
  location: "",
  remark: ""
});

// 表单验证规则
const formRules: FormRules = {
  serial: [{ required: true, message: "请输入仓库编号", trigger: "blur" }],
  name: [{ required: true, message: "请输入仓库名称", trigger: "blur" }],
  contacts_phone: [
    {
      pattern: /^1[3-9]\d{9}$/,
      message: "请输入正确的手机号码",
      trigger: "blur"
    }
  ]
};

// 监听 modelValue 变化
watch(
  () => props.modelValue,
  newVal => {
    visible.value = newVal;
    if (newVal) {
      isEdit.value = !!props.warehouseId;
      if (isEdit.value) {
        getWarehouseDetail();
      } else {
        resetForm();
      }
    }
  },
  { immediate: true }
);

// 监听 visible 变化
watch(visible, newVal => {
  emit("update:modelValue", newVal);
});

// 重置表单
const resetForm = () => {
  Object.assign(formData, {
    serial: "",
    name: "",
    company: "",
    contacts_person: "",
    contacts_phone: "",
    contact_address: "",
    location: "",
    remark: ""
  });
  formRef.value?.clearValidate();
};

// 获取仓库详情
const getWarehouseDetail = async () => {
  if (!props.warehouseId) return;

  try {
    loading.value = true;
    const response = await getWarehouse(props.warehouseId);
    if (response.code === 200) {
      const data = response.data;
      Object.assign(formData, {
        serial: data.serial || "",
        name: data.name || "",
        company: data.company || "",
        contacts_person: data.contacts_person || "",
        contacts_phone: data.contacts_phone || "",
        contact_address: data.contact_address || "",
        location: data.location || "",
        remark: data.remark || ""
      });
    } else {
      ElMessage.error(response.msg || "获取仓库信息失败");
    }
  } catch (error) {
    console.error("获取仓库详情失败:", error);
    ElMessage.error("获取仓库信息失败");
  } finally {
    loading.value = false;
  }
};

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return;

  try {
    const valid = await formRef.value.validate();
    if (!valid) return;

    loading.value = true;

    if (isEdit.value) {
      // 编辑
      const updateData: WarehouseUpdateRequest = {
        id: props.warehouseId!,
        ...formData
      };
      const response = await updateWarehouse(updateData);
      if (response.code === 200) {
        ElMessage.success("更新成功");
        emit("success");
        handleClose();
      } else {
        ElMessage.error(response.msg || "更新失败");
      }
    } else {
      // 创建
      const response = await createWarehouse(formData);
      if (response.code === 200) {
        ElMessage.success("创建成功");
        emit("success");
        handleClose();
      } else {
        ElMessage.error(response.msg || "创建失败");
      }
    }
  } catch (error) {
    console.error("提交失败:", error);
    ElMessage.error("操作失败");
  } finally {
    loading.value = false;
  }
};

// 关闭抽屉
const handleClose = () => {
  visible.value = false;
  resetForm();
};
</script>

<style lang="scss" scoped>
:deep(.el-drawer__header) {
  margin-bottom: 20px;
}

:deep(.el-drawer__body) {
  padding: 0 20px;
}

:deep(.el-drawer__footer) {
  padding: 20px;
  border-top: 1px solid var(--el-border-color-light);
}
</style>
