<script setup lang="ts">
import { computed, ref } from "vue";

// 声明 props 类型
export interface PermissionFormProps {
  formData?: {
    id?: string;
    name: string;
    group: string;
    method: string;
    path: string;
    backup?: string;
  };
}

// 声明 props 默认值
const props = withDefaults(defineProps<PermissionFormProps>(), {
  formData: () => ({
    name: "",
    group: "",
    method: "",
    path: "",
    backup: ""
  })
});

// vue 规定所有的 prop 都遵循着单向绑定原则，直接修改 prop 时，Vue 会抛出警告。
// 此处的写法仅仅是为了消除警告。因为对一个 reactive 对象执行 ref，
// 返回 Ref 对象的 value 值仍为传入的 reactive 对象
const newFormData = ref(props.formData);

// HTTP方法选项
const methodOptions = [
  { label: "GET", value: "GET" },
  { label: "POST", value: "POST" },
  { label: "PUT", value: "PUT" },
  { label: "DELETE", value: "DELETE" },
  { label: "OPTION", value: "OPTION" }
];

// 表单验证规则
const rules = {
  name: [
    { required: true, message: "请输入权限名称", trigger: "blur" }
  ],
  group: [
    { required: true, message: "请输入API分组", trigger: "blur" }
  ],
  method: [
    { required: true, message: "请选择请求方法", trigger: "change" }
  ],
  path: [
    { required: true, message: "请输入API路径", trigger: "blur" }
  ]
};
</script>

<template>
  <el-form :model="newFormData" :rules="rules" label-width="120px" label-position="top">
    <el-form-item label="权限名称" prop="name">
      <el-input
        v-model="newFormData.name"
        placeholder="请输入权限名称"
        clearable
      />
    </el-form-item>

    <div class="grid grid-cols-2 gap-4">
      <el-form-item label="API分组" prop="group">
        <el-input
          v-model="newFormData.group"
          placeholder="请输入API分组"
          clearable
        />
      </el-form-item>

      <el-form-item label="请求方法" prop="method">
        <el-select
          v-model="newFormData.method"
          placeholder="请选择请求方法"
          class="w-full"
        >
          <el-option
            v-for="option in methodOptions"
            :key="option.value"
            :label="option.label"
            :value="option.value"
          />
        </el-select>
      </el-form-item>
    </div>

    <el-form-item label="API路径" prop="path">
      <el-input
        v-model="newFormData.path"
        placeholder="请输入API路径"
        clearable
      />
    </el-form-item>

    <el-form-item label="备注" prop="backup">
      <el-input
        v-model="newFormData.backup"
        type="textarea"
        :rows="4"
        placeholder="请输入备注信息"
        maxlength="200"
        show-word-limit
      />
    </el-form-item>
  </el-form>
</template>
