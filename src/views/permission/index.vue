<template>
  <div class="main">
    <el-card shadow="never">
      <template #header>
        <div class="card-header">
          <span class="font-medium">权限管理</span>
        </div>
      </template>

      <div class="mb-4">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-input
              v-model="groupInput"
              placeholder="请输入API分组"
              clearable
            />
          </el-col>
          <el-col :span="6">
            <el-input
              v-model="queryParams.method"
              placeholder="请输入请求方法"
              clearable
            />
          </el-col>
          <el-col :span="6">
            <el-button type="primary" @click="handleSearch">搜索</el-button>
            <el-button @click="resetSearch">重置</el-button>
          </el-col>
          <el-col :span="6" class="text-right">
            <el-button type="primary" @click="showAddForm">
              <template #icon>
                <IconifyIconOnline icon="ep:plus" />
              </template>
              新增权限
            </el-button>
          </el-col>
        </el-row>
      </div>

      <pure-table
        border
        row-key="id"
        alignWhole="center"
        showOverflowTooltip
        :loading="loading"
        :data="tableData"
        :columns="columns"
        :pagination="pagination"
        @page-size-change="onSizeChange"
        @page-current-change="onCurrentChange"
      >
        <template #operation="{ row }">
          <el-button size="small" type="primary" @click="showEditForm(row)">
            编辑
          </el-button>
          <el-button size="small" type="danger" @click="handleDelete(row)">
            删除
          </el-button>
        </template>
      </pure-table>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import {
  deletePermission,
  getPermissionList,
  type PermissionInfo,
  type PermissionListParams
} from "@/api/permission";
import { addDrawer } from "@/components/ReDrawer";
import { IconifyIconOnline } from "@/components/ReIcon";
import { message } from "@/utils/message";
import { PureTable } from "@pureadmin/table";
import { computed, onMounted, ref } from "vue";
import PermissionForm from "./modules/PermissionForm.vue";

defineOptions({
  name: "PermissionList"
});

// 分页配置
const pagination = ref({
  currentPage: 1,
  pageSize: 10,
  total: 0,
  background: true
});

// 查询参数
const queryParams = ref<{
  group?: string;
  method?: string;
}>({});

// 表格数据
const tableData = ref<PermissionInfo[]>([]);
const loading = ref(false);

// 计算属性：处理group输入
const groupInput = computed({
  get: () => {
    return queryParams.value.group?.replace(/^'|'$/g, "") || "";
  },
  set: (val: string) => {
    queryParams.value.group = val ? `'${val}'` : "";
  }
});

// 表格列配置
const columns = computed(() => [
  {
    label: "权限名称",
    prop: "name",
    minWidth: 120
  },
  {
    label: "API分组",
    prop: "group",
    minWidth: 120
  },
  {
    label: "请求方法",
    prop: "method",
    minWidth: 100
  },
  {
    label: "API路径",
    prop: "path",
    minWidth: 200
  },
  {
    label: "备注",
    prop: "backup",
    minWidth: 150
  },
  {
    label: "操作",
    fixed: "right",
    width: 160,
    slot: "operation"
  }
]);

// 获取表格数据
const getTableData = async () => {
  loading.value = true;
  try {
    const params: PermissionListParams = {
      options: {
        order_by: "id",
        desc: false
      },
      page: {
        page: pagination.value.currentPage,
        limit: pagination.value.pageSize
      }
    };

    // 添加查询参数
    if (queryParams.value && Object.keys(queryParams.value).length > 0) {
      params.params = Object.entries(queryParams.value)
        .filter(([, value]) => value)
        .map(([key, value]) => ({
          var: key,
          val: value as string
        }));
    }

    const response = await getPermissionList(params);
    if (response.code === 200) {
      tableData.value = response.data.data;
      pagination.value.total = response.data.total;
    }
  } catch (error) {
    console.error("获取权限列表失败:", error);
    message("获取权限列表失败", { type: "error" });
  } finally {
    loading.value = false;
  }
};

// 搜索
const handleSearch = () => {
  pagination.value.currentPage = 1;
  getTableData();
};

// 重置搜索
const resetSearch = () => {
  queryParams.value = {};
  groupInput.value = "";
  pagination.value.currentPage = 1;
  getTableData();
};

// 分页事件
const onSizeChange = (size: number) => {
  pagination.value.pageSize = size;
  getTableData();
};

const onCurrentChange = (current: number) => {
  pagination.value.currentPage = current;
  getTableData();
};

// 显示新增表单
const showAddForm = () => {
  addDrawer({
    title: "新增权限",
    size: "500px",
    closeOnClickModal: false,
    contentRenderer: () => PermissionForm,
    props: {
      formData: {
        name: "",
        group: "",
        method: "",
        path: "",
        backup: ""
      }
    },
    beforeSure: async (done, { options }) => {
      const { formData } = options.props;
      try {
        const { createPermission } = await import("@/api/permission");
        await createPermission(formData);
        message("权限创建成功", { type: "success" });
        getTableData();
        done();
      } catch (error) {
        console.error("创建权限失败:", error);
        message("创建权限失败", { type: "error" });
      }
    }
  });
};

// 显示编辑表单
const showEditForm = (row: PermissionInfo) => {
  addDrawer({
    title: "编辑权限",
    size: "500px",
    closeOnClickModal: false,
    contentRenderer: () => PermissionForm,
    props: {
      formData: { ...row }
    },
    beforeSure: async (done, { options }) => {
      const { formData } = options.props;
      try {
        const { updatePermission } = await import("@/api/permission");
        await updatePermission(formData);
        message("权限更新成功", { type: "success" });
        getTableData();
        done();
      } catch (error) {
        console.error("更新权限失败:", error);
        message("更新权限失败", { type: "error" });
      }
    }
  });
};

// 删除权限
const handleDelete = async (row: PermissionInfo) => {
  try {
    await deletePermission(row.id);
    message("权限删除成功", { type: "success" });
    getTableData();
  } catch (error) {
    console.error("删除权限失败:", error);
    message("删除权限失败", { type: "error" });
  }
};

// 初始化
onMounted(() => {
  getTableData();
});
</script>

<style scoped>
.main {
  margin: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.text-right {
  text-align: right;
}
</style>
