<template>
  <el-drawer
    v-model="recordDetailVisible"
    title="手动导入订单"
    size="500px"
    :destroy-on-close="true"
    direction="rtl"
    :close-on-click-modal="false"
  >
    <el-form ref="recordDetailForm" :model="formData" label-width="120px">
      <el-form-item label="选择关联合同" required>
        <SelectSerialItem
          v-model:selectItem="selectContract"
          selection="single"
          :can-edit="true"
          :url="url.contract"
        />
      </el-form-item>

      <el-form-item label="选择导入平台" required>
        <el-radio-group v-model="selectPlatform">
          <el-radio
            v-for="item in platform"
            :key="item.value"
            :label="item.value"
          >
            {{ item.label }}
          </el-radio>
        </el-radio-group>
      </el-form-item>

      <el-form-item label="工作表名称">
        <el-input
          v-model="sheetName"
          placeholder="导入文件的工作表的Sheet名字，不输入就默认为sheet1, 注意大小写！"
        />
      </el-form-item>

      <el-form-item label="上传文件" required>
        <el-upload
          ref="uploadRef"
          :auto-upload="false"
          :limit="1"
          accept=".xlsx,.xls,.csv"
          :on-change="handleFileChange"
          :file-list="fileList"
        >
          <el-button type="primary">选择文件</el-button>
          <template #tip>
            <div class="el-upload__tip">
              只能上传 xlsx/xls/csv 文件，且不超过 10MB
            </div>
          </template>
        </el-upload>
      </el-form-item>
    </el-form>

    <template #footer>
      <div style="flex: auto">
        <el-button @click="recordDetailVisible = false">取消</el-button>
        <el-button
          type="primary"
          :loading="loading"
          @click="handleCreateAction"
        >
          提交
        </el-button>
      </div>
    </template>

    <div v-loading="loading" />
  </el-drawer>
</template>

<script setup lang="ts">
import { ContractInfo } from "@/api/contract";
import SelectSerialItem from "@/components/SelectSerialItem/index.vue";
import { postFormDataAction } from "@/utils/http";
import { ElMessage } from "element-plus";
import { reactive, ref } from "vue";

const emit = defineEmits(["handleFinish"]);

const url = {
  contract: "/api/financial_contract/list",
  import: "/api/sales_order/import"
};

const recordDetailVisible = ref(false);
const loading = ref(false);
const recordDetailForm = ref();
const uploadRef = ref();

const selectContract = ref<ContractInfo | null>(null);
const selectPlatform = ref("gk");
const sheetName = ref("");
const uploadFile = ref<File | null>(null);
const fileList = ref([]);

const formData = reactive({
  contract: {},
  platform: "gk",
  sheetName: "",
  file: null
});

const platform = [
  { label: "广垦商城", value: "gk" },
  { label: "唯品会", value: "vip" }
];

const show = async () => {
  loading.value = true;
  recordDetailVisible.value = true;

  // 重置表单
  selectContract.value = null;
  selectPlatform.value = "gk";
  sheetName.value = "";
  uploadFile.value = null;
  fileList.value = [];

  loading.value = false;
};

const handleFileChange = (file: any) => {
  uploadFile.value = file.raw;
  fileList.value = [file];
};

const handleCreateAction = async () => {
  if (!selectContract.value?.id) {
    ElMessage.error("请选择关联合同");
    return;
  }

  if (!uploadFile.value) {
    ElMessage.error("请选择要上传的文件");
    return;
  }

  try {
    loading.value = true;

    const formData = new FormData();
    formData.append("file", uploadFile.value);
    formData.append("contract_id", selectContract.value?.id);
    formData.append("contract_name", selectContract.value?.name);
    formData.append("platform", selectPlatform.value);
    formData.append("sheet_name", sheetName.value);

    const response = await postFormDataAction(url.import, formData);

    if (response.code === 200) {
      ElMessage.success("文件导入成功");
      recordDetailVisible.value = false;
      emit("handleFinish");
    } else {
      ElMessage.error(response.msg || "导入失败");
    }
  } catch (error) {
    console.error("导入失败:", error);
    ElMessage.error("导入失败，请重试");
  } finally {
    loading.value = false;
  }
};

defineExpose({
  show
});
</script>

<style scoped>
.el-upload__tip {
  color: #606266;
  font-size: 12px;
  margin-top: 7px;
}
</style>
