<template>
  <el-drawer
    v-model="recordDetailVisible"
    title="导入记录详情"
    size="80%"
    :destroy-on-close="true"
    direction="rtl"
    :close-on-click-modal="false"
  >
    <div v-if="recordItem" class="record-detail">
      <el-descriptions title="基本信息" :column="3" border>
        <el-descriptions-item label="合同名称">
          {{ recordItem.contract_name || '-' }}
        </el-descriptions-item>
        <el-descriptions-item label="数据来源">
          {{ recordItem.source || '-' }}
        </el-descriptions-item>
        <el-descriptions-item label="文件名">
          {{ recordItem.file_name || '-' }}
        </el-descriptions-item>
        <el-descriptions-item label="总数量">
          {{ recordItem.total_count || 0 }}
        </el-descriptions-item>
        <el-descriptions-item label="新增数量">
          {{ recordItem.new_count || 0 }}
        </el-descriptions-item>
        <el-descriptions-item label="更新数量">
          {{ recordItem.update_count || 0 }}
        </el-descriptions-item>
        <el-descriptions-item label="无变化数量">
          {{ recordItem.no_change_count || 0 }}
        </el-descriptions-item>
        <el-descriptions-item label="成功数量">
          {{ recordItem.success_count || 0 }}
        </el-descriptions-item>
        <el-descriptions-item label="失败数量">
          {{ recordItem.failed_count || 0 }}
        </el-descriptions-item>
      </el-descriptions>

      <div class="mt-4">
        <h3 class="mb-3">关联销售订单</h3>
        <div v-if="salesOrderList && salesOrderList.length > 0">
          <el-table
            :data="salesOrderList"
            style="width: 100%"
            border
            stripe
            :max-height="500"
          >
            <el-table-column
              prop="platform_order_serial"
              label="平台订单号"
              min-width="150"
            />
            <el-table-column
              prop="platform_name"
              label="平台名称"
              min-width="120"
            />
            <el-table-column
              prop="total_payment"
              label="总金额"
              min-width="120"
              align="right"
            >
              <template #default="{ row }">
                ¥{{ row.total_payment }}
              </template>
            </el-table-column>
            <el-table-column prop="status" label="状态" min-width="100" align="center">
              <template #default="{ row }">
                <el-tag
                  :type="
                    row.status === '已完成'
                      ? 'success'
                      : row.status === '配送中'
                      ? 'primary'
                      : 'warning'
                  "
                >
                  {{ row.status }}
                </el-tag>
              </template>
            </el-table-column>
          </el-table>
          
          <div class="mt-4 text-right">
            <el-pagination
              v-model:current-page="pagination.currentPage"
              v-model:page-size="pagination.pageSize"
              :total="pagination.total"
              :page-sizes="[10, 20, 50, 100]"
              layout="total, sizes, prev, pager, next, jumper"
              @size-change="onSizeChange"
              @current-change="onCurrentChange"
            />
          </div>
        </div>
        <el-empty v-else description="暂无关联的销售订单" />
      </div>
    </div>
    <el-empty v-else description="暂无数据" />
    
    <template #footer>
      <div style="flex: auto">
        <el-button @click="recordDetailVisible = false">关闭</el-button>
      </div>
    </template>
    
    <div v-loading="loading" />
  </el-drawer>
</template>

<script setup lang="ts">
import { getImportRecord } from "@/api/importRecord";
import { getSalesOrderList, type SalesOrderInfo } from "@/api/salesOrder";
import { ElMessage } from "element-plus";
import { ref, reactive } from "vue";

const emit = defineEmits(["handleFinish"]);

const recordDetailVisible = ref(false);
const loading = ref(false);
const recordItem = ref<any>(null);
const salesOrderList = ref<SalesOrderInfo[]>([]);

// 分页配置
const pagination = reactive({
  total: 0,
  pageSize: 10,
  currentPage: 1,
  background: true,
  pageSizes: [10, 20, 50, 100]
});

const show = async (row: any) => {
  if (!row || !row.id) {
    ElMessage.error("记录数据不存在或缺少ID");
    return;
  }

  loading.value = true;
  recordDetailVisible.value = true;

  try {
    await getRecordDetail(row.id);
  } catch (error) {
    console.error("获取记录详情失败:", error);
  } finally {
    loading.value = false;
  }
};

const getRecordDetail = async (id: string) => {
  try {
    const response = await getImportRecord(id);
    if (response.code === 200) {
      recordItem.value = response.data;
      // 获取到recordItem后，调用getRecordOrderList
      await getRecordOrderList();
    } else {
      ElMessage.error(response.msg || "获取记录详情失败");
    }
  } catch (error) {
    console.error("获取记录详情失败:", error);
    ElMessage.error("获取记录详情失败");
  }
};

const getRecordOrderList = async () => {
  if (!recordItem.value || !recordItem.value.serial) {
    return;
  }

  loading.value = true;
  try {
    const params = {
      options: {
        order_by: "created_at",
        desc: true
      },
      page: {
        page: pagination.currentPage,
        limit: pagination.pageSize
      },
      params: [
        {
          var: "import_record",
          val: recordItem.value.serial
        }
      ]
    };

    const response = await getSalesOrderList(params);
    if (response.code === 200) {
      salesOrderList.value = response.data.data;
      pagination.total = response.data.total;
    } else {
      ElMessage.error(response.msg || "获取销售订单列表失败");
    }
  } catch (error) {
    console.error("获取销售订单列表失败:", error);
    ElMessage.error("获取销售订单列表失败");
    salesOrderList.value = [];
  } finally {
    loading.value = false;
  }
};

// 分页相关
const onSizeChange = (size: number) => {
  pagination.pageSize = size;
  getRecordOrderList();
};

const onCurrentChange = (current: number) => {
  pagination.currentPage = current;
  getRecordOrderList();
};

defineExpose({
  show
});
</script>

<style scoped>
.record-detail {
  padding: 0 20px;
}

.mt-4 {
  margin-top: 16px;
}

.mb-3 {
  margin-bottom: 12px;
}

.text-right {
  text-align: right;
}
</style>
