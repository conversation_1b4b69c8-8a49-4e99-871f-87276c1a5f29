<template>
  <div class="main">
    <!-- 搜索表单 -->
    <el-form
      ref="formRef"
      :inline="true"
      :model="searchForm"
      class="search-form bg-bg_color w-full pl-8 pt-[12px] overflow-auto"
    >
      <el-form-item label="导入类型：" prop="import_type">
        <el-select
          v-model="searchForm.import_type"
          placeholder="请选择导入类型"
          clearable
          class="w-[180px]!"
        >
          <el-option
            v-for="item in importTypeDict"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="导入状态：" prop="status">
        <el-select
          v-model="searchForm.status"
          placeholder="请选择状态"
          clearable
          class="w-[180px]!"
        >
          <el-option
            v-for="item in importStatusDict"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="文件名：" prop="file_name">
        <el-input
          v-model="searchForm.file_name"
          placeholder="请输入文件名"
          clearable
          class="w-[180px]!"
        />
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          :icon="useRenderIcon('ep:search')"
          :loading="loading"
          @click="onSearch"
        >
          搜索
        </el-button>
        <el-button
          :icon="useRenderIcon('ep:refresh')"
          @click="resetForm(formRef)"
        >
          重置
        </el-button>
      </el-form-item>
    </el-form>

    <!-- 表格 -->
    <el-card class="box-card" shadow="never" style="margin-top: 16px">
      <template #header>
        <div class="flex justify-between items-center">
          <span class="font-medium">导入记录列表</span>
          <el-button
            type="primary"
            :icon="useRenderIcon('ri:upload-line')"
            @click="handleImport"
          >
            导入订单
          </el-button>
        </div>
      </template>

      <pure-table
        ref="tableRef"
        row-key="id"
        align-whole="center"
        :show-overflow-tooltip="false"
        :loading="loading"
        :data="dataList"
        :columns="columns"
        :pagination="pagination"
        @page-size-change="onSizeChange"
        @page-current-change="onCurrentChange"
      >
        <template #import_type="{ row }">
          <el-tag
            :color="getImportTypeColor(row.import_type)"
            effect="dark"
            round
          >
            {{ getImportTypeText(row.import_type) }}
          </el-tag>
        </template>
        <template #status="{ row }">
          <el-tag :color="getImportStatusColor(row.status)" effect="dark" round>
            {{ getImportStatusText(row.status) }}
          </el-tag>
        </template>
        <template #operation="{ row }">
          <el-button link type="primary" size="small" @click="handleView(row)">
            查看详情
          </el-button>
          <el-button
            v-if="row.status === 'failed' || row.status === 'partial'"
            link
            type="warning"
            size="small"
            @click="handleDownloadError(row)"
          >
            下载错误
          </el-button>
          <el-popconfirm
            :title="`是否确认删除文件名为${row.file_name}的这条记录`"
            @confirm="handleDelete(row)"
          >
            <template #reference>
              <el-button link type="danger" size="small"> 删除 </el-button>
            </template>
          </el-popconfirm>
        </template>
      </pure-table>
    </el-card>

    <!-- 导入组件 -->
    <RecordImport ref="recordImportRef" @handleFinish="handleFinish" />

    <!-- 详情组件 -->
    <RecordDetail ref="recordDetailRef" @handleFinish="handleFinish" />
  </div>
</template>

<script setup lang="ts">
import {
  deleteImportRecord,
  getImportRecordList,
  getImportStatusColor,
  getImportStatusText,
  getImportTypeColor,
  getImportTypeText,
  importStatusDict,
  importTypeDict,
  type ImportRecordInfo,
  type ImportRecordListParams
} from "@/api/importRecord";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import { formatDateTime } from "@/utils/formatTime";
import { PureTable } from "@pureadmin/table";
import "@pureadmin/table/dist/style.css";
import { ElMessage, type FormInstance } from "element-plus";
import { computed, onMounted, reactive, ref } from "vue";
import RecordDetail from "./modules/recordDetail.vue";
import RecordImport from "./modules/recordImport.vue";

defineOptions({
  name: "ImportRecord"
});

// 响应式数据
const loading = ref(false);
const dataList = ref<ImportRecordInfo[]>([]);
const tableRef = ref();
const formRef = ref<FormInstance>();
const recordImportRef = ref();
const recordDetailRef = ref();

// 搜索表单
const searchForm = reactive({
  import_type: "",
  status: "",
  file_name: ""
});

// 分页配置
const pagination = reactive({
  total: 0,
  pageSize: 10,
  currentPage: 1,
  background: true,
  pageSizes: [10, 20, 50, 100]
});

// 处理完成回调
const handleFinish = () => {
  getImportRecordListData();
};

// 表格列配置
const columns = computed(() => [
  {
    label: "文件名",
    prop: "file_name",
    minWidth: 150
  },
  {
    label: "导入类型",
    prop: "import_type",
    minWidth: 100,
    slot: "import_type"
  },
  {
    label: "总数量",
    prop: "total_count",
    minWidth: 80
  },
  {
    label: "成功数量",
    prop: "success_count",
    minWidth: 80
  },
  {
    label: "失败数量",
    prop: "failed_count",
    minWidth: 80
  },
  {
    label: "导入状态",
    prop: "status",
    minWidth: 100,
    slot: "status"
  },
  {
    label: "创建时间",
    prop: "created_at",
    minWidth: 150,
    formatter: ({ created_at }) => formatDateTime(created_at)
  },
  {
    label: "创建人",
    prop: "created_by",
    minWidth: 100
  },
  {
    label: "操作",
    fixed: "right",
    width: 200,
    slot: "operation"
  }
]);

// 获取导入记录列表
const getImportRecordListData = async () => {
  loading.value = true;
  try {
    const params: ImportRecordListParams = {
      options: {
        order_by: "created_at",
        desc: true
      },
      page: {
        page: pagination.currentPage,
        limit: pagination.pageSize
      }
    };

    // 添加搜索参数
    if (searchForm.import_type || searchForm.status || searchForm.file_name) {
      params.params = [];
      if (searchForm.import_type) {
        params.params.push({
          var: "import_type",
          val: searchForm.import_type
        });
      }
      if (searchForm.status) {
        params.params.push({
          var: "status",
          val: searchForm.status
        });
      }
      if (searchForm.file_name) {
        params.params.push({
          var: "file_name",
          val: searchForm.file_name
        });
      }
    }

    const response = await getImportRecordList(params);
    if (response.code === 200) {
      dataList.value = response.data.data;
      pagination.total = response.data.total;
    } else {
      ElMessage.error(response.msg || "获取导入记录列表失败");
    }
  } catch (error) {
    console.error("获取导入记录列表失败:", error);
    ElMessage.error("获取导入记录列表失败");
  } finally {
    loading.value = false;
  }
};

// 搜索
const onSearch = () => {
  pagination.currentPage = 1;
  getImportRecordListData();
};

// 重置表单
const resetForm = (formEl: FormInstance | undefined) => {
  if (!formEl) return;
  formEl.resetFields();
  onSearch();
};

// 分页相关
const onSizeChange = (size: number) => {
  pagination.pageSize = size;
  getImportRecordListData();
};

const onCurrentChange = (current: number) => {
  pagination.currentPage = current;
  getImportRecordListData();
};

// 操作方法
const handleImport = () => {
  recordImportRef.value?.show();
};

const handleView = (row: ImportRecordInfo) => {
  recordDetailRef.value?.show(row);
};

const handleDownloadError = (row: ImportRecordInfo) => {
  ElMessage.info(`下载错误文件: ${row.file_name}`);
  // TODO: 实现下载错误文件功能
};

const handleDelete = async (row: ImportRecordInfo) => {
  try {
    const response = await deleteImportRecord(row.id);
    if (response.code === 200) {
      ElMessage.success("删除成功");
      getImportRecordListData();
    } else {
      ElMessage.error(response.msg || "删除失败");
    }
  } catch (error) {
    console.error("删除导入记录失败:", error);
    ElMessage.error("删除失败");
  }
};

// 初始化
onMounted(() => {
  getImportRecordListData();
});
</script>

<style scoped>
.main {
  margin: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.text-right {
  text-align: right;
}
</style>
