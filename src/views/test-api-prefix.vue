<template>
  <div class="test-api-prefix">
    <el-card>
      <template #header>
        <span>API前缀测试页面</span>
      </template>
      
      <div class="config-info">
        <h3>当前环境配置：</h3>
        <p><strong>环境模式：</strong>{{ mode }}</p>
        <p><strong>VITE_API_PROXY_PREFIX：</strong>{{ apiPrefix || '(未设置)' }}</p>
        <p><strong>是否为开发环境：</strong>{{ isDev ? '是' : '否' }}</p>
      </div>
      
      <div class="test-urls">
        <h3>测试URL构建：</h3>
        <div class="url-item">
          <span class="label">原始路径：</span>
          <code>/api/user/list</code>
        </div>
        <div class="url-item">
          <span class="label">实际请求URL：</span>
          <code>{{ testApiUrl }}</code>
        </div>
      </div>
      
      <div class="test-request">
        <h3>测试请求：</h3>
        <el-button @click="testRequest" :loading="loading">
          发送测试请求到 /api/user/current
        </el-button>
        <div v-if="requestResult" class="request-result">
          <h4>请求结果：</h4>
          <pre>{{ requestResult }}</pre>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { http } from '@/utils/http';

const loading = ref(false);
const requestResult = ref('');

// 获取环境信息
const mode = import.meta.env.MODE;
const apiPrefix = import.meta.env.VITE_API_PROXY_PREFIX;
const isDev = import.meta.env.DEV;

// 计算测试URL
const testApiUrl = computed(() => {
  const prefix = apiPrefix || '';
  return prefix ? `${prefix}/api/user/list` : '/api/user/list';
});

// 测试请求
const testRequest = async () => {
  loading.value = true;
  try {
    const response = await http.post('/api/user/current');
    requestResult.value = JSON.stringify(response, null, 2);
  } catch (error) {
    requestResult.value = `请求失败: ${error}`;
  } finally {
    loading.value = false;
  }
};
</script>

<style scoped>
.test-api-prefix {
  padding: 20px;
}

.config-info, .test-urls, .test-request {
  margin-bottom: 20px;
}

.url-item {
  margin: 10px 0;
  display: flex;
  align-items: center;
}

.label {
  min-width: 120px;
  font-weight: bold;
}

code {
  background: #f5f5f5;
  padding: 4px 8px;
  border-radius: 4px;
  font-family: 'Courier New', monospace;
}

.request-result {
  margin-top: 15px;
  padding: 15px;
  background: #f9f9f9;
  border-radius: 4px;
}

pre {
  margin: 0;
  white-space: pre-wrap;
  word-wrap: break-word;
}
</style>
