import type {
  CategoryCreateRequest,
  CategoryInfo,
  CategoryUpdateRequest
} from "@/api/category";
import { createCategory, updateCategory } from "@/api/category";
import { addDrawer } from "@/components/ReDrawer";
import { message } from "@/utils/message";
import { cloneDeep } from "@pureadmin/utils";
import CategoryForm, { type CategoryFormProps } from "./CategoryForm.vue";

export interface UseCategoryFormDrawerOptions {
  onSuccess?: () => void;
  parentName?: string;
  parentId?: string;
}

/**
 * 打开分类表单抽屉
 * @param categoryData 分类数据（编辑时传入）
 * @param options 配置选项
 */
export function openCategoryFormDrawer(
  categoryData?: Partial<CategoryInfo>,
  options: UseCategoryFormDrawerOptions = {}
) {
  const { onSuccess, parentName = "", parentId = "" } = options;
  const isEdit = !!categoryData?.id;

  // 准备表单数据
  const formData = {
    id: categoryData?.id || "",
    name: categoryData?.name || "",
    description: categoryData?.description || "",
    parent: categoryData?.parent || parentId,
    category_type: categoryData?.category_type || "",
    order: categoryData?.order || 1
  };

  addDrawer({
    title: isEdit ? "编辑分类" : "新增分类",
    size: "500px",
    closeOnClickModal: false,
    contentRenderer: () => CategoryForm,
    props: {
      formData: cloneDeep(formData),
      parentName
    },
    beforeSure: async (done, { options }) => {
      const { formData: currentFormData } = options.props as CategoryFormProps;

      try {
        if (isEdit) {
          // 编辑时确保有必需的字段
          const updateData: CategoryUpdateRequest = {
            id: currentFormData.id!,
            name: currentFormData.name,
            description: currentFormData.description,
            parent: currentFormData.parent,
            category_type: currentFormData.category_type,
            order: currentFormData.order
          };
          await updateCategory(updateData);
          message("分类更新成功", { type: "success" });
        } else {
          // 新增时确保有必需的字段
          const createData: CategoryCreateRequest = {
            name: currentFormData.name!,
            description: currentFormData.description,
            parent: currentFormData.parent,
            category_type: currentFormData.category_type!,
            order: currentFormData.order!
          };
          // 如果有父级分类ID，需要设置parent字段
          if (parentId) {
            createData.parent = parentId;
          }
          await createCategory(createData);
          message("分类创建成功", { type: "success" });
        }

        onSuccess?.();
        done(); // 关闭抽屉
      } catch (error) {
        console.error("提交失败:", error);
        message("操作失败，请重试", { type: "error" });
        // 不调用 done()，保持抽屉打开
      }
    }
  });
}

/**
 * 打开新增分类抽屉
 */
export function openAddCategoryDrawer(
  options: UseCategoryFormDrawerOptions = {}
) {
  return openCategoryFormDrawer(undefined, options);
}

/**
 * 打开编辑分类抽屉
 */
export function openEditCategoryDrawer(
  categoryData: CategoryInfo,
  options: UseCategoryFormDrawerOptions = {}
) {
  return openCategoryFormDrawer(categoryData, options);
}
