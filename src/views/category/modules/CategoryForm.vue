<script setup lang="ts">
import type { CategoryInfo } from "@/api/category";
import { reactive, ref, watch } from "vue";

// 声明 props 类型
export interface CategoryFormProps {
  formData: Partial<CategoryInfo>;
  parentName?: string;
}

// 声明 props 默认值
const props = withDefaults(defineProps<CategoryFormProps>(), {
  parentName: ""
});

// 创建本地响应式表单数据
const localFormData = reactive({
  id: "",
  name: "",
  description: "",
  parent: "",
  category_type: "",
  order: 1 // 设置默认值为1
});

// 表单引用
const formRef = ref();

// 表单验证规则
const rules = {
  name: [{ required: true, message: "请输入分类名称", trigger: "blur" }],
  category_type: [
    { required: true, message: "请输入分类类型", trigger: "blur" }
  ],
  order: [{ required: true, message: "请输入排序", trigger: "blur" }]
};

// 监听props变化，同步到本地数据
watch(
  () => props.formData,
  newData => {
    Object.assign(localFormData, {
      id: newData.id || "",
      name: newData.name || "",
      description: newData.description || "",
      parent: newData.parent || "",
      category_type: newData.category_type || "",
      order: newData.order || 1
    });
  },
  { immediate: true, deep: true }
);

// 监听本地数据变化，同步回props（通过emit或其他方式）
watch(
  localFormData,
  newData => {
    // 将本地数据同步回父组件的formData
    Object.assign(props.formData, newData);
  },
  { deep: true }
);

// 验证表单
const validateForm = () => {
  return new Promise(resolve => {
    formRef.value?.validate((valid: boolean) => {
      resolve(valid);
    });
  });
};

// 暴露验证方法给父组件
defineExpose({
  validateForm
});
</script>

<template>
  <div class="category-form">
    <el-form
      ref="formRef"
      :model="localFormData"
      :rules="rules"
      label-width="100px"
      label-position="left"
    >
      <el-form-item label="分类名称" prop="name">
        <el-input
          v-model="localFormData.name"
          placeholder="请输入分类名称"
          clearable
        />
      </el-form-item>

      <el-form-item label="分类类型" prop="category_type">
        <el-input
          v-model="localFormData.category_type"
          placeholder="请输入分类类型"
          clearable
        />
      </el-form-item>

      <el-form-item label="上级分类">
        <el-input
          :value="parentName || '顶级分类'"
          disabled
          placeholder="上级分类"
        />
      </el-form-item>

      <el-form-item label="排序" prop="order">
        <el-input-number
          v-model="localFormData.order"
          :min="1"
          :max="9999"
          placeholder="请输入排序"
          style="width: 100%"
        />
      </el-form-item>

      <el-form-item label="描述">
        <el-input
          v-model="localFormData.description"
          type="textarea"
          :rows="3"
          placeholder="请输入描述（可选）"
          maxlength="200"
          show-word-limit
        />
      </el-form-item>
    </el-form>
  </div>
</template>

<style scoped>
.category-form {
  padding: 20px;
}
</style>
