<template>
  <div class="main">
    <el-card shadow="never">
      <template #header>
        <div class="card-header">
          <span class="font-medium">分类管理</span>
        </div>
      </template>

      <div class="mb-4">
        <el-button type="primary" @click="handleAdd">
          <template #icon>
            <IconifyIconOnline icon="ep:plus" />
          </template>
          新增分类
        </el-button>
        <el-button @click="getCategoryListData">
          <template #icon>
            <IconifyIconOnline icon="ep:refresh" />
          </template>
          刷新
        </el-button>
      </div>

      <el-table
        ref="tableRef"
        v-loading="loading"
        :data="tableData"
        style="width: 100%"
        row-key="id"
        :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
        :default-expand-all="false"
      >
        <el-table-column prop="name" label="分类名称" width="200" />
        <el-table-column prop="category_type" label="分类类型" width="150" />
        <el-table-column prop="order" label="排序" width="80" />
        <el-table-column prop="description" label="描述" />
        <el-table-column label="操作" width="280">
          <template #default="{ row }">
            <el-button-group>
              <el-button size="small" @click="handleAddChild(row)"
                >子分类</el-button
              >
              <el-button size="small" type="primary" @click="handleEdit(row)"
                >编辑</el-button
              >
              <el-button size="small" type="danger" @click="handleDelete(row)"
                >删除</el-button
              >
            </el-button-group>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import {
  deleteCategory,
  getCategoryTree,
  type CategoryInfo
} from "@/api/category";
import { message } from "@/utils/message";
import { onMounted, ref } from "vue";
import { openCategoryFormDrawer } from "./modules/useCategoryFormDrawer";

defineOptions({
  name: "CategoryList"
});

const tableRef = ref();
const loading = ref(false);
const tableData = ref<CategoryInfo[]>([]);

// 获取分类树形数据
const getCategoryListData = async () => {
  try {
    loading.value = true;
    const response = await getCategoryTree();
    if (response.code === 200) {
      tableData.value = response.data.data || [];
    } else {
      message(response.msg || "获取分类数据失败", { type: "error" });
    }
  } catch (error) {
    console.error("获取分类数据失败:", error);
    message("获取分类数据失败", { type: "error" });
  } finally {
    loading.value = false;
  }
};

// 处理新增分类
const handleAdd = () => {
  openCategoryFormDrawer(undefined, {
    onSuccess: () => {
      getCategoryListData();
    }
  });
};

// 处理添加子分类
const handleAddChild = (row: CategoryInfo) => {
  openCategoryFormDrawer(undefined, {
    parentName: row.name,
    parentId: row.id,
    onSuccess: () => {
      getCategoryListData();
    }
  });
};

// 处理编辑分类
const handleEdit = (row: CategoryInfo) => {
  openCategoryFormDrawer(row, {
    onSuccess: () => {
      getCategoryListData();
    }
  });
};

// 处理删除分类
const handleDelete = async (row: CategoryInfo) => {
  try {
    const response = await deleteCategory(row.id);
    if (response.code === 200) {
      message("删除分类成功", { type: "success" });
      getCategoryListData();
    } else {
      message(response.msg || "删除分类失败", { type: "error" });
    }
  } catch (error) {
    console.error("删除分类失败:", error);
    message("删除分类失败", { type: "error" });
  }
};

// 展开所有节点
const handleExpandAll = () => {
  if (tableRef.value) {
    tableRef.value.store.states.defaultExpandAll.value = true;
    tableRef.value.store.states.expandRows.value = [...tableData.value];
  }
};

// 收起所有节点
const handleCollapseAll = () => {
  if (tableRef.value) {
    tableRef.value.store.states.defaultExpandAll.value = false;
    tableRef.value.store.states.expandRows.value = [];
  }
};

// 页面加载时获取数据
onMounted(() => {
  getCategoryListData();
});
</script>

<style scoped>
.main {
  margin: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>
